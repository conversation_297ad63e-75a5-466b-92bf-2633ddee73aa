workflow:
  rules:
    # merge request pipeline
    - if: $CI_MERGE_REQUEST_IID
    # allow manual run branch pipeline on web for develop & master branch
    - if: '$CI_PIPELINE_SOURCE == "web" && ($CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == "master")'
    # note that schedule is a branch pipeline
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
      variables:
        IMAGE_VERSION: "qa" #version of image to use for scheduled pipeline jobs
    # block version bump changes from triggering branch pipeline
    - if: '($CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == "master") && $CI_COMMIT_MESSAGE =~ /version updated to/'
      changes:
        - $INFO_XML_PATH
      when: never
    # branch pipeline for develop and master only
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == "master"'

# Repeated rulesets
.run_all_except_merge_train_and_schedule:
  rules:
    - if: $CI_MERGE_REQUEST_EVENT_TYPE == 'merge_train'
      when: never
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never

.run_only_manual_except_master:
  rules:
    - !reference [ .run_all_except_merge_train_and_schedule, rules ]
    - if: $CI_COMMIT_REF_SLUG != "master"
      when: manual
    - when: never


#include:
#  - local: ci-config/templates/base_template.gitlab-ci.yml
include:
  - project: 'devops/ci-templates'
    ref: master
    file:
      - '/templates/Dotnet/Dotnet.gitlab-ci.yml'
      - '/templates/Docker/Kaniko.gitlab-ci.yml'
      - '/templates/Git/Git_Push.gitlab-ci.yml'
      - '/templates/Git/Git_Clone.gitlab-ci.yml'
      - '/templates/Bash/Bash.gitlab-ci.yml'
      - '/templates/Scala/Sbt.gitlab-ci.yml'
      - '/templates/Scala/Sbt_Docker.gitlab-ci.yml'
      - '/templates/Docker/Dind.gitlab-ci.yml'
      - '/templates/Devstack/Devstack.gitlab-ci.yml'
      - '/templates/Security/Provenance.gitlab-ci.yml'

  - project: 'bpf-velocity/ci-templates'
    ref: master
    file:
      - 'templates/bpf-release-note.gitlab-ci.yml'

  - project: "data-experience/gpt-code-review"
    ref: main
    file:
      - "/ci-template/gpt-code-review.gitlab-ci.yml"

  - project: 'agoda-e2e/testing-ci-templates'
    ref: main
    file:
      - '/templates/coverage/codecov.gitlab-ci.yml'

stages:
  - Prepare Docker
  - Test
  - Utility
  - Release

docker_imaging:test:
  stage: Prepare Docker
  extends:
    - .docker_dind
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - when: on_success
  variables:
    DOCKERFILE: "docker/Dockerfile"
    HARBOR_IMAGE: "${PROJECT_DOCKER_SUBSPACE}/${PROJECT_GIT_REPO}"
    # Gitlab CI struggles to find java with older docker versions, so this forces it to 23
    DIND_IMAGE: "library/sbtdocker-azulzulu:23.0.0-dind-openjdk8-sbt-1.5.4"
  tags:
    - m1.xlarge
  before_script:
    - !reference [.docker_dind, before_script]
  script:
    - IMAGE_VERSION=PL-$CI_PIPELINE_ID
    - echo "IMAGE_NAME=${HARBOR_IMAGE}" > variables.env
    - echo "IMAGE_VERSION=$IMAGE_VERSION" >> variables.env
    - echo "IMAGE_VERSION in docker_imaging:test = $IMAGE_VERSION"
    - export HARBOR_TAG="$IMAGE_VERSION"
    - FULL_IMAGE_NAME=${HARBOR_REGISTRY}/${HARBOR_IMAGE}:${IMAGE_VERSION}
    - docker build -t $FULL_IMAGE_NAME -f $DOCKERFILE .
    - docker push $FULL_IMAGE_NAME
    - SIGN_FUNC_FULL_IMAGE=$FULL_IMAGE_NAME
    - SIGN_FUNC_DOTENV_FILE="variables.env"
    - !reference [.sign_image_funcs, extract_remote_image_digest]
    - cat variables.env
  artifacts:
    reports:
      dotenv: variables.env

sign_image:test:
  stage: Prepare Docker
  extends: [ .sign_image ]
  needs: [ docker_imaging:test ]
  variables:
    IMAGE_NAME: ${IMAGE_NAME}
    IMAGE_TAG: ${IMAGE_VERSION}
    IMAGE_DIGEST: ${CONTAINER_DIGEST}
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - when: on_success

continuous:
  extends:
    - .continuous
  parallel:
    matrix:
      - SUB_PROJECT:
          - Common
          - Runner
          - Analytics
          - Email
          - Payment
          - Provisioning
          - PushBookingSummary
          - UPC

.continuous:
  stage: Test
  extends: [ .dotnet ]
  image: reg-hk.agodadev.io/library/dotnet/sdk:8.0
  needs: [ ]
  rules:
    - when: on_success
  variables:
    DOTNET_PROJECTS_PATH: "${DOTNET_PROJECTS_PATH}"
    BASE_NAMESPACE: "Agoda.EBE.Agents"
    DOTNET_PROJECT: "${BASE_NAMESPACE}.${SUB_PROJECT}"
    DOTNET_TEST_PROJECT: "${DOTNET_PROJECT}.UnitTest"
  script:
    - echo "Running tests for $DOTNET_PROJECT"
    - export PATH="$PATH:/root/.dotnet/tools"
    - |
      echo -e "\e[0Ksection_start:`date +%s`:compile[collapsed=true]\r\e[0KRestore and Build sln"
      dotnet build $DOTNET_TEST_PROJECT -p:RunAnalyzersDuringBuild=true -p:RunAnalyzers=true $NUGET_DEFAULT_PACKAGE_SOURCES > dotnet-build.log
      tail -n 10 dotnet-build.log
      echo -e "\e[0Ksection_end:`date +%s`:compile\r\e[0K"
    - dotnet test --no-build $DOTNET_TEST_PROJECT --collect:"XPlat Code Coverage" --logger:"junit;LogFilePath=..\artifacts\{assembly}-test-result.xml;MethodFormat=Class;FailureBodyFormat=Verbose" | tee dotnet-test-$DOTNET_TEST_PROJECT.log
  after_script:
    # Reduce class filename to relative path for project with no project reference (Agoda.EBE.Agents.Common)
    - COBERTURA_REPORT=$(find ./$DOTNET_TEST_PROJECT -name "*coverage.cobertura.xml" | head -n 1)
    - perl -pi.bak -e "s/(<class.*)(filename=\")((?!${BASE_NAMESPACE}).*)/\1\2${DOTNET_PROJECT}\/\3/" $COBERTURA_REPORT
    - perl -pi.bak -e "s/${DOTNET_PROJECT}\/<\/source>/<\/source>/" $COBERTURA_REPORT
  artifacts:
    when: always
    paths:
      - ./**/*test-result.xml
      - ./**/*coverage.cobertura.xml
      - ./coverage-report/**/*
      - dotnet-*.log
    reports:
      junit:
        - ./**/*test-result.xml

merge-continuous-tests-coverage:
  stage: Test
  extends: [ .dotnet ]
  image: reg-hk.agodadev.io/library/dotnet/sdk:8.0
  needs:
    - continuous
  rules:
    - when: always
  coverage: '/Line Coverage: \d+(?:\.\d+)?/'
  variables:
    COVERAGE_FOLDER: "coverage-report"
  script:
    - dotnet tool install -g dotnet-script
    - reportgenerator "-reports:**/*coverage.cobertura.xml" "-fileFilters:-**/Connected Services/**/*" -targetdir:$COVERAGE_FOLDER -reporttypes:Cobertura
    - reportgenerator "-reports:**/*coverage.cobertura.xml" "-fileFilters:-**/Connected Services/**/*" -targetdir:$COVERAGE_FOLDER -reporttypes:HtmlSummary
    - reportgenerator "-reports:**/*coverage.cobertura.xml" "-fileFilters:-**/Connected Services/**/*" -targetdir:$COVERAGE_FOLDER -reporttypes:JsonSummary
    - dotnet script PrintCoverage.csx $COVERAGE_FOLDER/Summary.json
  after_script:
    # Reduce class filename to relative path to allow coverage visualization in Gitlab
    - PWD_REGEXP=$(echo "${PWD}/" | sed 's/\//\\\//g')
    - perl -pi.bak -e "s/(<class.*)(filename=\")${PWD_REGEXP}(.*)/\1\2\3/" ./coverage-report/Cobertura.xml
  artifacts:
    paths:
      - ./coverage-report/Cobertura.xml
    reports:
      coverage_report:
        coverage_format: cobertura
        path: ./coverage-report/Cobertura.xml

code_coverage:publish:
  stage: Test
  extends: .codecov-publish
  needs:
    - merge-continuous-tests-coverage
  rules:
    - when: always
  allow_failure: true
  variables:
    REPORTS_PATH: "./coverage-report/Cobertura.xml"
    PULL_BASE_LATEST: "$CI_MERGE_REQUEST_TARGET_BRANCH_NAME"

release:QA:
  stage: Release
  needs: [ ]
  rules:
    - !reference [.run_all_except_merge_train_and_schedule, rules]
    # auto run on develop branch pipeline for latest image release
    - if: $CI_COMMIT_BRANCH && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
      when: on_success
    - when: manual
  variables:
    RELEASE_TYPE: qa
    TEMPLATE_DATA_VERSION_PREFIX: "IMAGE_TAG="
    TEMPLATE_DATA_VERSION_SUFFIX: ""
  trigger:
    include: ci-config/templates/release-qa-prelive.gitlab-ci.yml
    strategy: depend
  allow_failure: true

release:prod:
  stage: Release
  needs: [ ]
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_PIPELINE_SOURCE == 'web' && $CI_COMMIT_BRANCH == 'develop'
      when: manual
    - if: $CI_COMMIT_BRANCH == 'develop'
      when: manual
    - when: never
  variables:
    PARENT_PIPELINE_ID: $CI_PIPELINE_ID
    RELEASE_TYPE: release
  trigger:
    include: ci-config/templates/release-prod.gitlab-ci.yml
    strategy: depend

release:hotfix:
  stage: Release
  needs: [ ]
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_BRANCH == 'master'
      when: manual
    - if: $CI_PIPELINE_SOURCE == 'web' && $CI_COMMIT_BRANCH == 'master'
      when: manual
  variables:
    PARENT_PIPELINE_ID: $CI_PIPELINE_ID
    RELEASE_TYPE: hotfix
  trigger:
    include: ci-config/templates/release-prod.gitlab-ci.yml
    strategy: depend


#Still in Beta
GPT File Based Review:
  stage: Utility
  needs: []
  extends: .gpt-file-based-review
  variables:
    FF_UPDATE_MR_DESCRIPTION: "false"
