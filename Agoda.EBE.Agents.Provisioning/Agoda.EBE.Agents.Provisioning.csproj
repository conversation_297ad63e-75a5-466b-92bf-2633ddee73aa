﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>default</LangVersion>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Agoda.Abs.Phoenix.API.Client" Version="2.4.395" />
    <PackageReference Include="Agoda.BookingApi.GenClient" Version="6.549.0" />
    <PackageReference Include="Agoda.Builds.Metrics" Version="1.0.113" />
    <PackageReference Include="Agoda.Config.Consul" Version="5.2.140" />
    <PackageReference Include="Agoda.EBE.Structure" Version="2.208.0.112" />
    <PackageReference Include="Agoda.EBE.Framework" Version="1.1.0.2903" />
    <PackageReference Include="Agoda.EBE.Workflow.Client" Version="2.0.776" />
    <PackageReference Include="Agoda.Enigma.Client" Version="2.0.99" />
    <PackageReference Include="Agoda.Frameworks.DB" Version="7.0.87" />
    <PackageReference Include="Agoda.Payments.Upc.Client" Version="1.0.8657.28771" />
    <PackageReference Include="Agoda.Payments.Upc.Client.Generated" Version="1.0.8657.28771" />
    <PackageReference Include="Agoda.Payments.Upc.Integrate.Client" Version="1.0.8318.25625" />
    <PackageReference Include="Agoda.PayoutApi.Client.Generated" Version="0.82.3-RELEASE" />
    <PackageReference Include="Agoda.Secure.Structure" Version="1.344.0" />
    <PackageReference Include="Agoda.Upc.Structure" Version="2.0.7961.31976" />
    <PackageReference Include="Agoda.WhiteLabelApi.Client" Version="10.1364.0" />
    <PackageReference Include="Autofac" Version="5.2.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.FxCopAnalyzers" Version="3.3.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="3.1.3" />
    <PackageReference Include="NUnit" Version="3.12.0" />
    <PackageReference Include="Roslynator.Analyzers" Version="4.12.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="5.0.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Private.ServiceModel" Version="4.9.0" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="4.9.0" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.9.0" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.9.0" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.9.0" />
    <PackageReference Include="Agoda.ApplicationInsights.AspNetCore" Version="2.6.3" />
    <PackageReference Include="AgProtobuf.NET-Abs" Version="3.1101.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.11.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Agoda.EBE.Agents.Common\Agoda.EBE.Agents.Common.csproj">
      <Private>true</Private>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Configuration\FeatureSwitches\" />
    <Folder Include="Object\GovernmentCampaign\" />
  </ItemGroup>
</Project>
