using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Agents.Common.Util;
using NUnit.Framework;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{
    [TestFixture]
    public class DoRefundStateUtilTest
    {
        [Test]
        public void InitialState_ShouldWorkProperly()
        {
            var amountToRefund = 20;
            var actionResult = 1;

            var result = DoRefundStateUtil.InitialState(amountToRefund, actionResult);

            result.AmountToRefund.ShouldBe(amountToRefund);
            result.WorkflowActionResult.ShouldBe(actionResult);
            result.FinalActionResult.ShouldBe(actionResult);
            result.Remark.ShouldBeEmpty();
            result.ShouldProcessToNextStep.ShouldBeTrue();
        }

        [Test]
        [TestCase(true, true, true, true)]
        [TestCase(true, false, true, false)]
        [TestCase(false, true, false, false)]
        [TestCase(false, false, false, false)]
        public void UpdateStateWithExternalLoyaltyInfo_ShouldWorkProperly(bool extLoyaltyInfoExists,
            bool loyaltyRefundProcessedFeatureEnabled,
            bool expectedExternalLoyaltyInfoExists,
            bool expectedEnableLoyaltyRefundProcessedEmail)
        {
            var doRefundState = new DoRefundState();

            DoRefundStateUtil.UpdateStateWithExternalLoyaltyInfo(doRefundState, extLoyaltyInfoExists, loyaltyRefundProcessedFeatureEnabled);

            doRefundState.ExternalLoyaltyInfoExists.ShouldBe(expectedExternalLoyaltyInfoExists);
            doRefundState.EnableLoyaltyRefundProcessedEmail.ShouldBe(expectedEnableLoyaltyRefundProcessedEmail);
        }

        [Test]
        public void UpdateStateNotToProcessNextStep_ShouldWorkProperly()
        {
            var actionResult = 2;
            var remark = "Remark";

            var doRefundState = new DoRefundState();

            DoRefundStateUtil.UpdateStateNotToProcessNextStep(doRefundState, actionResult, remark);

            doRefundState.ShouldProcessToNextStep.ShouldBeFalse();
            doRefundState.WorkflowActionResult.ShouldBe(actionResult);
            doRefundState.Remark.ShouldBe(remark);
        }

        [Test]
        public void UpdateFinalActionResult_ShouldWorkProperly()
        {
            var actionResult = 2;

            var doRefundState = new DoRefundState();

            DoRefundStateUtil.UpdateFinalActionResult(doRefundState, actionResult);

            doRefundState.FinalActionResult.ShouldBe(actionResult);
        }

        [Test]
        public void UpdateDoRefundStateAmount_ShouldWorkProperly()
        {
            var doRefundState = new DoRefundState() { AmountToRefund = 100 };

            DoRefundStateUtil.UpdateDoRefundStateAmount(doRefundState, 20);

            doRefundState.AmountToRefund.ShouldBe(80);
        }
    }
}