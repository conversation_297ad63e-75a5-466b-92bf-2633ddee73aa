using System;
using System.Collections.Generic;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Util.Data;
using Agoda.EBE.Framework.Messaging;
using NSubstitute;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Common.UnitTest.Util.Data
{
    [TestFixture]
    public class MeasurementProviderTests
    {
        private IMeasurementProvider _measurementProvider;
        private IMessaging _mockMessaging;
        private IMeasurementMessageFactory _mockMeasurementMessageFactory;
        private IEBEMeasurementMessage _mockEBEMeasurementMessage;
        private IEBEExceptionMessage _mockEBEExceptionMessage;

        [SetUp]
        public void SetUp()
        {
            _mockMessaging = Substitute.For<IMessaging>();
            _mockMeasurementMessageFactory = Substitute.For<IMeasurementMessageFactory>();
            _mockEBEMeasurementMessage = Substitute.For<IEBEMeasurementMessage>();
            _mockEBEExceptionMessage = Substitute.For<IEBEExceptionMessage>();

            _mockMeasurementMessageFactory.CreateNewMeasurement().Returns(_mockEBEMeasurementMessage);
            _mockMessaging.MeasurementMessageFactory.Returns(_mockMeasurementMessageFactory);
            _mockMessaging.ExceptionMessage.Returns(_mockEBEExceptionMessage);

            _measurementProvider = new MeasurementProvider(_mockMessaging);
        }

        [Test]
        public void CallWithMeasurement_Should_Return_Result_When_Callback_Success()
        {
            var result = _measurementProvider.CallWithMeasurement(() => 1, Enum.Measurement.PiiClientMetric);
            Assert.AreEqual(1, result);
            
            _mockEBEMeasurementMessage.Received().BeginTrack();
            _mockEBEMeasurementMessage.Received().EndTrack(Enum.Measurement.PiiClientMetric);
        }

        [Test]
        public void CallWithMeasurement_Should_Throw_Exception_When_Callback_Fail()
        {
            try
            {
                _measurementProvider.CallWithMeasurement<int>(() => throw new Exception("Test Failure"),
                    Enum.Measurement.PiiClientMetric);
            }
            catch (Exception ex)
            {
                _mockEBEMeasurementMessage.Received().BeginTrack();
                _mockEBEExceptionMessage.Received().Send(Arg.Any<string>(), Arg.Any<Exception>(), LogLevel.FATAL);
                _mockEBEMeasurementMessage.Received().EndTrack(Enum.Measurement.PiiClientMetric, false);
            }
        }
    }
}