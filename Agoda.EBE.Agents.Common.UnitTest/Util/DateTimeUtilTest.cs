using System;
using Agoda.EBE.Agents.Common.Util;
using NUnit.Framework;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{
    [TestFixture]
    public class DateTimeUtilTest
    {
        private string dateTimeFormatWithoutSecond = "yyyyMMddHHmm";
        
        [Test]
        public void GetCurrentTimeWithTimeZone_DefaultTimeZone_ReturnCorrectTime()
        {
            var timeZone = TimeZoneInfo.Utc;
            var currentTime = DateTime.UtcNow.ToString(dateTimeFormatWithoutSecond);
            var resultWithoutSecond = DateTimeUtil.GetCurrentTimeWithTimeZone(timeZone).Remove(12);
            resultWithoutSecond.ShouldBe(currentTime);
        }
        
        [Test]
        public void GetCurrentTimeWithTimeZone_TokyoTimeZone_ReturnCorrectTime()
        {
            var tokyoTimeZone = DateTimeUtil.GetTokyoTimeZoneInfo();
            var currentTokyoTime = DateTime.UtcNow.AddHours(9).ToString(dateTimeFormatWithoutSecond);
            var resultWithoutSecond = DateTimeUtil.GetCurrentTimeWithTimeZone(tokyoTimeZone).Remove(12);
            resultWithoutSecond.ShouldBe(currentTokyoTime);
        }
    }
}