using Agoda.EBE.Agents.Common.Util;
using NUnit.Framework;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{

    [TestFixture]
    public class MaskingUtilTest
    {
        [Test]
        public void MaskPhoneNumber_TestMasking_ReturnsMaskedPhoneNumber()
        {
            string phoneNumber = "0999999999";

            var maskedPhoneNumber = MaskingUtil.MaskPhoneNumber(phoneNumber);
            maskedPhoneNumber.ShouldNotBeNull();
            maskedPhoneNumber.ShouldBe("xxxxxx9999");
        }
    }
}