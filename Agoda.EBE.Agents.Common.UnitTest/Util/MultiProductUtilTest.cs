using Agoda.EBE.Agents.Common.Util;
using NUnit.Framework;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{
    [TestFixture]
    public class MultiProductUtilTest
    {
        [Test]
        [TestCase(Enum.MultiProductType.None, false)]
        [TestCase(Enum.MultiProductType.Package, true)]
        [TestCase(Enum.MultiProductType.MixAndSave, true)]
        [TestCase(Enum.MultiProductType.HackerFare, true)]
        [TestCase(Enum.MultiProductType.SingleFlight, true)]
        [TestCase(Enum.MultiProductType.SingleProperty, true)]
        [TestCase(Enum.MultiProductType.FlightWithProtection, true)]
        [TestCase(Enum.MultiProductType.SingleVehicle, true)]
        [TestCase(Enum.MultiProductType.SingleProtection, true)]
        [TestCase(Enum.MultiProductType.MultiProperties, true)]
        public void IsMultiProductFlow_BasedOnInput_EvaluateCorrectResult(
            Enum.MultiProductType productType,
            bool expectedIsMultiProduct)
        {
            MultiProductUtil.IsMultiProductFlow(productType).ShouldBe(expectedIsMultiProduct);
        }
        
        [Test]
        [TestCase(Enum.MultiProductType.None, false)]
        [TestCase(Enum.MultiProductType.Package, true)]
        [TestCase(Enum.MultiProductType.MixAndSave, true)]
        [TestCase(Enum.MultiProductType.HackerFare, true)]
        [TestCase(Enum.MultiProductType.SingleFlight, true)]
        [TestCase(Enum.MultiProductType.SingleProperty, false)]
        [TestCase(Enum.MultiProductType.FlightWithProtection, true)]
        [TestCase(Enum.MultiProductType.SingleVehicle, true)]
        public void IsItineraryPaymentProduct_BasedOnInput_EvaluateCorrectResult(
            Enum.MultiProductType productType,
            bool expectedIsMultiProduct)
        {
            MultiProductUtil.IsItineraryPaymentProduct(productType).ShouldBe(expectedIsMultiProduct);
        }
    }
}