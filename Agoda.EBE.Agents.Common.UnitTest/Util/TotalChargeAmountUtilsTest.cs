using System.Collections.Generic;
using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Framework.Objects.MultiProductBooking;
using System;
using Agoda.EBE.Agents.Common.Util;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{
    [TestFixture]
    public class TotalChargeAmountUtilsTest
    {
        [Test]
        [TestCase("USD", "THB", true)]
        [TestCase("THB", "JPY", true)]
        [TestCase("JPY", "EUR", true)]
        [TestCase("JPY", "EUR", true)]
        [TestCase("USD", "USD", false)]
        [TestCase("THB", "THB", false)]
        [TestCase("JPY", "JPY", false)]
        [TestCase("EUR", "EUR", false)]
        [TestCase("JPY", null, false)]
        [TestCase("JPY", "    ", false)]
        [TestCase(null, null, false)] // edge
        [TestCase(null, "EUR", true)] // edge
        public void TestIsCurrencyDifferentBaseOnTestCases(string chargeCurrency, string succCurrency, bool expected)
        {
            Assert.AreEqual(expected, TotalChargeAmountUtils.IsCurrencyDifferent(chargeCurrency, succCurrency));
        }

        [Test]
        [TestCase(true)]
        [TestCase(false)]
        public void TestGetAmountByBookingResultsWithNullEbeBookingCharges(bool calculateDiscount)
        {
            List<EbeBookingCharges> items = null;
            Assert.AreEqual(null, TotalChargeAmountUtils.GetAmountByBookingResults(
                items, calculateDiscount
            ));
        }

        [Test]
        [TestCase(true)]
        [TestCase(false)]
        public void TestGetAmountByBookingResultsWithNoActiveEbeBookingCharges(bool calculateDiscount)
        {
            List<EbeBookingCharges> items = new List<EbeBookingCharges>()
            {
                new EbeBookingCharges
                {
                    BookingId = 10,
                    RecStatus = 0,
                    LocalCurrency = "USD",
                    SellingAmount = 10m,
                    ExchangeRateLocalToSupplier = 10m,
                    LocalSupplierAmount = 10m
                },

                new EbeBookingCharges
                {
                    BookingId = 20,
                    RecStatus = 0,
                    LocalCurrency = "USD",
                    SellingAmount = 20m,
                    ExchangeRateLocalToSupplier = 20m,
                    LocalSupplierAmount = 20m
                },

                new EbeBookingCharges
                {
                    BookingId = 30,
                    RecStatus = 0,
                    LocalCurrency = "USD",
                    SellingAmount = 30m,
                    ExchangeRateLocalToSupplier = 30m,
                    LocalSupplierAmount = 30m
                }
            };

            AmountByBookingResult expected = new AmountByBookingResult
            {
                TotalLocalCOGS = decimal.Zero,
                TotalChargesLocal = decimal.Zero
            };
            AmountByBookingResult actual = TotalChargeAmountUtils.GetAmountByBookingResults(
                items, calculateDiscount
            );

            Assert.AreEqual(expected.TotalLocalCOGS, actual.TotalLocalCOGS);
            Assert.AreEqual(expected.TotalChargesLocal, actual.TotalChargesLocal);
        }

        [Test]
        [TestCase(true)]
        [TestCase(false)]
        public void TestGetAmountByBookingResultsWithAllActiveEbeBookingCharges(bool calculateDiscount)
        {
            List<EbeBookingCharges> items = new List<EbeBookingCharges>()
            {
                new EbeBookingCharges
                {
                    BookingId = 10,
                    RecStatus = 1,
                    LocalCurrency = "USD",
                    SellingAmount = 10m,
                    ExchangeRateLocalToSupplier = 10m,
                    LocalSupplierAmount = 10m
                },

                new EbeBookingCharges
                {
                    BookingId = 20,
                    RecStatus = 1,
                    LocalCurrency = "USD",
                    SellingAmount = 20m,
                    ExchangeRateLocalToSupplier = 20m,
                    LocalSupplierAmount = 20m
                },

                new EbeBookingCharges
                {
                    BookingId = 30,
                    RecStatus = 1,
                    LocalCurrency = "USD",
                    SellingAmount = 30m,
                    ExchangeRateLocalToSupplier = 30m,
                    LocalSupplierAmount = 30m
                }
            };

            AmountByBookingResult expected = new AmountByBookingResult
            {
                TotalLocalCOGS = 10m + 20m + 30m,
                TotalChargesLocal = 10m * 10m + 20m * 20m + 30m * 30m
            };
            AmountByBookingResult actual = TotalChargeAmountUtils.GetAmountByBookingResults(
                items, calculateDiscount
            );

            Assert.AreEqual(expected.TotalLocalCOGS, actual.TotalLocalCOGS);
            Assert.AreEqual(expected.TotalChargesLocal, actual.TotalChargesLocal);
        }


        [Test]
        [TestCase(true, 207, 0, 0)]
        [TestCase(true, 208, 40, 1000)]
        [TestCase(false, 207, 40, 1000)]
        [TestCase(false, 208, 40, 1000)]
        public void TestGetAmountByBookingResultsBaseOnTestCases(
            bool calculateDiscount,
            int chargeTypeId,
            int totalLocalCOGS,
            int totalChargesLocal
        )
        {
            List<EbeBookingCharges> items = new List<EbeBookingCharges>()
            {
                new EbeBookingCharges
                {
                    BookingId = 10,
                    RecStatus = 1,
                    LocalCurrency = "USD",
                    SellingAmount = 10m,
                    ExchangeRateLocalToSupplier = 10m,
                    LocalSupplierAmount = 10m,
                    ChargeTypeId = chargeTypeId
                },

                new EbeBookingCharges
                {
                    BookingId = 20,
                    RecStatus = 0,
                    LocalCurrency = "USD",
                    SellingAmount = 20m,
                    ExchangeRateLocalToSupplier = 20m,
                    LocalSupplierAmount = 20m,
                    ChargeTypeId = chargeTypeId
                },

                new EbeBookingCharges
                {
                    BookingId = 30,
                    RecStatus = 1,
                    LocalCurrency = "USD",
                    SellingAmount = 30m,
                    ExchangeRateLocalToSupplier = 30m,
                    LocalSupplierAmount = 30m,
                    ChargeTypeId = chargeTypeId
                }
            };

            AmountByBookingResult expected = new AmountByBookingResult
            {
                TotalLocalCOGS = Convert.ToDecimal(totalLocalCOGS),
                TotalChargesLocal = Convert.ToDecimal(totalChargesLocal)
            };
            AmountByBookingResult actual = TotalChargeAmountUtils.GetAmountByBookingResults(
                items, calculateDiscount
            );

            Assert.AreEqual(expected.TotalLocalCOGS, actual.TotalLocalCOGS);
            Assert.AreEqual(expected.TotalChargesLocal, actual.TotalChargesLocal);
        }
        
        [Test] 
        [TestCase(true, 214.23, 237.22, 1.0, false, 1.0, 237.22, 237.22)]
        [TestCase(false, 214.23, 237.22, 1.0, false, 1.0, 214.23, 214.23)]
        [TestCase(true, 0.0, 0.0, 1.0, false, 1.0, 0.0, 0.0)]
        [TestCase(false, 60.0, 60.0, 1.0, true, 0.025, 60.0, 2400.0)]
        public void TestGetFutureBalanceAmount(
            bool isMerchantCommission,
            decimal totalLocalCOGS,
            decimal totalChargesLocal,
            decimal chargeExchangeRate,
            bool isDifferentCurrency,
            decimal succExchangeRate,
            decimal amountUsd,
            decimal amountLocal)
        {
            List<FinancialBreakdownByBookingResult> financialBreakdownListResult =
                new List<FinancialBreakdownByBookingResult>();
            FinancialBreakdownByBookingResult referenceSellInclusive = new FinancialBreakdownByBookingResult();
            referenceSellInclusive.TypeId = 1;
            referenceSellInclusive.ItemId = 41;
            referenceSellInclusive.Quantity = 1;
            referenceSellInclusive.LocalAmount = 237.22M;
            referenceSellInclusive.ExchangeRate = 1M;
            referenceSellInclusive.USDAmount = 237.22M;
            financialBreakdownListResult.Add(referenceSellInclusive);
            var actual = TotalChargeAmountUtils.GetFutureBalanceAmount(
                isMerchantCommission,
                financialBreakdownListResult,
                new AmountByBookingResult()
                {
                    TotalLocalCOGS = totalLocalCOGS,
                    TotalChargesLocal = totalChargesLocal
                },
                chargeExchangeRate,
                isDifferentCurrency,
                succExchangeRate);
            Assert.AreEqual(actual, (amountUsd, amountLocal));
        }
    }
}