using Agoda.EBE.Agents.Common.Util;
using NUnit.Framework;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{
    public class JapaOtaBookingUtilTest
    {
        [Test]
        [TestCase(1, 2, 29014, 2, "JPY", "JPY", (int)Enum.ExchangeRateOption.FixedFX, true)]
        [TestCase(1, 2, 29014, 2, "JPY", "JPY", (int)Enum.ExchangeRateOption.FloatingUpliftFX, false)]
        [TestCase(1, 2, 29014, 2, "KRW", "JPY", (int)Enum.ExchangeRateOption.FixedFX, false)]
        [TestCase(1, 1, 29014, 2,"JPY", "JPY", (int)Enum.ExchangeRateOption.FixedFX, false)]
        [TestCase(1, 2, 332, 2,"JPY", "JPY", (int)Enum.ExchangeRateOption.FixedFX, false)]
        [TestCase(1, 2, 29014, 1,"JPY", "JPY", (int)Enum.ExchangeRateOption.FixedFX, false)]
        [TestCase(2, 2, 29014, 2,"JPY", "JPY", (int)Enum.ExchangeRateOption.FixedFX, false)]
        public void Validate_IsAgodaOtaJaPaFixedExchangeRate(int whiteLabelId, 
            int paymentModel, 
            int dmcId, 
            int inventoryTypeId, 
            string paymentCurrency, 
            string supplierCurrency,
            int exchangeRateOption, 
            bool result)
        {
            JapaOtaBookingUtil.IsAgodaOtaJaPaFixedExchangeRate(whiteLabelId, 
                    paymentModel, 
                    dmcId, 
                    inventoryTypeId, 
                    paymentCurrency, 
                    supplierCurrency, 
                    exchangeRateOption)
                .ShouldBe(result);
        }
    }
}