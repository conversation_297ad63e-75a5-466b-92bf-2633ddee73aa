{"request": {"affiliateModel": 1, "userId": "25df88ea-74d2-4c80-81ae-73beefd09f59", "storefrontId": 3, "siteId": 1829983, "referralUrl": "https://gwpci.agoda.com/payment/", "platformId": 57, "trackingCookieId": "25df88ea-74d2-4c80-81ae-73beefd09f59", "sessionId": "dyvsjgrdllfj5iyx0ir3opmq", "userAgent": {"origin": "US", "osName": "iOS", "osVersion": "", "browserName": "Mobile Safari", "browserLanguage": "en-us", "browserVersion": "13", "browserSubVersion": "1", "browserBuildNumber": "", "deviceBrand": "Apple", "deviceModel": "iPhone", "deviceTypeId": 4, "isMobile": true, "isTouch": true, "additionalInfo": "{\"additionalDeviceInfo\":{\"userAgent\":\"Mozilla/5.0 (iPhone; CPU iPhone OS 13_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Mobile/15E148 Safari/604.1\"}}", "rawString": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Mobile/15E148 Safari/604.1"}, "clientIp": "************", "userContext": {"languageId": 1, "requestOrigin": "US", "currency": "usd", "nationalityId": 181, "experimentData": {"force": {}}}, "isNewsLetterOptIn": true, "isPassportRequired": false, "isNationalityRequired": false, "tmSessionId": null, "acceptHeader": null}, "customer": {"memberId": *********, "isUserLoggedIn": false}, "paymentInfo": {"method": 2, "paymentCurrency": "USD", "paymentAmount": 312.93, "paymentAmountUSD": 312.93, "accountingEntity": {"merchantOfRecord": 5632, "rateContract": 5632, "revenue": 5632, "argument": null}, "siteExchangeRate": 1, "destinationCurrency": null, "destinationExchangeRate": 0, "rateQuoteId": 0, "paymentOption": 0, "gateway": null, "displayPricebreakdown": {"value": {"type": 1, "amount": {"amount": 368.61, "currencyCode": "USD"}, "originalAmount": {"amount": 569.82, "currencyCode": "USD"}, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": [{"value": {"type": 2, "amount": {"amount": 312.93, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": [{"value": {"type": 4, "amount": {"amount": 312.93, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": [{"value": {"type": 5, "amount": {"amount": 312.93, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": {"amount": 52.15, "currencyCode": "USD"}, "productId": null}, "breakdowns": [{"value": {"type": 6, "amount": {"amount": 52.93, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "2020-09-22 00:00:00.000", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 6, "amount": {"amount": 52, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "2020-09-23 00:00:00.000", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 6, "amount": {"amount": 52, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "2020-09-24 00:00:00.000", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 6, "amount": {"amount": 52, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "2020-09-25 00:00:00.000", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 6, "amount": {"amount": 52, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "2020-09-26 00:00:00.000", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 6, "amount": {"amount": 52, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "2020-09-27 00:00:00.000", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}]}, {"value": {"type": 11, "amount": {"amount": 0, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 10, "amount": {"amount": 0, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}]}]}, {"value": {"type": 3, "amount": {"amount": 55.68, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": [{"value": {"type": 4, "amount": {"amount": 55.68, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": [{"value": {"type": 11, "amount": {"amount": 0, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 10, "amount": {"amount": 55.68, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": [{"value": {"type": 9, "amount": {"amount": 18.78, "currencyCode": "USD"}, "originalAmount": null, "title": "Tax (Pay at the property)", "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 9, "amount": {"amount": 21.9, "currencyCode": "USD"}, "originalAmount": null, "title": "City tax (Pay at the property)", "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 9, "amount": {"amount": 15, "currencyCode": "USD"}, "originalAmount": null, "title": "Service charge (Pay at the property)", "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}]}]}]}, {"value": {"type": 15, "amount": {"amount": 0, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}]}, "points": [{"pointType": 1, "pointAttributes": {"currency": "USD", "amount": 0}}], "isRedirect": false, "timeoutMinutes": null, "paymentRedirect": null, "paymentCategoryId": 1}, "creditCardInfo": {"creditCardType": 2, "chargeOption": 1, "paymentOption": 0, "creditCardId": 0, "customerIdCard": null, "installmentPlanId": null, "noCvcState": null, "transientCCId": *********, "saveAsPermanent": false}, "payment3DS": {"payment3DSOption": 2, "postBackFields": null, "acceptHeader": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Mobile/15E148 Safari/604.1", "3DS": true}, "bookingState": {"actionType": 23, "actionId": 1350372, "bookingType": 99, "bookingId": 0, "schemaVersion": "1", "flights": [], "slices": [], "segments": [], "passengers": [], "payments": [], "breakdown": [], "breakdownPerPax": [], "baggageAllowance": [], "segmentInfoByPaxType": null, "history": [{"actionId": 1350388, "itineraryId": 344354074, "bookingType": null, "bookingId": null, "actionType": 23, "version": 1, "actionDate": "2020-09-23 04:22:00.000", "parameters": "", "description": "ManualBox", "recStatus": 1, "recCreatedWhen": "2020-09-23 04:22:00.000"}, {"actionId": 1350372, "itineraryId": 344354074, "bookingType": null, "bookingId": null, "actionType": 1, "version": 0, "actionDate": "2020-09-23 04:22:09.000", "parameters": "", "description": "Created", "recStatus": null, "recCreatedWhen": ""}], "paxTickets": [], "itinerary": {"itineraryId": 344354074, "memberId": *********, "recStatus": 1, "recCreatedWhen": "2020-09-23 04:22:00.000", "recModifiedWhen": "2020-09-23 04:22:00.000"}, "summary": [], "userAgent": null, "bookingAttribution": [], "itineraryDate": "2020-09-23 04:22:09.000", "fareRulePolicies": null, "seatSelections": null}, "fraudResult": {"action": 4, "score": 0, "checkIP": "************", "isEmailWhiteList": false, "cardCurrency": "USD", "suggestions": []}, "preAuthResult": {"type": "noAuthInBookingForm", "gatewayId": 0, "gwTransactionId": "", "gatewayInfoId": null, "internalToken": null, "creditCardId": null, "transactionDateTime": "", "bookingIds": [], "status": 13, "require3ds": null, "errors": [], "transactionDate": ""}, "creditCardLast4Digits": "", "initBookingResult": null, "searchReplayStatus": null, "supplierStatus": null, "settlementResult": null, "confirmBookingResult": null, "flightDetails": null, "ticketingAwaitResult": null, "emailConfirmResult": null, "voidPreAuthResult": null, "refundResult": null, "rejectBookingResult": null, "rejectRootCause": null, "emailForSupplier": null, "propertyBookingState": null, "protectionBookingState": null, "tripProtectionBookingState": null, "vehicleBookingState": null, "campaignInfo": null, "whitelabelToken": null, "features": {"is3dsPush": false, "isProductLevelPayment": false}, "redeemResult": [], "refundPointsResult": [], "campaignAction": null, "markCampaignResult": [], "unmarkCampaignResult": [], "emailConfirmResults": [], "commonPaymentInfo": {"method": 2, "displayPriceBreakdown": {"value": {"type": 1, "amount": {"amount": 368.61, "currencyCode": "USD"}, "originalAmount": {"amount": 569.82, "currencyCode": "USD"}, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": [{"value": {"type": 2, "amount": {"amount": 312.93, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": [{"value": {"type": 4, "amount": {"amount": 312.93, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": [{"value": {"type": 5, "amount": {"amount": 312.93, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": {"amount": 52.15, "currencyCode": "USD"}, "productId": null}, "breakdowns": [{"value": {"type": 6, "amount": {"amount": 52.93, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "2020-09-22 00:00:00.000", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 6, "amount": {"amount": 52, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "2020-09-23 00:00:00.000", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 6, "amount": {"amount": 52, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "2020-09-24 00:00:00.000", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 6, "amount": {"amount": 52, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "2020-09-25 00:00:00.000", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 6, "amount": {"amount": 52, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "2020-09-26 00:00:00.000", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 6, "amount": {"amount": 52, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "2020-09-27 00:00:00.000", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}]}, {"value": {"type": 11, "amount": {"amount": 0, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 10, "amount": {"amount": 0, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}]}]}, {"value": {"type": 3, "amount": {"amount": 55.68, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": [{"value": {"type": 4, "amount": {"amount": 55.68, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": [{"value": {"type": 11, "amount": {"amount": 0, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 10, "amount": {"amount": 55.68, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": [{"value": {"type": 9, "amount": {"amount": 18.78, "currencyCode": "USD"}, "originalAmount": null, "title": "Tax (Pay at the property)", "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 9, "amount": {"amount": 21.9, "currencyCode": "USD"}, "originalAmount": null, "title": "City tax (Pay at the property)", "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}, {"value": {"type": 9, "amount": {"amount": 15, "currencyCode": "USD"}, "originalAmount": null, "title": "Service charge (Pay at the property)", "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}]}]}]}, {"value": {"type": 15, "amount": {"amount": 0, "currencyCode": "USD"}, "originalAmount": null, "title": null, "date": "", "averageAmountPerUnit": null, "productId": null}, "breakdowns": null}]}, "timeoutMinutes": null, "redirectInfo": null, "paymentCategoryId": null}, "productPaymentInfo": null, "preAuthResults": [], "settlementResults": [], "payment3DSResult": null, "bitmap$0": false}