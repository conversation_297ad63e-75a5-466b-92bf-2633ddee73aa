using System.Collections.Generic;
using System.Linq;
using Agoda.BookingApi.GenClient.Models;
using Agoda.EBE.Agents.Common.Util.Samples;
using Agoda.EBE.Framework.Objects;
using NUnit.Framework;
using Agoda.EBE.Agents.Common.UnitTest.TestData.BookingAction;
using Agoda.EBE.Framework.Objects.MultiProductBooking;
using Com.Agoda.Mpbe.State.Product.Property;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{
    [TestFixture]
    public class PropertyBookingActionUtilsTest
    {
        [Test]
        public void IsPropertyConfirmed_ReturnTrue_IfLastBookingAllotmentConfirmed()
        {
            var propertyBookingActionUtils = new PropertyBookingActionUtils();
            
            var ba = BookingActionMock.GetBookingActionNormal(999, 111);
            var bas = ba.GetBookingActionState();

            var provisioning = new List<EbeBookingProvisioning>
            {
                new EbeBookingProvisioning
                {
                ReferenceId = 1,
                ReplyStatus = (int)Common.Enum.ABSResponseStatus.AllotmentRejected
                },
                new EbeBookingProvisioning
                {
                    ReferenceId = 2,
                    ReplyStatus = (int)Common.Enum.ABSResponseStatus.AllotmentConfirmed
                }
            };

            var propertyInternalBookings = new List<PropertyInternalBooking>
            {
                new PropertyInternalBooking
                {
                    Booking = new EbeBooking
                    {
                        BookingId = 1
                    },
                    Provisioning = provisioning
                }
            };
            
            var propertyInternalModel = new PropertyInternalModel
            {
                Bookings = propertyInternalBookings
            };

            bas.PropertyInternalModel = propertyInternalModel;
            
            var actual = propertyBookingActionUtils.IsPropertyConfirmed(bas);
            actual.ShouldBe(true);
        }

        [Test]
        public void IsPropertyConfirmed_ReturnFalse_IfLastBookingIsNotAllotmentConfirmed()
        {
            var propertyBookingActionUtils = new PropertyBookingActionUtils();
            
            var ba = BookingActionMock.GetBookingActionNormal(999, 111);
            var bas = ba.GetBookingActionState();

            var provisioning = new List<EbeBookingProvisioning>
            {
                new EbeBookingProvisioning
                {
                    ReferenceId = 1,
                    ReplyStatus = (int)Common.Enum.ABSResponseStatus.AllotmentConfirmed
                },
                new EbeBookingProvisioning
                {
                    ReferenceId = 2,
                    ReplyStatus = (int)Common.Enum.ABSResponseStatus.AllotmentRejected
                }
            };

            var propertyInternalBookings = new List<PropertyInternalBooking>
            {
                new PropertyInternalBooking
                {
                    Booking = new EbeBooking
                    {
                        BookingId = 1
                    },
                    Provisioning = provisioning
                }
            };
            
            var propertyInternalModel = new PropertyInternalModel
            {
                Bookings = propertyInternalBookings
            };

            bas.PropertyInternalModel = propertyInternalModel;

                
            bas.PropertyInternalModel = propertyInternalModel;
            var actual = propertyBookingActionUtils.IsPropertyConfirmed(bas);
            actual.ShouldBe(false);
        }
        
        [Test]
        public void IsPropertyConfirmed_ReturnFalse_IfLastBookingOrderedByRefIdIsNotAllotmentConfirmed()
        {
            var propertyBookingActionUtils = new PropertyBookingActionUtils();
            
            var ba = BookingActionMock.GetBookingActionNormal(999, 111);
            var bas = ba.GetBookingActionState();

            var provisioning = new List<EbeBookingProvisioning>
            {
                new EbeBookingProvisioning
                {
                    ReferenceId = 2,
                    ReplyStatus = (int)Common.Enum.ABSResponseStatus.AllotmentRejected
                },
                new EbeBookingProvisioning
                {
                    ReferenceId = 1,
                    ReplyStatus = (int)Common.Enum.ABSResponseStatus.AllotmentConfirmed
                }
            };

            var propertyInternalBookings = new List<PropertyInternalBooking>
            {
                new PropertyInternalBooking
                {
                    Booking = new EbeBooking
                    {
                        BookingId = 1
                    },
                    Provisioning = provisioning
                }
            };
            
            var propertyInternalModel = new PropertyInternalModel
            {
                Bookings = propertyInternalBookings
            };

            bas.PropertyInternalModel = propertyInternalModel;

                
            bas.PropertyInternalModel = propertyInternalModel;
            var actual = propertyBookingActionUtils.IsPropertyConfirmed(bas);
            actual.ShouldBe(false);
        }
        
        [Test]
        public void IsPropertyConfirmed_ReturnFalse_IfBookingActionStateIsNull()
        {
            var propertyBookingActionUtils = new PropertyBookingActionUtils();
            
            var actual = propertyBookingActionUtils.IsPropertyConfirmed(null);
            actual.ShouldBe(false);
        }
    }
}
