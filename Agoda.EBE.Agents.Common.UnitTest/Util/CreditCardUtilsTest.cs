using Agoda.EBE.Agents.Common.Util;
using NUnit.Framework;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{
    public class CreditCardUtilsTest
    {
        [Test]
        [TestCase("12/2021", 12, 2021)]
        [TestCase("09/2023", 9, 2023)]
        [TestCase("05/24", 5, 2024)]
        [TestCase(null, 0, 0)] // Null won't break
        [TestCase("1234", 1234, 0)] // Mul-format won't break
        public void SplitToCCExpiryMonthAndYear_BaseOnTestCases(string inputExpiryDate, int expectedMonth, int expectedYear)
        {
            var actual = CreditCardUtils.SplitToCCExpiryMonthAndYear(inputExpiryDate);
            actual.Month.ShouldBe(expectedMonth);
            actual.Year.ShouldBe(expectedYear);
        }
    }
}