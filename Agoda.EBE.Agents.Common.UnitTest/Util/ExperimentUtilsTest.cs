using System.Collections.Generic;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Util;
using Agoda.EBE.Framework.Messaging;
using NSubstitute;
using NUnit.Framework;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{
    public class ExperimentUtilsTest
    {
        
        private readonly IMeasurementMessageFactory _measurementMessageFactory = Substitute.For<IMeasurementMessageFactory>();
        private readonly IMessaging _messaging = Substitute.For<IMessaging>();
        private readonly IEBEMeasurementMessage _measurement = new MeasurementMessage("metric", "component");
        
        [Test]
        public void IsExperimentActive_NotForced_ReturnFalse()
        {
            var forcedExperiments = new Dictionary<string, string> { { "TEST-456", "B" } };

            _messaging.MeasurementMessageFactory.Received(1).Returns(_measurementMessageFactory);
            _measurementMessageFactory.CreateNewMeasurement().Returns(_measurement);

            var actual = ExperimentUtils.IsForcedB(forcedExperiments, "TEST-123", _messaging);
            actual.ShouldBe(false);
        }
        
        [Test]
        public void IsExperimentActive_VariantB_ReturnTrue()
        {
            var experimentName = "TEST-123";
            var variantBStr = "B";
            var forcedExperiments = new Dictionary<string, string> { { experimentName, variantBStr } };

            var actual = ExperimentUtils.IsForcedB(forcedExperiments, experimentName, _messaging);
            actual.ShouldBe(true);
        }
    }
}