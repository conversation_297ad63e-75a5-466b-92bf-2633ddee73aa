using System.Linq;
using Agoda.EBE.Agents.Common.UnitTest.TestData.BookingAction;
using Agoda.EBE.Agents.Common.Util;
using Agoda.EBE.Framework.Objects;
using NSubstitute;
using NUnit.Framework;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{
    [TestFixture]
    public class CartMultiProductTypeUtilTest
    {
        [Test]
        public void IsCartPartialSuccessAllowed_ReturnFalse_IfIsPartialSuccessAllowedIsNull()
        {
            var dummyBookingActionStates = new BookingActionState
            {
                Request = new Framework.Objects.MultiProductBooking.Request
                {
                    IsPartialSuccessAllowed = null
                }
            };

            var cartMultiProductTypeUtil = new CartMultiProductTypeUtil();
            var actual = cartMultiProductTypeUtil.IsCartPartialSuccessAllowed(dummyBookingActionStates);
            actual.ShouldBe(false);
        }
        [Test]
        public void IsCartPartialSuccessAllowed_ReturnTrue_IfIsPartialSuccessAllowedIsTrue()
        {
            var dummyBookingActionStates = new BookingActionState
            {
                Request = new Framework.Objects.MultiProductBooking.Request
                {
                    IsPartialSuccessAllowed = true
                }
            };

            var cartMultiProductTypeUtil = new CartMultiProductTypeUtil();
            var actual = cartMultiProductTypeUtil.IsCartPartialSuccessAllowed(dummyBookingActionStates);
            actual.ShouldBe(true);
        }

        [Test]
        public void IsCartPartialSuccessAllowed_ReturnTrue_IfIsPartialSuccessAllowedIsFalse()
        {
            var dummyBookingActionStates = new BookingActionState
            {
                Request = new Framework.Objects.MultiProductBooking.Request
                {
                    IsPartialSuccessAllowed = false
                }
            };
    
            var cartMultiProductTypeUtil = new CartMultiProductTypeUtil();
            var actual = cartMultiProductTypeUtil.IsCartPartialSuccessAllowed(dummyBookingActionStates);
            actual.ShouldBe(false);
        }
    
        [Test]
        public void IsCleanUpFlow_ReturnTrue_IfIsCleanUpFlowIsFalse()
        {
            var dummyBookingActionStates = new BookingActionState
            {
                Request = new Framework.Objects.MultiProductBooking.Request
                {
                    IsCleanUpFlow = false
                }
            };
    
            var cartMultiProductTypeUtil = new CartMultiProductTypeUtil();
            var actual = cartMultiProductTypeUtil.IsCartPartialSuccessAllowed(dummyBookingActionStates);
            actual.ShouldBe(false);
        }
        
        [Test]
        public void IsCleanUpFlow_ReturnFalse_IfIsCleanUpFlowIsNull()
        {
            var dummyBookingActionStates = new BookingActionState
            {
                Request = new Framework.Objects.MultiProductBooking.Request
                {
                    IsCleanUpFlow = null
                }
            };
    
            var cartMultiProductTypeUtil = new CartMultiProductTypeUtil();
            var actual = cartMultiProductTypeUtil.IsCartPartialSuccessAllowed(dummyBookingActionStates);
            actual.ShouldBe(false);
        }
    
        [Test]
        public void IsCleanUpFlow_ReturnTrue_IfIsCleanUpFlowIsTrue()
        {
            var dummyBookingActionStates = new BookingActionState
            {
                Request = new Framework.Objects.MultiProductBooking.Request
                {
                    IsCleanUpFlow = true
                }
            };
    
            var cartMultiProductTypeUtil = new CartMultiProductTypeUtil();
            var actual = cartMultiProductTypeUtil.IsCleanUpFlow(dummyBookingActionStates);
            actual.ShouldBe(true);
        }
    
        [Test]
        public void IsCartBookingHotelConfirmed_ReturnsFalse_IfItsMasterBookingAction()
        {
            var bookingActionSub = Substitute.For<BookingAction>();
            bookingActionSub.BookingId = 0;
            // bookingActionSub.BookingId.Returns();
    
            var cartMultiProductTypeUtil = new CartMultiProductTypeUtil();
            var actual = cartMultiProductTypeUtil.IsCartBookingHotelConfirmed(bookingActionSub);
            actual.ShouldBe(false);
        }
    
        [Test]
        public void IsCartBookingHotelConfirmed_ReturnsFalse_IfBookingIsOtherThanSingleProperty()
        {
            var bookingActionSub = Substitute.For<BookingAction>();
            bookingActionSub.BookingId = 1;
            bookingActionSub.ProductTypeId = (int)Enum.MultiProductType.Package;
    
            var cartMultiProductTypeUtil = new CartMultiProductTypeUtil();
            var actual = cartMultiProductTypeUtil.IsCartBookingHotelConfirmed(bookingActionSub);
            actual.ShouldBe(false);
        }
    
        [Test]
        public void IsCartBookingHotelConfirmed_ReturnsFalse_IfBookingIsSinglePropertyAndNotConfirmed()
        {
            var ba = BookingActionMock.GetBookingActionWithProtoState(999,111, (int)Common.Enum.ABSResponseStatus.AllotmentRejected, (int)Common.Enum.ABSResponseStatus.AllotmentRejected);
            ba.ProductTypeId = (int)(Enum.MultiProductType.SingleProperty);

            var cartMultiProductTypeUtil = new CartMultiProductTypeUtil();
            var actual = cartMultiProductTypeUtil.IsCartBookingHotelConfirmed(ba);
            
            actual.ShouldBe(false);
        }
        
        [Test]
        public void IsCartBookingHotelConfirmed_ReturnsFalse_IfBookingIsSinglePropertyAndTheLatestProvisioningsIsNotConfirmed()
        {
            var ba = BookingActionMock.GetBookingActionWithProtoState(999,111, (int)Common.Enum.ABSResponseStatus.AllotmentConfirmed, (int)Common.Enum.ABSResponseStatus.AllotmentRejected);
            ba.ProductTypeId = (int)(Enum.MultiProductType.SingleProperty);
            
            var cartMultiProductTypeUtil = new CartMultiProductTypeUtil();
            var actual = cartMultiProductTypeUtil.IsCartBookingHotelConfirmed(ba);
            
            actual.ShouldBe(false);
        }
        
        [Test]
        public void IsCartBookingHotelConfirmed_ReturnsTrue_IfBookingIsSinglePropertyAndTheLatestProvisioningsIsConfirmed()
        {
            var ba = BookingActionMock.GetBookingActionWithProtoState(999,111, (int)Common.Enum.ABSResponseStatus.AllotmentRejected, (int)Common.Enum.ABSResponseStatus.AllotmentConfirmed);
            ba.ProductTypeId = (int)(Enum.MultiProductType.SingleProperty);
            
            var cartMultiProductTypeUtil = new CartMultiProductTypeUtil();
            var actual = cartMultiProductTypeUtil.IsCartBookingHotelConfirmed(ba);
            
            actual.ShouldBe(true);
        }
        
        public void IsCartBookingHotelConfirmed_ReturnsTrue_IfBookingIsSinglePropertyAndAllTheProvisioningsIsConfirmed()
        {
            var ba = BookingActionMock.GetBookingActionWithProtoState(999,111, (int)Common.Enum.ABSResponseStatus.AllotmentConfirmed, (int)Common.Enum.ABSResponseStatus.AllotmentConfirmed);
            ba.ProductTypeId = (int)(Enum.MultiProductType.SingleProperty);
            
            var cartMultiProductTypeUtil = new CartMultiProductTypeUtil();
            var actual = cartMultiProductTypeUtil.IsCartBookingHotelConfirmed(ba);
            
            actual.ShouldBe(true);
        }
    }
}
