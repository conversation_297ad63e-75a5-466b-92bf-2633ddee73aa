using System;
using Agoda.EBE.Agents.Common.Util;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{
    [TestFixture]
    public class RetryStrategyTest
    {
        [Test]
        [TestCase(0, 19)]
        [TestCase(1, 23)]
        [TestCase(2, 29)]
        [TestCase(4, 37)]
        [TestCase(8, 53)]
        [TestCase(9, 59)]
        [TestCase(11, 23)]
        [TestCase(12, 29)]
        [TestCase(18, 53)]
        [TestCase(99, 59)]

        public void ExponentialBackoffWorks(int input, int expected)
        {
            Assert.AreEqual(expected, RetryStrategy.RetryPeriod(input));
        }
        
        [Test]
        [TestCase(1, 2)]
        [TestCase(2, 4)]
        [TestCase(3, 6)]
        [TestCase(4, 8)]
        [TestCase(5, 10)]
        [TestCase(6, 10)]
        [TestCase(Int32.MaxValue, 10)]

        public void TruncatedLinearBackoffWorks(int numRetry, int expected)
        {
            var retryInput = new RetryStrategy.RetryEstimatorInput
            {
                InitialRetryTime = 2,
                MaxRetryTime = 10
            };
            Assert.AreEqual(expected, RetryStrategy.TruncatedLinearRetryPeriod(numRetry, retryInput));
        }
        
        [Test]
        [TestCase(0, 15)]
        [TestCase(1, 15)]
        [TestCase(2, 15)]
        [TestCase(3, 30)]
        [TestCase(4, 30)]
        [TestCase(5, 30)]
        [TestCase(6, 60)]
        [TestCase(Int32.MaxValue, 240)]

        public void ExponentialRetryBackoffWorks(int numRetry, int expected)
        {
            var retryInput = new RetryStrategy.RetryEstimatorInput
            {
                InitialRetryTime = 15,
                MaxRetryTime = 240
            };
            Assert.AreEqual(expected, RetryStrategy.ExponentialRetryPeriod(numRetry, retryInput));
        }
    }
}