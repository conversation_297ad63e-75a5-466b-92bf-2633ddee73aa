﻿using System;
using Agoda.EBE.Agents.Common.Util;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{
    [TestFixture]
    public class PaymentUtilsTest
    {
        [Test]
        public void PaymentUtils_GetTransactionId()
        {
            var act = PaymentUtils.GetTransactionId("111/222");
            Assert.AreEqual(111, act);
            act = PaymentUtils.GetTransactionId("111-222");
            Assert.AreEqual(111, act);
            act = PaymentUtils.GetTransactionId("111");
            Assert.AreEqual(111, act);
            Assert.Throws<FormatException>(() => PaymentUtils.GetTransactionId("xxx"));
        }

        [Test]
        [TestCase(Enum.WhitelabelId.Agoda, Enum.MultiProductType.SingleProperty,
            Enum.PaymentClientIdType.PropertyAgoda, false)]
        [TestCase(Enum.WhitelabelId.Agoda, Enum.MultiProductType.SingleFlight, Enum.PaymentClientIdType.PropertyAgoda, false)]
        [TestCase(Enum.WhitelabelId.TestStrategicPartners, Enum.MultiProductType.SingleProperty, Enum.PaymentClientIdType.SPWhitelabel, false)]
        [TestCase(Enum.WhitelabelId.TestStrategicPartners, Enum.MultiProductType.SingleFlight, Enum.PaymentClientIdType.PropertyAgoda, false)]
        [TestCase(Enum.WhitelabelId.Agoda, Enum.MultiProductType.SingleProperty, Enum.PaymentClientIdType.PropertyAgodaV2, true)]
        public void PaymentUtils_GetPaymentClientId(
            Enum.WhitelabelId whitelabelId,
            Enum.MultiProductType multiProductType,
            Enum.PaymentClientIdType paymentClientIdType,
            bool usePropertyAgodaPaymentConfigV2)
        {
            var paymentClientId =
                PaymentUtils.GetPaymentClientId((int)whitelabelId, multiProductType, usePropertyAgodaPaymentConfigV2);
            Assert.AreEqual(paymentClientIdType, (Enum.PaymentClientIdType)paymentClientId);
        }
    }
}
