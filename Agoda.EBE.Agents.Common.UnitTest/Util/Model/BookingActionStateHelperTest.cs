using System;
using System.Linq;
using Agoda.EBE.Agents.Common.UnitTest.TestData.BookingAction;
using Agoda.EBE.Agents.Common.Util.Model;
using Agoda.EBE.Framework.Objects;
using Com.Agoda.Commons.Agprotobuf.Net.Utils;
using Com.Agoda.Mpbe.State.Product.Property;
using NUnit.Framework;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Util.Model
{
    [TestFixture]
    public class BookingActionStateHelperTest
    {
        #region Tests for HOTEL UPC Flow

        [Test]
        public void UpdateBreakdownProto_Test()
        {
            long upcId = 1;
            int bookingId = 123442;
            int hotelPaymentMethodId = 1;
            Guid userId = Guid.NewGuid();
            var ba = BookingActionMock.GetBookingActionWithProto(bookingId, 111);
            var zoneRetainedConversion = true;
            var now = DateTimeOffset.Now;
            var nowAsTimestamp = now.LocalDateTime.ToTimestamp(zoneRetainedConversion);

            var actual = ba.GetBookingActionState()
                .UpdateBreakdownProto(hotelPaymentMethodId, userId, upcId, now.LocalDateTime);

            var propertyState = actual.ItineraryState.Product.Properties.First();

            propertyState.FinancialBreakdowns.ToList().ForEach(breakdown =>
            {
                Console.WriteLine("[recModifiedWhen|now] - timestamp [{0} | {1}] | now: {2:o}", breakdown.RecModifyWhen, nowAsTimestamp, now);
                breakdown.UpcId.ShouldBe(upcId);
                breakdown.RecModifyWhen.ShouldBe(nowAsTimestamp);
            });
        }

        [Test]
        public void InsertSuccProto_Test()
        {
            Int32 bookingId = 1234242;
            long succid = 50000;
            Guid payoutUuid = Guid.NewGuid();
            string last4 = "3236";
            double exchangeRate = 1.2;
            string requestcurrency = "AED";
            double requestcreditlimit = 10;
            double actualcreditlimit = 100;
            string currencycode = "AED";
            double amount = 10.3;
            int cardtype = 1;
            int cardstatus = 1;
            Guid userid = Guid.NewGuid();

            var zoneRetainedConversion = true;
            var now = DateTimeOffset.Now;
            var nowAsTimestamp = now.LocalDateTime.ToTimestamp(zoneRetainedConversion);

            var expected = new EbePropertyBookingSingleUsedCreditCard()
            {
                BookingId = bookingId,
                SuccId = succid,
                PayoutUuid = payoutUuid.toUUID(),
                Last4 = last4,
                ExchangeRate = Converters.toProtoDecimalValue((decimal)exchangeRate),
                RequestCurrency = requestcurrency,
                RequestCreditLimit = Converters.toProtoDecimalValue((decimal)requestcreditlimit),
                ActualCreditLimit = Converters.toProtoDecimalValue((decimal)actualcreditlimit),
                CurrencyCode = currencycode,
                Amount = Converters.toProtoDecimalValue((decimal)amount),
                CardType = cardtype,
                CardStatus = cardstatus,
                IsSubmitted = false,
                RecStatus = (int)Common.Enum.RecStatus.Active,
                RecCreatedWhen = nowAsTimestamp,
                RecCreatedBy = Converters.toUUID(userid),
            };

            var ba = BookingActionMock.GetBookingActionWithProto(999, 111);
            var actual = ba.GetBookingActionState().InsertSuccProto(bookingId, succid, payoutUuid, last4, exchangeRate,
                requestcurrency,
                requestcreditlimit, actualcreditlimit, currencycode, amount, cardtype, cardstatus, userid, now.LocalDateTime);

            var actualBookingSucc = actual.ItineraryState.Product.Properties.First().BookingSingleUsedCreditCards.First();

            expected.BookingId.ShouldBe(actualBookingSucc.BookingId);
            expected.PayoutUuid.ShouldBe(actualBookingSucc.PayoutUuid);
            expected.Last4.ShouldBe(actualBookingSucc.Last4);
            expected.ExchangeRate.ShouldBe(actualBookingSucc.ExchangeRate);
            expected.RequestCreditLimit.ShouldBe(actualBookingSucc.RequestCreditLimit);
            expected.ActualCreditLimit.ShouldBe(actualBookingSucc.ActualCreditLimit);
            expected.Amount.ShouldBe(actualBookingSucc.Amount);
            expected.RecStatus.ShouldBe(actualBookingSucc.RecStatus);
            expected.RecCreatedWhen.ShouldBe(actualBookingSucc.RecCreatedWhen);
        }

        [Test]
        public void UpdateSuccAmountProto_Test()
        {
            // Arrange
            int bookingId = 1000;
            int bookingsuccid = 9999;
            decimal exchangerate = 50000;
            decimal requestcreditlimit = 500000;
            decimal actualcreditlimit = 6000000;
            string currencycode = "JYP";
            Guid userid = Guid.NewGuid();
            string requestcurrency = "THB";
            decimal amount = 10000;

            var zoneRetainedConversion = true;
            var now = DateTimeOffset.Now;
            var nowAsTimestamp = now.LocalDateTime.ToTimestamp(zoneRetainedConversion);

            #region BookingSucc Proto

            var bookingSuccProto = new EbePropertyBookingSingleUsedCreditCard()
            {
                BookingSuccId = bookingsuccid,
                BookingId = bookingId,
                SuccId = 0,
                Last4 = "1234",
                ExchangeRate = Converters.toProtoDecimalValue((decimal)exchangerate),
                RequestCreditLimit = Converters.toProtoDecimalValue((decimal)requestcreditlimit),
                ActualCreditLimit = Converters.toProtoDecimalValue((decimal)actualcreditlimit),
                CurrencyCode = currencycode,
                CardType = 1,
                CardStatus = 1,
                IsSubmitted = true,
                RecStatus = 1,
                RecCreatedWhen = nowAsTimestamp,
                RecCreatedBy = Guid.NewGuid().toUUID(),
                RecModifyWhen = null, //to make sure that RecModifyWhen must be updated from original value
                RecModifyBy = Guid.NewGuid().toUUID(),
                RequestCurrency = requestcurrency,
                Amount = Converters.toProtoDecimalValue((decimal)amount),
            };

            #endregion


            var expected = new EbePropertyBookingSingleUsedCreditCard()
            {
                ExchangeRate = Converters.toProtoDecimalValue((decimal)exchangerate),
                RequestCreditLimit = Converters.toProtoDecimalValue((decimal)requestcreditlimit),
                ActualCreditLimit = Converters.toProtoDecimalValue((decimal)actualcreditlimit),
                CurrencyCode = currencycode,
                RequestCurrency = requestcurrency,
                Amount = Converters.toProtoDecimalValue((decimal)amount),
                RecModifyWhen = nowAsTimestamp,
            };

            var ba = BookingActionMock.GetBookingActionWithProto(999, 111);
            var state = ba.GetBookingActionState();
            var itineraryState = state.ItineraryState;

            itineraryState.Product.Properties.First().BookingSingleUsedCreditCards.Add(bookingSuccProto);

            state.ItineraryState = itineraryState;
            ba.State = BookingActionMock.toJson(state);

            var actual = ba.GetBookingActionState().UpdateSuccAmountProto(bookingId, bookingsuccid, exchangerate,
                requestcreditlimit, actualcreditlimit, currencycode, userid, requestcurrency, amount, now.LocalDateTime);

            var actualBookingSucc = actual.ItineraryState.Product.Properties.First().BookingSingleUsedCreditCards.First();

            expected.ExchangeRate.ShouldBe(actualBookingSucc.ExchangeRate);
            expected.RequestCreditLimit.ShouldBe(actualBookingSucc.RequestCreditLimit);
            expected.ActualCreditLimit.ShouldBe(actualBookingSucc.ActualCreditLimit);
            expected.CurrencyCode.ShouldBe(actualBookingSucc.CurrencyCode);
            expected.Amount.ShouldBe(actualBookingSucc.Amount);
            expected.RequestCurrency.ShouldBe(actualBookingSucc.RequestCurrency);
            expected.RecModifyWhen.ShouldBe(actualBookingSucc.RecModifyWhen);
        }

        [Test]
        public void UpdateSuccCancelStatusProto_Test()
        {
            // Arrange
            int recStatus = (int)Common.Enum.RecStatus.Active;
            int cardStatus = 1;
            int bookingId = 1000;
            int bookingsuccid = 9999;
            double exchangerate = 50000;
            double requestcreditlimit = 500000;
            double actualcreditlimit = 6000000;
            string currencycode = "JYP";
            Guid userid = Guid.NewGuid();
            string requestcurrency = "THB";
            double amount = 10000;

            var zoneRetainedConversion = true;
            var now = DateTimeOffset.Now;
            var nowAsTimestamp = now.LocalDateTime.ToTimestamp(zoneRetainedConversion);

            #region BookingSucc Proto

            var bookingSuccProto = new EbePropertyBookingSingleUsedCreditCard()
            {
                BookingSuccId = bookingsuccid,
                BookingId = bookingId,
                SuccId = 0,
                Last4 = "1234",
                ExchangeRate = Converters.toProtoDecimalValue((decimal)exchangerate),
                RequestCreditLimit = Converters.toProtoDecimalValue((decimal)requestcreditlimit),
                ActualCreditLimit = Converters.toProtoDecimalValue((decimal)actualcreditlimit),
                CurrencyCode = currencycode,
                CardType = 1,
                CardStatus = 1,
                IsSubmitted = true,
                RecStatus = 1,
                RecCreatedWhen = Converters.ToTimestamp(DateTime.Now),
                RecCreatedBy = Guid.NewGuid().toUUID(),
                RecModifyWhen = null, //to make sure that RecModifyWhen must be updated from original value
                RecModifyBy = Guid.NewGuid().toUUID(),
                RequestCurrency = requestcurrency,
                Amount = Converters.toProtoDecimalValue((decimal)amount),
            };

            #endregion


            var expected = new EbePropertyBookingSingleUsedCreditCard()
            {
                RequestCreditLimit = Converters.toProtoDecimalValue((decimal)Decimal.Zero),
                ActualCreditLimit = Converters.toProtoDecimalValue((decimal)Decimal.Zero),
                Amount = Converters.toProtoDecimalValue((decimal)Decimal.Zero),
                RecStatus = recStatus,
                CardStatus = cardStatus,
                RecModifyWhen = nowAsTimestamp
            };

            //Act
            var ba = BookingActionMock.GetBookingActionWithProto(999, 111);
            var state = ba.GetBookingActionState();
            var itineraryState = state.ItineraryState;

            itineraryState.Product.Properties.First().BookingSingleUsedCreditCards.Add(bookingSuccProto);

            state.ItineraryState = itineraryState;
            ba.State = BookingActionMock.toJson(state);

            var actual = ba.GetBookingActionState()
                .UpdateSuccCancelStatusProto(bookingId, bookingsuccid, recStatus, cardStatus, userid, now.LocalDateTime);

            var actualBookingSucc = actual.ItineraryState.Product.Properties.First().BookingSingleUsedCreditCards.First();


            //Assert
            expected.RequestCreditLimit.ShouldBe(actualBookingSucc.RequestCreditLimit);
            expected.ActualCreditLimit.ShouldBe(actualBookingSucc.ActualCreditLimit);
            expected.Amount.ShouldBe(actualBookingSucc.Amount);
            expected.RecStatus.ShouldBe(actualBookingSucc.RecStatus);
            expected.CardStatus.ShouldBe(actualBookingSucc.CardStatus);
            expected.RecModifyWhen.ShouldBe(actualBookingSucc.RecModifyWhen);
        }

        [Test]
        public void UpdateBookingIsAdvanceGuaranteeProto_Test()
        {
            // Arrange
            int bookingId = 1000;
            bool isAdvanceGuarantee = true;
            Guid userId = Guid.NewGuid();

            var zoneRetainedConversion = true;
            var now = DateTimeOffset.Now;
            var nowAsTimestamp = now.LocalDateTime.ToTimestamp(zoneRetainedConversion);

            var actionId = 3030043;

            var expectedSellInfo = new EbePropertyBookingSellInfo()
            {
                IsAdvanceGuarantee = isAdvanceGuarantee,
                LastUpdatedWhen = nowAsTimestamp
            };

            var expectedSellInfoHistories = new EbePropertyBookingSellInfoHistory()
            {
                HistoryActionDate = nowAsTimestamp
            };

            //Act
            var ba = BookingActionMock.GetBookingActionWithProto(999, 111);
            var actual = ba.GetBookingActionState()
                .UpdateBookingIsAdvanceGuaranteeProto(bookingId, isAdvanceGuarantee, userId, now.LocalDateTime);

            var actualProductProperties = actual.ItineraryState.Product.Properties.First();
            var actualSellInfo = actualProductProperties.SellInfo;
            var actualSellInfoHistories = actualProductProperties.SellInfoHistories.First();

            //Assert
            expectedSellInfo.IsAdvanceGuarantee.ShouldBe(actualSellInfo.IsAdvanceGuarantee);
            expectedSellInfo.LastUpdatedWhen.ShouldBe(actualSellInfo.LastUpdatedWhen);

            expectedSellInfoHistories.HistoryActionDate.ShouldBe(actualSellInfoHistories.HistoryActionDate);
        }

        [Test]
        public void UpdateFinanceInfoInAdditionalBookingDataProto_Test()
        {
            var expectedHotelPaymentMethodId = 1;
            var expectedHotelPaymentConditionId = 2;
            //Act
            var ba = BookingActionMock.GetBookingActionWithProto(999, 111);
            var actual = ba.GetBookingActionState()
                .UpdateFinanceInfoInAdditionalBookingDataProto(expectedHotelPaymentMethodId, expectedHotelPaymentConditionId);

            var actualAdditionalData = actual.ItineraryState.Product.Properties.First().AdditionalData;

            //Assert
            actualAdditionalData.HotelPaymentMethodId.ShouldBe(expectedHotelPaymentMethodId);
            actualAdditionalData.HotelPaymentConditionId.ShouldBe(expectedHotelPaymentConditionId);
        }

        #endregion

        #region Tests for HOTEL PROVISION Flow

        [Test]
        public void UpdatePriceDisplaySettingProto_Test()
        {
            int bookingId = 123442;
            var priceDisplaySettings = "price-display";

            var ba = BookingActionMock.GetBookingActionWithProto(bookingId, 111);
            var actual = ba.GetBookingActionState().UpdatePriceDisplaySettingProto(bookingId, priceDisplaySettings);

            actual.ItineraryState.Product.Properties.First().BookingSummary.PriceDisplaySetting
                .ShouldBe(priceDisplaySettings);
        }

        [Test]
        public void InsertEBEBookingProvisioningProto_Test()
        {
            int bookingId = 1211;
            int processType = 2;
            int dmcId = 332;
            DateTime dmcDueDate = DateTime.Now;
            string localDMCCurrency = "THB";
            decimal netRate = 32;
            int replyStatus = 1;
            decimal originalNetRate = 25;
            string requestXML = "requestXML";
            string responseXML = "responseXML";
            string remarks = "remarks";
            string bookingExternalReference = "123432";
            string token = "token";
            Guid userGuid = Guid.NewGuid();

            var zoneRetainedConversion = true;
            var now = DateTimeOffset.Now;
            var nowAsTimestamp = now.LocalDateTime.ToTimestamp(zoneRetainedConversion);
            var dmcDueDateAsTimestamp = dmcDueDate.ToTimestamp(zoneRetainedConversion);

            var expected = new EbePropertyBookingProvisioning
            {
                ReferenceId = 1,
                BookingId = bookingId,
                DmcId = dmcId,
                MethodId = processType,
                TransmittalDate = nowAsTimestamp,
                ReplyDate = nowAsTimestamp,
                DmcDueDate = dmcDueDateAsTimestamp,
                LocalDmcCurrency = localDMCCurrency,
                OriginalNetRate = originalNetRate.toProtoDecimalValue(),
                NetRate = netRate.toProtoDecimalValue(),
                BookingExternalReference = bookingExternalReference,
                RequestXML = requestXML,
                ResponseXML = responseXML,
                Remarks = remarks,
                ReplyStatus = replyStatus,
                RecStatus = (int)Common.Enum.RecStatus.Active,
                RecCreatedWhen = nowAsTimestamp,
                RecCreatedBy = userGuid.toUUID(),
                PurchaseDetails = string.Empty
            };


            var ba = BookingActionMock.GetBookingActionWithProto(999, 111);
            var actual = ba.GetBookingActionState().InsertEBEBookingProvisioningProto(bookingId, processType, 1, dmcId,
                dmcDueDate, localDMCCurrency, netRate, replyStatus, originalNetRate, requestXML, responseXML, remarks,
                bookingExternalReference, token, userGuid, now.LocalDateTime);

            var actualBookingSucc = actual.ItineraryState.Product.Properties.First().Provisionings.First();

            expected.BookingId.ShouldBe(actualBookingSucc.BookingId);
            expected.ReferenceId.ShouldBe(actualBookingSucc.ReferenceId);
            expected.DmcId.ShouldBe(actualBookingSucc.DmcId);
            expected.MethodId.ShouldBe(actualBookingSucc.MethodId);
            expected.LocalDmcCurrency.ShouldBe(actualBookingSucc.LocalDmcCurrency);
            expected.BookingExternalReference.ShouldBe(actualBookingSucc.BookingExternalReference);
            expected.RecStatus.ShouldBe(actualBookingSucc.RecStatus);
            expected.RecCreatedWhen.ShouldBe(actualBookingSucc.RecCreatedWhen);
            expected.TransmittalDate.ShouldBe(actualBookingSucc.TransmittalDate);
            expected.ReplyDate.ShouldBe(actualBookingSucc.ReplyDate);
            expected.DmcDueDate.ShouldBe(actualBookingSucc.DmcDueDate);
        }

        [Test]
        public void UpdateEBEBookingProvisioningProto_Test()
        {
            var bookingProvisioningID = 1;
            var processType = 2;
            var dmcDueDate = DateTime.Now;
            string localCurrency = "THB";
            decimal netRate = 32;
            int replyStatus = 1;
            decimal originalNetRate = 25;
            string requestXML = "requestXML";
            string responseXML = "responseXML";
            string remarks = "remarks";
            string bookingExternalReference = "123432";
            string token = "token";
            Guid userGuid = Guid.NewGuid();

            var zoneRetainedConversion = true;
            var now = DateTimeOffset.Now;
            var nowAsTimestamp = now.LocalDateTime.ToTimestamp(zoneRetainedConversion);
            var dmcDueDateAsTimestamp = dmcDueDate.ToTimestamp(zoneRetainedConversion);

            var expected = new EbePropertyBookingProvisioning
            {
                MethodId = processType,
                ReplyStatus = replyStatus,
                RequestXML = requestXML,
                ResponseXML = responseXML,
                Remarks = remarks,
                NetRate = netRate.toProtoDecimalValue(),
                LocalDmcCurrency = localCurrency,
                BookingExternalReference = bookingExternalReference,
                Token = token,
                RecModifyWhen = nowAsTimestamp,
                ReplyDate = nowAsTimestamp,
                TransmittalDate = nowAsTimestamp,
                DmcDueDate = dmcDueDateAsTimestamp
            };

            var ba = BookingActionMock.GetBookingActionWithProto(999, 111);
            var readyState = ba.GetBookingActionState().InsertEBEBookingProvisioningProto(12345, processType, 1, 332,
                dmcDueDate, "THB", netRate, replyStatus, originalNetRate, requestXML, responseXML, remarks,
                bookingExternalReference, token, userGuid, now.LocalDateTime);

            var actual = readyState
                .UpdateEBEBookingProvisioningProto(bookingProvisioningID, processType, replyStatus, requestXML,
                    responseXML, netRate, localCurrency, remarks, dmcDueDate, bookingExternalReference,
                    token, userGuid, now.LocalDateTime);

            var actualBookingSucc = actual.ItineraryState.Product.Properties.First().Provisionings.First();

            expected.MethodId.ShouldBe(actualBookingSucc.MethodId);
            expected.ReplyStatus.ShouldBe(actualBookingSucc.ReplyStatus);
            expected.RequestXML.ShouldBe(actualBookingSucc.RequestXML);
            expected.ResponseXML.ShouldBe(actualBookingSucc.ResponseXML);
            expected.LocalDmcCurrency.ShouldBe(actualBookingSucc.LocalDmcCurrency);
            expected.Token.ShouldBe(actualBookingSucc.Token);
            expected.RecModifyWhen.ShouldBe(actualBookingSucc.RecModifyWhen);
            expected.ReplyDate.ShouldBe(actualBookingSucc.ReplyDate);
            expected.TransmittalDate.ShouldBe(actualBookingSucc.TransmittalDate);
            expected.DmcDueDate.ShouldBe(actualBookingSucc.DmcDueDate);
        }

        [Test]
        public void UpdateDMCSpecificDataProto_Test()
        {
            var supplierSpecificData = "supplier Specific data";
            Guid userGuid = Guid.NewGuid();

            var zoneRetainedConversion = true;
            var now = DateTimeOffset.Now;
            var nowAsTimestamp = now.LocalDateTime.ToTimestamp(zoneRetainedConversion);

            var ba = BookingActionMock.GetBookingActionWithProto(999, 111);
            var actual = ba.GetBookingActionState().UpdateDMCSpecificDataProto(supplierSpecificData, userGuid, now.LocalDateTime);
            var actualBooking = actual.ItineraryState.Product.Properties.First().Booking;

            actualBooking.DmcSpecificData.ShouldBe(supplierSpecificData);
            actualBooking.RecModifyWhen.ShouldBe(nowAsTimestamp);
        }

        [Test]
        public void UpdateBookingExtReferenceProto_Test()
        {
            var externalRef = "external-ref-data&";
            Guid userGuid = Guid.NewGuid();

            var zoneRetainedConversion = true;
            var now = DateTimeOffset.Now;
            var nowAsTimestamp = now.LocalDateTime.ToTimestamp(zoneRetainedConversion);

            var ba = BookingActionMock.GetBookingActionWithProto(999, 111);
            var actual = ba.GetBookingActionState().UpdateBookingExtReferenceProto(externalRef, userGuid, now.LocalDateTime);

            var actualBooking = actual.ItineraryState.Product.Properties.First().Booking;

            actualBooking.BookingExternalReference.ShouldBe(externalRef);
            actualBooking.RecModifyWhen.ShouldBe(nowAsTimestamp);
        }

        [Test]
        public void UpdateIsConfirmedBookingProto_Test()
        {
            var isConfirmed = true;
            Guid userGuid = Guid.NewGuid();

            var zoneRetainedConversion = true;
            var now = DateTimeOffset.Now;
            var nowAsTimestamp = now.LocalDateTime.ToTimestamp(zoneRetainedConversion);

            var ba = BookingActionMock.GetBookingActionWithProto(999, 111);
            var actual = ba.GetBookingActionState().UpdateIsConfirmedBookingProto(isConfirmed, userGuid, now.LocalDateTime);

            var actualBookingSummary = actual.ItineraryState.Product.Properties.First().BookingSummary;

            actualBookingSummary.IsConfirmedBooking.ShouldBe(isConfirmed);
            actualBookingSummary.RecModifyWhen.ShouldBe(nowAsTimestamp);
        }

        [Test]
        public void UpsertBookingSupplierDataProto_Test()
        {
            var siteId = "1";
            var subsiteId = "22";
            var supplierTransactionId = "supplier transaction id";
            var inventoryData = "inventory data";
            Guid userGuid = Guid.NewGuid();
            var supplierResponseInfo = "supplier response info";

            var ba = BookingActionMock.GetBookingActionWithProto(999, 111);
            var bkActState = ba.GetBookingActionState();
            var checkPoint = DateTime.Now;

            var zoneRetainedConversion = true;
            var now = DateTimeOffset.Now;
            var nowAsTimestamp = now.LocalDateTime.ToTimestamp(zoneRetainedConversion);

            //insert
            var inserted = bkActState.UpsertBookingSupplierDataProto(siteId, 
                subsiteId, 
                supplierTransactionId, 
                inventoryData, 
                userGuid, 
                now.LocalDateTime,
                supplierResponseInfo);

            inserted.ItineraryState.Product.Properties.First().BookingSupplierData.SupplierSiteId.ShouldBe(siteId);
            inserted.ItineraryState.Product.Properties.First().BookingSupplierData.SupplierSubSiteId.ShouldBe(subsiteId);
            inserted.ItineraryState.Product.Properties.First().BookingSupplierData.SupplierTransactionId.ShouldBe(supplierTransactionId);
            inserted.ItineraryState.Product.Properties.First().BookingSupplierData.InventoryData.ShouldBe(inventoryData);
            inserted.ItineraryState.Product.Properties.First().BookingSupplierData.RecCreatedBy.Value.ShouldBe(userGuid.ToString());
            inserted.ItineraryState.Product.Properties.First().BookingSupplierData.RecCreatedWhen.ToLocalDateTime().ShouldBeGreaterThan(checkPoint);
            inserted.ItineraryState.Product.Properties.First().BookingSupplierData.RecCreatedWhen.ShouldBe(nowAsTimestamp);
            inserted.ItineraryState.Product.Properties.First().BookingSupplierData.RecModifyBy.ShouldBeNull();
            inserted.ItineraryState.Product.Properties.First().BookingSupplierData.RecModifyWhen.ShouldBeNull();
            inserted.ItineraryState.Product.Properties.First().BookingSupplierData.SupplierResponseInfo.ShouldBe(supplierResponseInfo);

            //update
            var updated = bkActState.UpsertBookingSupplierDataProto(
                siteId + "UPD",
                subsiteId + "UPD",
                supplierTransactionId + "UPD",
                inventoryData + "UPD",
                userGuid,
                now.LocalDateTime,
                supplierResponseInfo + "UPD");

            updated.ItineraryState.Product.Properties.First().BookingSupplierData.SupplierSiteId.ShouldBe(siteId + "UPD");
            updated.ItineraryState.Product.Properties.First().BookingSupplierData.SupplierSubSiteId.ShouldBe(subsiteId + "UPD");
            updated.ItineraryState.Product.Properties.First().BookingSupplierData.SupplierTransactionId.ShouldBe(supplierTransactionId + "UPD");
            updated.ItineraryState.Product.Properties.First().BookingSupplierData.InventoryData.ShouldBe(inventoryData + "UPD");
            updated.ItineraryState.Product.Properties.First().BookingSupplierData.RecModifyBy.Value.ShouldBe(userGuid.ToString());
            updated.ItineraryState.Product.Properties.First().BookingSupplierData.RecModifyWhen.ToLocalDateTime().ShouldBeGreaterThan(checkPoint);
            updated.ItineraryState.Product.Properties.First().BookingSupplierData.RecModifyWhen.ShouldBe(nowAsTimestamp);
            updated.ItineraryState.Product.Properties.First().BookingSupplierData.SupplierResponseInfo.ShouldBe(
                 supplierResponseInfo + "UPD");
        }

        [Test]
        public void UpdateInventoryTypeIdProto_Test()
        {
            var inventoryTypeId = 5;
            Guid userGuid = Guid.NewGuid();

            var ba = BookingActionMock.GetBookingActionWithProto(999, 111);
            var bkActState = ba.GetBookingActionState();
            var checkPoint = DateTime.Now;

            var zoneRetainedConversion = true;
            var now = DateTimeOffset.Now;
            var nowAsTimestamp = now.LocalDateTime.ToTimestamp(zoneRetainedConversion);

            var itineraryState = bkActState.ItineraryState;
            itineraryState.Product.Properties[0].RateCategory = new EbePropertyBookingRateCategory();
            bkActState.ItineraryState = itineraryState;

            var updated = bkActState.UpdateInventoryTypeIdProto(inventoryTypeId, userGuid, now.LocalDateTime);
            updated.ItineraryState.Product.Properties.First().RateCategory.InventoryTypeId.ShouldBe(inventoryTypeId);
            updated.ItineraryState.Product.Properties.First().RateCategory.LastUpdatedBy.Value.ShouldBe(userGuid.ToString());
            updated.ItineraryState.Product.Properties.First().RateCategory.LastUpdatedWhen.ToLocalDateTime().ShouldBeGreaterThan(checkPoint);
            updated.ItineraryState.Product.Properties.First().RateCategory.LastUpdatedWhen.ShouldBe(nowAsTimestamp);
        }
        
        
        [Test]
        public void UpdateSupplierSessionIdProto_Success()
        {
            var ba = BookingActionMock.GetBookingActionWithProto(999, 111);
            var bkActState = ba.GetBookingActionState();

            var supplierSessionId = "c0a86b93-8971-4cae-b4da-7708c51f4a61";
            var itineraryState = bkActState.ItineraryState;
            itineraryState.Product.Properties[0].AdditionalData = new EbeAdditionalBookingData();
            bkActState.ItineraryState = itineraryState;

            var updated = bkActState.UpdateSupplierSessionIdProto(supplierSessionId);
            updated.ItineraryState.Product.Properties.First().AdditionalData.SupplierSessionId.ShouldBe(supplierSessionId);
        }  

        #endregion

        #region Tests for VEHICLE UPC Flow

        [Test]
        public void VehicleUpdateBookingActionStateProto_Test()
        {
            long upcId = 1;
            int bookingId = 123442;

            var ba = BookingActionMock.GetBookingActionWithProto(bookingId, 111);
            var actual = ba.GetBookingActionState().VehicleUpdateBookingActionStateProto(upcId);

            actual.ItineraryState.Product.Vehicles.First().Breakdowns.First().UpcId.ShouldBe(upcId);
            actual.ItineraryState.Product.Vehicles.First().Breakdowns.First().UpcPaymentMethodId
                .ShouldBe(null);
        }

        #endregion
    }
}