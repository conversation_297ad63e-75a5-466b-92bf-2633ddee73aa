using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.DataAccess.Interface;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Messaging.Log;
using Agoda.EBE.Agents.Common.UnitTest.TestData.BookingAction;
using Agoda.EBE.Agents.Common.Util;
using Agoda.EBE.Framework;
using Agoda.EBE.Framework.Objects;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{
    [TestFixture]
    [Category("Provisioning_Agent")]
    [Parallelizable(ParallelScope.All)]
    public class BookingActionUtilsTests
    {
        [Test]
        public void GetBookingState_WithValidData()
        {
            var messaging = Substitute.For<IMessaging>();
            var bfdb = Substitute.For<IBFDBDataAccess>();
            var booking = new BookingAction();
            booking.BookingId = 1234;
            booking.State = GetBookingState();
            booking.ItineraryId = 567;
            booking.ProductTypeId = 1;
                
            bfdb.GetBookingAction((int) booking.BookingId).Returns(booking);

            var sut = new BookingActionUtils(messaging, bfdb);

            var bookingState = sut.GetBookingAction((int) booking.BookingId, "test");

            bookingState.ShouldNotBeNull();

            var mappedProductTypeId = bookingState.ProductTypeId;
            mappedProductTypeId.ShouldBe(booking.ProductTypeId,"Check for ProductTypeId mapping");
        }

        [Test]
        public void GetBookingState_ShouldRetry3Times()
        {
            var messaging = Substitute.For<IMessaging>();
            var bfdb = Substitute.For<IBFDBDataAccess>();
            var booking = new BookingAction();
            booking.BookingId = 1234;
            booking.State = "INVALID";
            booking.ItineraryId = 567;

            bfdb.GetBookingAction((int)booking.BookingId).Returns(booking);

            var sut = new BookingActionUtils(messaging, bfdb);

            Should.Throw<JsonReaderException>(() => sut.GetBookingAction((int)booking.BookingId, "test"));

            messaging.Received(sut.MaxRetries)
                .ExceptionMessage.Send(
                    Arg.Any<string>(),
                    Arg.Any<Exception>(),
                    LogLevel.WARN,
                    Arg.Any<Dictionary<string, string>>());
        }

        private string GetBookingState()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var resourceName = "Agoda.EBE.Agents.Common.UnitTest.Util.Samples.ValidBookingState.json";
            string result = null;

            using (Stream stream = assembly.GetManifestResourceStream(resourceName))
            using (StreamReader reader = new StreamReader(stream))
            {
                result = reader.ReadToEnd();
            }

            return result;
        }

        [Test]
        public void GetBookingsState_WithValidData()
        {
            var messaging = Substitute.For<IMessaging>();
            var bfdb = Substitute.For<IBFDBDataAccess>();
            var booking = new BookingAction();
            booking.BookingId = 0;
            booking.State = GetBookingState();
            booking.ItineraryId = 567;

            bfdb.GetBookingActionByItineraryID((int) booking.ItineraryId).Returns(new List<BookingAction> {booking});

            var sut = new BookingActionUtils(messaging, bfdb);

            var bookingState = sut.GetBookingActions((int) booking.ItineraryId, "test");

            bookingState.ShouldNotBeNull();
        }

        [Test]
        public void GetBookingsState_NotFound()
        {
            var messaging = Substitute.For<IMessaging>();
            var bfdb = Substitute.For<IBFDBDataAccess>();
            var booking = new BookingAction();
            booking.BookingId = 1234;
            booking.State = GetBookingState();
            booking.ItineraryId = 567;

            bfdb.GetBookingActionByItineraryID((int) booking.ItineraryId).Returns(new List<BookingAction>());

            var sut = new BookingActionUtils(messaging, bfdb);

            var bookingState = sut.GetBookingActions((int) booking.ItineraryId, "test");

            bookingState.ShouldBeNull();
        }

        [Test]
        public void GetBookingsState_WithValidPropertyBookingStateData()
        {
            var messaging = Substitute.For<IMessaging>();
            var bfdb = Substitute.For<IBFDBDataAccess>();
            var booking = BookingActionMock.GetBookingActionWithProto(999, 111);

            bfdb.GetBookingAction((int) booking.BookingId).Returns(booking);

            var sut = new BookingActionUtils(messaging, bfdb);

            var bookingState = sut.GetBookingAction((int) booking.BookingId, "test");

            bookingState.ShouldNotBeNull();

            var actualIsTestBookingInternal = bookingState.State.PropertyInternalModel.Bookings[0].BookingSummary.isTestBooking;
            actualIsTestBookingInternal.ShouldBe(true, "Check for Property InternalModel isTestBooking Mapping");

            var actualIsTestBookingProto =
                bookingState.State.ItineraryState.Product.Properties[0].BookingSummary.IsTestBooking;
            actualIsTestBookingProto.ShouldBe(true, "Check for Property Proto isTestBooking Mapping");
        }
    }
}