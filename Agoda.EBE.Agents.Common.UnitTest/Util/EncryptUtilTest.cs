using Agoda.EBE.Agents.Common.Util;
using NUnit.Framework;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Util
{
    [TestFixture]
    public class EncryptUtilTest
    {
        private string phoneNumber = "0987654321";
        private string encryptedPhoneNumber = "3000390038003700360035003400330032003100";
        private string email = "<EMAIL>";
        private string encryptedEmail = "740065006D0070002E003100320033004000610067006F00640061002E0063006F006D00";
        
        private string concatString = "Test01Test02Test03";
        private string hashkey = "HASH";
        private string hashedStr = "4aa963157f5fa315a60260a792ba1a951fdb9182";
        private string hashedStrWithoutKey = "6aedc0b56be6a83bdd0b31e64e6062e5a300f31f";
        private string hashedEmptyStr = "da39a3ee5e6b4b0d3255bfef95601890afd80709";
        
        [Test]
        public void EncryptPhoneNumber_ReturnsEncryptedPhoneNumber()
        {
            EncryptUtil.Encode(phoneNumber).ShouldBe(encryptedPhoneNumber);
        }
        
        [Test]
        public void DecryptPhoneNumber_ReturnsDecryptedPhoneNumber()
        {
            EncryptUtil.Decode(encryptedPhoneNumber).ShouldBe(phoneNumber);
        }
        
        [Test]
        public void EncryptEmail_ReturnsEncryptedEmail()
        {
            EncryptUtil.Encode(email).ShouldBe(encryptedEmail);
        }
        
        [Test]
        public void DecryptEmail_ReturnsDecryptedEmail()
        {
            EncryptUtil.Decode(encryptedEmail).ShouldBe(email);
        }
        
        [Test]
        public void EncryptEmptyString_ReturnsEncryptedEmptyString()
        {
            EncryptUtil.Encode("").ShouldBe("");
        }
        
        [Test]
        public void DecryptEmptyString_ReturnsDecryptedEmptyString()
        {
            EncryptUtil.Decode("").ShouldBe("");
        }
        
        [Test]
        public void CalculateHashCode_ReturnsHashedString()
        {
            EncryptUtil.CalculateHashCode(concatString, hashkey ).ShouldBe(hashedStr);
        }
        
        [Test]
        public void CalculateHashCode_WithoutHashKey_ReturnsHashedString()
        {
            EncryptUtil.CalculateHashCode(concatString, "" ).ShouldBe(hashedStrWithoutKey);
        }
        
        [Test]
        public void CalculateHashCode_EmptyStr_ReturnsHashedString()
        {
            EncryptUtil.CalculateHashCode("", "" ).ShouldBe(hashedEmptyStr);
        }
    }
}