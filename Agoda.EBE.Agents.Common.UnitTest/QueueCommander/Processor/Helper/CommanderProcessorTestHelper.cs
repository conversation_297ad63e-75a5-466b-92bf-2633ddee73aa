using System;
using System.Threading;
using Agoda.EBE.Agents.Common.Base.Interface;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.QueueCommander.Object;
using Agoda.EBE.Agents.Common.QueueCommander.Processor;
using Agoda.EBE.Agents.Common.QueueCommander.Utils;
using Agoda.EBE.Agents.Common.QueueCommander.Utils.Interface;
using Agoda.EBE.Agents.Common.QueueConsumer.Processor.Interface;
using Agoda.EBE.Framework.MQ;
using Agoda.EBE.Framework.MQ.Interface;
using Agoda.EBE.Framework.MQ.MessageType;
using EasyNetQ;
using NSubstitute;

namespace Agoda.EBE.Agents.Common.UnitTest.QueueCommander.Processor.Helper
{
    public abstract class CommanderProcessorTestHelper
    {
        private CommanderConfiguration _configMock;
        private IRabbitMQAdapter _publishQueueAdepterMock;
        private CancellationTokenSource _cancellationTokenSourceMock;
        private string _rmqConnectionString;
        internal IConsumerProcessor _consumerProcessMock;
        private IQueueProcessorBase _queueProcessorBaseMock;
        internal IMessaging _messagingMock;
        private IQueueNameUtils _queueNameUtils;
        
        internal void SetUp()
        {
            _configMock = new CommanderConfiguration("EBE.TestAgent",
                10, 10, "queueConnectionString");
            _publishQueueAdepterMock = Substitute.For<IRabbitMQAdapter>();
            _cancellationTokenSourceMock = Substitute.For<CancellationTokenSource>();
            _rmqConnectionString = "queueConnectionString";
            _consumerProcessMock = Substitute.For<IConsumerProcessor>();
            _queueProcessorBaseMock = Substitute.For<IQueueProcessorBase>();
            _messagingMock = Substitute.For<IMessaging>();
            _queueNameUtils = new QueueNameUtils(_configMock.QueueBaseName, "DMC");
        }
        
        internal CommanderProcessor GetCommander()
        {
            return new CommanderProcessor(
                _publishQueueAdepterMock,
                _consumerProcessMock,
                _queueProcessorBaseMock,
                _messagingMock,
                _configMock,
                _cancellationTokenSourceMock,
                _queueNameUtils);
        }

        internal void VerifySubscribeTimes(int times)
        {
            _queueProcessorBaseMock.Received(times).SubscribeAndProcess(Arg.Any<string>(), 
                Arg.Any<int>(), Arg.Any<string>(), 
                Arg.Any<CancellationTokenSource>(), Arg.Any<Action<IMessage<DefaultMessage>,
                    MessageReceivedInfo>>());
        }

        internal void VerifySubscribeToQueueTimes(string queueName, int times)
        {
            _queueProcessorBaseMock.Received(times).SubscribeAndProcess(Arg.Is(queueName), 
                Arg.Is(10), Arg.Is(_rmqConnectionString), 
                Arg.Is(_cancellationTokenSourceMock), Arg.Any<Action<IMessage<DefaultMessage>, MessageReceivedInfo>>());
        }
    }
}