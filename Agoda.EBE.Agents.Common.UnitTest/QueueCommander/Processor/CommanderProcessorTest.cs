using Agoda.EBE.Agents.Common.UnitTest.QueueCommander.Processor.Helper;
using Agoda.EBE.Framework.MQ.MessageType;
using EasyNetQ;
using NSubstitute;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Common.UnitTest.QueueCommander.Processor
{
    [TestFixture]
    [Category("QueueCommander")]
    public class CommanderProcessorTest: CommanderProcessorTestHelper
    {
        [SetUp]
        public void SetUp()
        {
            base.SetUp();
        }

        [Test]
        public void Received_SingleCommand()
        {
            var commander = GetCommander();
            
            var ycsCommandMsg = Substitute.For<IMessage<CommandMessage>>();
            ycsCommandMsg.Body.Returns(new CommandMessage("332"));
            
            commander.OnReceived(ycsCommandMsg, null);
            
            VerifySubscribeTimes(1);
            VerifySubscribeToQueueTimes("EBE.TestAgent.DMC.332", 1);
        }
        
        [Test]
        public void Received_MultipleCommandSameDmc()
        {
            var commander = GetCommander();
            
            var ycsCommandMsg = Substitute.For<IMessage<CommandMessage>>();
            ycsCommandMsg.Body.Returns(new CommandMessage("332"));

            commander.OnReceived(ycsCommandMsg, null);
            commander.OnReceived(ycsCommandMsg, null);

            VerifySubscribeTimes(1);
            VerifySubscribeToQueueTimes("EBE.TestAgent.DMC.332", 1);
        }
        
        [Test]
        public void Received_MultipleCommandDifferentDmc()
        {
            var commander = GetCommander();
            var ycsCommandMsg = Substitute.For<IMessage<CommandMessage>>();
            ycsCommandMsg.Body.Returns(new CommandMessage("332"));
                
            var bcomCommandMsg = Substitute.For<IMessage<CommandMessage>>();
            bcomCommandMsg.Body.Returns(new CommandMessage("3038"));

            commander.OnReceived(ycsCommandMsg, null);
            commander.OnReceived(bcomCommandMsg, null);
            
            VerifySubscribeTimes(2);
            VerifySubscribeToQueueTimes("EBE.TestAgent.DMC.332", 1);
            VerifySubscribeToQueueTimes("EBE.TestAgent.DMC.3038", 1);
        }
    }
}