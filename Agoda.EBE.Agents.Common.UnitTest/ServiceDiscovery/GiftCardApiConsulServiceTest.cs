using System.Collections.Generic;
using Agoda.EBE.Agents.Common.ServiceDiscovery;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Common.UnitTest.ServiceDiscovery
{
    [TestFixture]
    public class GiftCardApiConsulServiceTests
    {
        private const string TestServiceName = "giftcard_api-main";
        private static readonly IEnumerable<string> TestServiceTags = new[] { "main" };

        [Test]
        public void ServiceName_ReturnsExpectedName()
        {
            var giftCardApiService = new GiftCardApiConsulService();

            Assert.AreEqual(TestServiceName, giftCardApiService.ServiceName());
        }

        [Test]
        public void ServiceTags_ReturnsExpectedTags()
        {
            var giftCardApiService = new GiftCardApiConsulService();

            var tags = giftCardApiService.ServiceTags().Tags;
            Assert.Contains("main", tags);
        }

        [Test]
        public void DefaultDevelopmentValue_ReturnsEmptyList()
        {
            var giftCardApiService = new GiftCardApiConsulService();

            Assert.IsEmpty(giftCardApiService.DefaultDevelopmentValue());
        }

        [Test]
        public void GetEndpoints_BuildsExpectedEndpoints()
        {
            var services = new List<Config.Consul.Abstractions.Service>
            {
                new Config.Consul.Abstractions.Service { Address = "127.0.0.1", ServicePort = 8080 },
                new Config.Consul.Abstractions.Service { Address = "*********", ServicePort = 8081 }
            };

            var giftCardApiService = new GiftCardApiConsulService();
            foreach (var service in services)
            {
                var endpoint = GiftCardApiConsulService.GetEndpoint(service);

                Assert.IsTrue(endpoint.Contains(service.Address));
                Assert.IsTrue(endpoint.Contains(service.ServicePort.ToString()));
                Assert.IsTrue(endpoint.Contains("/giftcard"));
            }
        }
    }
}