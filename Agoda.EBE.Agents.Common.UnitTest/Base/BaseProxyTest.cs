using System;
using Agoda.EBE.Agents.Common.Base;
using Agoda.EBE.Agents.Common.Messaging;
using NSubstitute;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Common.UnitTest.Base
{
    public class BaseProxyTest
    {
        private class TestProxy : BaseProxy
        {
            protected override IMeasurementMessageFactory MeasurementMessageFactory { get; } =
                Substitute.For<IMeasurementMessageFactory>();

            protected override ExternalServiceName ApiName => ExternalServiceName.BookingApi;


            public bool MockServiceCall(
                System.Enum methodName,
                Func<bool> func,
                Func<Exception, bool> onError = null)
            {
                return MeasureCall(
                    methodName: methodName,
                    methodFunc: func,
                    defineSuccessFunc: response => response,
                    onError: onError
                );
            }
        }

        private enum Test
        {
            Endpoint1
        }


        [Test]
        public void MeasureCall_ShouldHandleSuccess()
        {
            var proxy = new TestProxy();
            var functionCalled = false;

            var result = proxy.MockServiceCall(Test.Endpoint1, () =>
            {
                functionCalled = true;
                return true;
            });

            Assert.True(functionCalled);
            Assert.True(result);
        }


        [Test]
        public void MeasureCall_ShouldThrowOnExceptionWithNoErrorHandler()
        {
            var proxy = new TestProxy();
            Assert.Throws<Exception>(() => proxy.MockServiceCall(Test.Endpoint1, () => throw new Exception()));
        }

        [Test]
        public void MeasureCall_ShouldHandleExceptionWithErrorHandler()
        {
            var proxy = new TestProxy();
            var errorHandlerCalled = false;

            Assert.Throws<Exception>(() => proxy.MockServiceCall(Test.Endpoint1, () => throw new Exception(), ex =>
            {
                errorHandlerCalled = true;
                throw ex;
            }));
            Assert.True(errorHandlerCalled);
        }
    }
}