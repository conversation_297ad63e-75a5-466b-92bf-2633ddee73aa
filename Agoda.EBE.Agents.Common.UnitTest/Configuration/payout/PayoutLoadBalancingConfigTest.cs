using System;
using System.Collections.Generic;
using Agoda.EBE.Agents.Common.Configuration.Interface;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.ServiceDiscovery;
using NSubstitute;
using NUnit.Framework;
using Assert = NUnit.Framework.Assert;


namespace Agoda.EBE.Agents.UPC.UnitTest.Configuration
{
    [TestFixture]
    public class PayoutLoadBalancingConfigTest
    {
        private IPayoutConfiguration _payoutConfiguration;
        private PayoutApiDiscovery _payoutApiDiscovery;
        private IMessaging _messaging;

        [SetUp]
        public void Setup()
        {
            _payoutConfiguration = new PayoutConfiguration();
            _payoutApiDiscovery = new PayoutApiDiscovery();
            _messaging = Substitute.For<IMessaging>();
        }

        [Test]
        public void Create_PayoutLoadBalancingConfig_From_Static_Urls()
        {
            Setup();

            var baseUrls = new List<string>();
            baseUrls.Add("https://localhost:8443");
            _payoutConfiguration.UsePayoutStaticUrls = true;
            _payoutConfiguration.PayoutUrls = baseUrls[0];
            var payoutLoadBalancingConfig = PayoutLoadBalancingConfigFactory.GetPayoutLoadBalancingConfig(_payoutConfiguration, _payoutApiDiscovery,_messaging);
            Assert.AreEqual(baseUrls, payoutLoadBalancingConfig.GetPayoutServer());
        }

        [Test]
        public void Cannot_Create_PayoutLoadBalancingConfig_From_Static_Urls_If_Urls_Is_Empty()
        {
            Setup();
            _payoutConfiguration.UsePayoutStaticUrls = true;
            _payoutConfiguration.PayoutUrls = "";
            Assert.That(() => PayoutLoadBalancingConfigFactory.GetPayoutLoadBalancingConfig(_payoutConfiguration, _payoutApiDiscovery,_messaging),
                Throws.TypeOf<ArgumentNullException>()," Value cannot be null.");
        }
        
        [Test]
        public void Create_PayoutLoadBalancingConfig_From_Discovery()
        {
            Setup();
            var baseUrls = new List<string>();
            baseUrls.Add("https://localhost:8443");
            var services = new List<Config.Consul.Abstractions.Service>();
            var service = new Config.Consul.Abstractions.Service();
            service.ServiceAddress = "localhost";
            service.ServicePort = 8443;
            services.Add(service);
            _payoutApiDiscovery.SetValue(services);
            _payoutConfiguration.UsePayoutStaticUrls = false;
            _payoutConfiguration.PayoutUrls = baseUrls[0];
            var payoutLoadBalancingConfig = PayoutLoadBalancingConfigFactory.GetPayoutLoadBalancingConfig(_payoutConfiguration, _payoutApiDiscovery,_messaging);
            Assert.AreEqual(baseUrls, payoutLoadBalancingConfig.GetPayoutServer());
        }
    }
}