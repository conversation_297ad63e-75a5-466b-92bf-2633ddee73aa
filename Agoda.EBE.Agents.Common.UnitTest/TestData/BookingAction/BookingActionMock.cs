using System;
using System.IO;
using System.Linq;
using Agoda.EBE.Framework.Objects;
using Agoda.EBE.Framework.Objects.MultiProductBooking;
using Com.Agoda.Commons.Agprotobuf.Net.Utils;
using Com.Agoda.Commons.Agprotobuf.Types;
using Com.Agoda.Mpbe.State.Itinerary;
using Com.Agoda.Mpbe.State.Product;
using Com.Agoda.Mpbe.State.Product.Common;
using Com.Agoda.Mpbe.State.Product.Property;
using Com.Agoda.Mpbe.State.Product.Vehicle;
using Google.Protobuf.Collections;
using Google.Protobuf.WellKnownTypes;
using Newtonsoft.Json;
using Org.BouncyCastle.Asn1.Esf;

namespace Agoda.EBE.Agents.Common.UnitTest.TestData.BookingAction
{
    public static class BookingActionMock
    {
        private static readonly JsonSerializerSettings SerializeSettings = new JsonSerializerSettings
        {
            // Serialization settings to make DateTime compatible with Scala workflow-agent
            DateTimeZoneHandling = DateTimeZoneHandling.Unspecified, // No time zone specified
            DateFormatString = "yyyy'-'MM'-'dd' 'HH':'mm':'ss.fff" // Only 3 digits of milliseconds
        };


        public static Framework.Objects.BookingAction GetBookingActionNormal(int bookingId, int actionId)
        {
            var stateJson = File.ReadAllText("TestData/BookingAction/PropertyBookingAction_Normal.json");
            var stateJsonOverwrittenId = stateJson.Replace("[BookingId]", bookingId.ToString());
            var bookingAction = new Framework.Objects.BookingAction
            {
                BookingId = bookingId,
                ActionId = actionId,
                State = stateJsonOverwrittenId,
                WorkflowId = 2,
                WorkflowStateId = 300,
                RecCreatedWhen = DateTime.Now,
                RecModifyWhen = DateTime.Now
            };

            return bookingAction;
        }

        public static Framework.Objects.BookingAction GetBookingActionWithProto(int bookingId, int actionId)
        {
            var ba = GetBookingActionNormal(bookingId, actionId);
            var state = ba.GetBookingActionState();
            state.ItineraryState = toItineraryStateProto(ba);

            ba.State = JsonConvert.SerializeObject(state, SerializeSettings);

            return ba;
        }

        public static String toJson(BookingActionState state)
        {
            return JsonConvert.SerializeObject(state, SerializeSettings);
        }


        private static PropertyProductModel GetPropertyProductModelProto(BookingActionState state)
        {
            var booking = state.PropertyInternalModel.Bookings.First();
            var financialBreakdownProto = new EbePropertyFinancialBreakdown()
            {
                HotelPaymentMethodId = 0
            };


            var sellInfo = booking.SellInfo;
            var propertyProductModelProto = new PropertyProductModel();

            var sellInfoProto = new EbePropertyBookingSellInfo()
            {
                BookingId = sellInfo.BookingId,
                PricingTemplateId = sellInfo.PricingTemplateId,
                DownLiftAmountUsd = sellInfo.DownLiftAmountUsd.GetValueOrDefault().toProtoDecimalValue(),
                SellTagId = sellInfo.SellTagId,
                SearchId = sellInfo.SearchId,
                IsAdvanceGuarantee = sellInfo.IsAdvanceGuarantee,
                OfferId = sellInfo.OfferId,
                LastUpdatedWhen = Converters.ToTimestamp(sellInfo.LastUpdatedWhen),
                LastUpdatedBy = sellInfo.LastUpdatedBy.toUUID(),
                PricingRequestId = sellInfo.PricingRequestId
            };

            var summary = booking.BookingSummary;
            propertyProductModelProto.BookingSummary = new EbePropertyBookingSummary()
            {
                BookingId = summary.BookingId,
                MultiProductId = summary.MultiProductId,
                DisplayCurrency = summary.DisplayCurrency,
                OriginalSellingAmount = summary.OriginalSellingAmount.GetValueOrDefault().toProtoDecimalValue(),
                OriginalSupplierAmount = summary.OriginalSupplierAmount.GetValueOrDefault().toProtoDecimalValue(),
                OriginalLocalCurrency = summary.OriginalLocalCurrency,
                OriginalLocalSupplierAmount = summary.OriginalLocalSupplierAmount.GetValueOrDefault().toProtoDecimalValue(),
                OriginalPaymentCurrency = summary.OriginalPaymentCurrency,
                OriginalLocalPaymentAmount = summary.OriginalLocalPaymentAmount.GetValueOrDefault().toProtoDecimalValue(),
                OriginalLocalCcPaymentAmount = summary.OriginalLocalCcPaymentAmount.GetValueOrDefault().toProtoDecimalValue(),
                OriginalRedeemAmount = summary.OriginalRedeemAmount.GetValueOrDefault().toProtoDecimalValue(),
                PlatformId = summary.PlatformId,
                ChargeOptionId = summary.ChargeOptionId,
                OriginalFullyChargeDate = Converters.ToTimestamp(summary.OriginalFullyChargeDate.GetValueOrDefault()),
                IsUserLoggedIn = summary.IsUserLoggedIn,
                IsTravelAgency = summary.IsTravelAgency,
                TravelAgencyLoginChannel = summary.TravelAgencyLoginChannel,
                OriginalFullyAuthDate = Converters.ToTimestamp(summary.OriginalFullyAuthDate.GetValueOrDefault()),
                TaxSurchargeInfo = summary.TaxSurchargeInfo,
                IsNotCcRequired = summary.IsNotCcRequired,
                ExchangeRateOption = summary.ExchangeRateOption,
                PriceDisplaySetting = summary.PriceDisplaySetting,
                DisplayAmount = summary.DisplayAmount.GetValueOrDefault().toProtoDecimalValue(),
                BookingCreatedDate = Converters.ToTimestamp(summary.BookingCreatedDate.GetValueOrDefault()),
                IsConfirmedBooking = summary.IsConfirmedBooking,
                WhitelabelId = summary.WhitelabelId,
                RecStatus = summary.RecStatus,
                RecCreatedWhen = Converters.ToTimestamp(summary.RecCreatedWhen),
                RecCreatedBy = new UUID()
                {
                    Value = summary.RecCreatedBy.ToString()
                },
                RecModifyWhen = Converters.ToTimestamp(summary.RecModifyWhen.GetValueOrDefault()),
                RecModifyBy = new UUID()
                {
                    Value = summary.RecModifyBy.ToString()
                },
                StayType = summary.StayType,
                IsTestBooking = summary.isTestBooking
            };
            propertyProductModelProto.Booking = new EbePropertyBooking();
            propertyProductModelProto.Booking.BookingId = booking.SellInfo.BookingId;

            propertyProductModelProto.SellInfo = sellInfoProto;
            propertyProductModelProto.FinancialBreakdowns.Add(financialBreakdownProto);

            propertyProductModelProto.AdditionalData = new EbeAdditionalBookingData
            {
                SupplierData = new EbePropertyBookingSupplier()
            };

            return propertyProductModelProto;
        }

        private static VehicleProductModel GetVehicleProductModelProto(BookingActionState state)
        {
            var vehicleProductModel = new VehicleProductModel();
            vehicleProductModel.Breakdowns.Add(new FinancialBreakdown());

            return vehicleProductModel;
        }

        private static ItineraryState toItineraryStateProto(Framework.Objects.BookingAction action)
        {
            var baState = action.GetBookingActionState();

            var proto = new ItineraryState();

            var ItineraryProto = new Itinerary()
            {
                ItineraryId = action.ItineraryId,
                MemberId = action.MemberId,
                RecStatus = action.RecStatus,
                RecCreatedWhen = Converters.ToTimestamp(action.RecCreatedWhen),
                RecModifiedWhen = Converters.ToTimestamp(action.RecModifyWhen.ToUniversalTime())
            };

            var ItineraryModelProto = new ItineraryModel()
            {
                Itinerary = ItineraryProto
            };

            var ProductProto = new ProductModel();
            ProductProto.Properties.Add(GetPropertyProductModelProto(baState));
            ProductProto.Vehicles.Add(GetVehicleProductModelProto(baState));

            proto.Itinerary = ItineraryModelProto;
            proto.Product = ProductProto;
            return proto;
        }


        private static EbePropertyBooking toEbePropertyBookingProto(Framework.Objects.BookingAction action)
        {
            
            var booking = action.GetBookingActionState().PropertyInternalModel.Bookings.First().Booking;

            var propertyBookingProto = new EbePropertyBooking();

            propertyBookingProto.AffiliateModel = booking.AffiliateModel;
            propertyBookingProto.AgentName = booking.StringAgentName;
            propertyBookingProto.AvailabilityType = booking.AvailabilityType;
            propertyBookingProto.AffiliatePaymentMethod = booking.AffiliatePaymentMethod;
            propertyBookingProto.AgentBookingChannel = booking.AgentBookingChannel;
            propertyBookingProto.AgentClientIp = booking.AgentClientIp;
            propertyBookingProto.ArrivalTimeWindow = booking.ArrivalTimeWindow;
            propertyBookingProto.AgodaCancellationFeeId = booking.AgodaCancellationFeeId;

            propertyBookingProto.BookingId = booking.BookingId;
            propertyBookingProto.BookingDate = Converters.ToTimestamp(booking.BookingDate.GetValueOrDefault());
            propertyBookingProto.BookingDateFrom = Converters.ToTimestamp(booking.BookingDateFrom.GetValueOrDefault());
            propertyBookingProto.BookingDateUntil = Converters.ToTimestamp(booking.BookingDateUntil.GetValueOrDefault());
            propertyBookingProto.BookingExternalReference = booking.BookingExternalReference;
            propertyBookingProto.BookingTypeId = booking.BookingTypeId;


            propertyBookingProto.CcReceived = booking.CcReceived;
            propertyBookingProto.CancellationPolicy = booking.CancellationPolicy;
            propertyBookingProto.CancellationFeeAmount =
                booking.CancellationFeeAmount.GetValueOrDefault().toProtoDecimalValue();
            propertyBookingProto.CidList = booking.CidList;
            propertyBookingProto.CancellationPolicyCode = booking.CancellationPolicyCode;

            propertyBookingProto.DataCenter = booking.Datacenter;
            propertyBookingProto.DiscountAmount = booking.DiscountAmount.GetValueOrDefault().toProtoDecimalValue();
            propertyBookingProto.DiscountSavings = booking.DiscountSavings.GetValueOrDefault().toProtoDecimalValue();
            propertyBookingProto.DiscountType = booking.DiscountType;
            propertyBookingProto.DmcCode = booking.DmcCode;
            propertyBookingProto.DmcId = booking.DmcId;
            propertyBookingProto.DmcSpecificData = booking.DmcSpecificData;

            propertyBookingProto.Excluded = booking.Excluded;

            propertyBookingProto.FeCid = booking.FeCid;
            propertyBookingProto.FraudAction = booking.FraudAction;
            propertyBookingProto.FraudScore = booking.FraudScore;
            propertyBookingProto.FraudCheckIp = booking.FraudCheckIp;
            propertyBookingProto.FallbackCxlPolicyCode = booking.FallbackCxlPolicyCode;
            propertyBookingProto.FaxFormRequestStatus = booking.FaxformRequestStatus;

            propertyBookingProto.Included = booking.Included;
            propertyBookingProto.IsLocked = booking.IsLocked;
            propertyBookingProto.ItineraryId = booking.ItineraryId;
            propertyBookingProto.IsAffiliateProcessed = booking.IsAffiliateProcessed;
            propertyBookingProto.IsAgentAssist = booking.IsAgentAssist;
            propertyBookingProto.IsAutoProcessed = booking.IsAutoProcessed;
            propertyBookingProto.IsEmailWhitelist = booking.IsEmailWhitelist;
            propertyBookingProto.IsFraudReview = booking.IsFraudReview;
            propertyBookingProto.IsReminderSent = booking.IsReminderSent;
            propertyBookingProto.IsRewardsProcessed = booking.IsRewardsProcessed;
            propertyBookingProto.IsSpecialOffer = booking.IsSpecialOffer;
            propertyBookingProto.IsChargeBackRequested = booking.IsChargebackRequested;

            propertyBookingProto.LanguageId = booking.LanguageId;

            propertyBookingProto.MembershipContentLabel = booking.MembershipContentLabel;
            propertyBookingProto.MembershipContentText = booking.MembershipContentText;


            propertyBookingProto.PaymentModel = booking.PaymentModel;
            propertyBookingProto.PointMultiply = booking.PointMultiply;
            propertyBookingProto.PromotionCode = booking.PromotionCode;
            propertyBookingProto.PromotionText = booking.PromotionText;
            propertyBookingProto.PreBookingId = booking.PrebookingId;
            propertyBookingProto.PromotionCampaignId = booking.PromotionCampaignId;

            propertyBookingProto.RateChannel = booking.RateChannel;
            propertyBookingProto.RecStatus = booking.RecStatus;
            propertyBookingProto.ReferralUrl = booking.ReferralUrl;
            propertyBookingProto.ReviewBy = booking.ReviewBy.GetValueOrDefault().toUUID();
            propertyBookingProto.RecCreatedWhen = Converters.ToTimestamp(booking.RecCreatedWhen);
            propertyBookingProto.RateModelType = booking.RatemodelType;
            propertyBookingProto.RecModifyWhen = Converters.ToTimestamp(booking.RecModifyWhen.GetValueOrDefault());
            propertyBookingProto.RecModifyBy = booking.RecModifyBy.GetValueOrDefault().toUUID();
            propertyBookingProto.RecCreatedBy = booking.RecCreatedBy.toUUID();
            propertyBookingProto.RewardsSpecialOfferId = booking.RewardsSpecialOfferId;

            propertyBookingProto.ServerName = booking.ServerName;
            propertyBookingProto.SessionId = booking.SessionId;
            propertyBookingProto.SsId = booking.SsId.GetValueOrDefault();
            propertyBookingProto.SsIdFb = booking.SsId.GetValueOrDefault();
            propertyBookingProto.StorefrontId = booking.StorefrontId.GetValueOrDefault();
            propertyBookingProto.SsProviderId = booking.SsProviderId.GetValueOrDefault();
            propertyBookingProto.SsResultId = booking.SsResultId.GetValueOrDefault();
            propertyBookingProto.SsResultType = booking.SsResultType.GetValueOrDefault();

            propertyBookingProto.TrackingTag = booking.TrackingTag;
            propertyBookingProto.TrackingCookieDate =
                Converters.ToTimestamp(booking.TrackingCookieDate.GetValueOrDefault());
            propertyBookingProto.TrackingCookieId = booking.TrackingCookieId;
            propertyBookingProto.UserTracking = booking.UserTracking.GetValueOrDefault().toUUID();

            propertyBookingProto.WorkflowId = booking.WorkflowId;
            propertyBookingProto.WorkflowActionId = booking.WorkflowActionId;
            propertyBookingProto.WorkflowParameterId = booking.WorkflowParameterId;
            propertyBookingProto.WorkflowParameterValue = booking.WorkflowParameterValue;
            propertyBookingProto.WorkflowStateId = booking.WorkflowStateId;

            return propertyBookingProto;
        }
        
        public static Framework.Objects.BookingAction GetBookingActionWithProtoState(int bookingId, int actionId,
            int replyStatus1, int replyStatus2)
        {
            var stateJson = File.ReadAllText("TestData/BookingAction/PropertyBookingAction_WithProtoState.json");
            var stateJsonOverwrittenId = stateJson.Replace("[BookingId]", bookingId.ToString())
                .Replace("[ReplyStatus1]", replyStatus1.ToString())
                .Replace("[ReplyStatus2]", replyStatus2.ToString());
            var bookingAction = new Framework.Objects.BookingAction
            {
                BookingId = bookingId,
                ActionId = actionId,
                State = stateJsonOverwrittenId,
                WorkflowId = 2,
                WorkflowStateId = 300,
                RecCreatedWhen = DateTime.Now,
                RecModifyWhen = DateTime.Now
            };
            var bas = bookingAction.GetBookingActionState();
            bas.ItineraryState = toItineraryStateProto(bookingAction);
            bas.PropertyProductModel = GetPropertyProductModelProto(bas);
            
            
            var booking = bas.PropertyInternalModel.Bookings.First();
            var provisions = booking.Provisioning.Select(item => new EbePropertyBookingProvisioning()
            {
                ReferenceId = item.ReferenceId,
                BookingProvisioningId = item.BookingProvisioningId,
                BookingId = item.BookingId,
                DmcId = item.BookingId,
                MethodId = item.MethodId,
                LocalDmcCurrency = item.LocalDmcCurrency,
                ReplyStatus = item.ReplyStatus
            });
            var propertyProductModelProto = new PropertyProductModel();
            propertyProductModelProto.Provisionings.AddRange(provisions);
            bas.PropertyProductModel = propertyProductModelProto;
            bookingAction.State = JsonConvert.SerializeObject(bas, SerializeSettings);
            
            return bookingAction;
        }

    }
}