﻿// <copyright file="EnigmaClientMetricProcessorTest.cs" company="Agoda Company Co., Ltd.">
// AGODA ® is a registered trademark of AGIP LLC, used under license by Agoda Company Co., Ltd.. Agoda is part of Priceline (NASDAQ:PCLN)
// </copyright>

using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Framework.Messaging;
using NSubstitute;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Common.UnitTest.Messaging.EnigmaMetric
{
    using System;
    using System.Collections.Generic;

    [TestFixture]
    [Category("Enigma_Client_Metric")]
    public class EnigmaClientMetricProcessorTest
    {
        private IMessaging mockedMessaging;
        IMeasurementMessageFactory measurementMessageFactory;
        IEBEExceptionMessage exceptionMessage;
        private EnigmaClientMetricProcessor enigmaClientMetricProcessor;

        [SetUp]
        public void SetUp()
        {
            mockedMessaging = Substitute.For<IMessaging>();
            measurementMessageFactory = Substitute.For<IMeasurementMessageFactory>();
            exceptionMessage = Substitute.For<IEBEExceptionMessage>();
            mockedMessaging.ExceptionMessage.Returns(exceptionMessage);
            mockedMessaging.MeasurementMessageFactory.Returns(measurementMessageFactory);
            enigmaClientMetricProcessor = new EnigmaClientMetricProcessor(mockedMessaging);

        }

        [Test]
        public void TestHandle()
        {
            var logLevel = LogLevel.INFO;
            var endPoint = "BookingDetail";
            var elapsedMillis = 2L;
            enigmaClientMetricProcessor.Handle(logLevel, new Dictionary<string, object>(), endPoint, elapsedMillis);
            exceptionMessage.Received().Send(Arg.Any<String>(), null, logLevel);
        }

        [Test]
        public void TestHandleError()
        {
            var logLevel = LogLevel.ERROR;
            var endPoint = "BookingDetail";
            var ex = new Exception();
            enigmaClientMetricProcessor.HandleError("clientName", ex, endPoint, null,null);
            exceptionMessage.Received().Send(Arg.Any<String>(), ex, logLevel);
        }
    }
}