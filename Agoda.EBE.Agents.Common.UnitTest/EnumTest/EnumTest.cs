using NUnit.Framework;

namespace Agoda.EBE.Agents.Common.UnitTest.EnumTest
{
    public class EnumTest
    {
        [Test]
        [TestCase(1, Enum.WhitelabelId.Agoda)]
        [TestCase(999, Enum.WhitelabelId.TestStrategicPartners)]
        [TestCase(-1, Enum.WhitelabelId.Agoda)]
        [TestCase(null, Enum.WhitelabelId.Agoda)]
        public void Whitelabel_GetWhitelabelId(int? whitelabelId, Enum.WhitelabelId whitelabelEnum)
        {
            Assert.AreEqual(Enum.GetWhitelabelId(whitelabelId), whitelabelEnum);
        }

        [Test]
        [TestCase((int)Enum.WhitelabelId.Agoda, false)]
        [TestCase((int)Enum.WhitelabelId.TestStrategicPartners, true)]
        [TestCase(-1, false)]
        [TestCase(null, false)]
        public void Whitelabel_IsSPWLWhitelabelGroup(int? whitelabelId, bool result)
        {
            Assert.AreEqual(Enum.IsSPWLGroup(Enum.GetWhitelabelId(whitelabelId)), result);
        }
        
        [Test]
        [TestCase(Enum.WhitelabelId.US_BANK, true)]
        [TestCase(Enum.WhitelabelId.TRAVEL_1_MY_REWARDS, true)]
        [TestCase(Enum.WhitelabelId.TRAVEL_2_MY_REWARDS, true)]
        [TestCase(Enum.WhitelabelId.TRAVEL_3_MY_REWARDS, true)]
        [TestCase(Enum.WhitelabelId.TRAVEL_4_MY_REWARDS, true)]
        [TestCase(Enum.WhitelabelId.US_BANK_UAT, true)]
        [TestCase(Enum.WhitelabelId.TRAVEL_1_MY_REWARDS_UAT, true)]
        [TestCase(Enum.WhitelabelId.TRAVEL_2_MY_REWARDS_UAT, true)]
        [TestCase(Enum.WhitelabelId.TRAVEL_3_MY_REWARDS_UAT, true)]
        [TestCase(Enum.WhitelabelId.TRAVEL_4_MY_REWARDS_UAT, true)]
        [TestCase(Enum.WhitelabelId.Agoda, false)]
        public void IsUsbankGroup_Categorizes_Correctly(Enum.WhitelabelId wlId, bool result)
        {
            Assert.AreEqual(Enum.IsUsBankGroup(wlId), result);
        }
        
    }
}