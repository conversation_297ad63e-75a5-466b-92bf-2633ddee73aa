using Agoda.EBE.Agents.Common.Configuration;
using Agoda.EBE.Agents.Common.Extension;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.RoundRobin;
using Agoda.WhiteLabelApi.Client;
using Autofac;
using NSubstitute;
using NUnit.Framework;
using IWhiteLabelExperimentManager = Agoda.WhiteLabelApi.Client.Services.IExperimentManager;

namespace Agoda.EBE.Agents.Common.UnitTest.Extension
{
    [TestFixture]
    [Category("ContainerBuilderExtensions")]
    public class ContainerBuilderExtensionsTests
    {
        [Test]
        public void RegisterWhiteLabelServices_ShouldRegisterAllServices()
        {
            // Arrange
            var builder = new ContainerBuilder();
            var serverSettings = new[] { new ServerSettings() };
            var whiteLabelClientConfig = new WhiteLabelClientConfiguration
            {
                TimeoutMilliseconds = 5000,
                ServerSettings = serverSettings
            };

            const string applicationName = "TestApp";
            const string defaultAdpApiKey = "apiKey";

            var messagingMock = Substitute.For<IMessaging>();
            builder.RegisterInstance(messagingMock).As<IMessaging>();

            // Act
            builder.RegisterWhiteLabelServices(whiteLabelClientConfig, applicationName, defaultAdpApiKey);
            var container = builder.Build();

            // Assert
            Assert.IsTrue(container.TryResolve(out IWhiteLabelClient whiteLabelClient));
            Assert.IsTrue(container.TryResolve(out IWhiteLabelExperimentManager experimentManager));
            Assert.IsTrue(container.TryResolve(out IWhiteLabelConfigService configService));
            Assert.IsTrue(container.TryResolve(out IWhiteLabelHelperService helperService));

            Assert.NotNull(whiteLabelClient);
            Assert.NotNull(experimentManager);
            Assert.NotNull(configService);
            Assert.NotNull(helperService);
        }
    }
}