﻿using System;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Common.UnitTest.Extension
{
    [TestFixture]
    [Category("StringExtension")]
    public class StringExtensionTest
    {
        [Test]
        [Parallelizable]
        public void RemoveAllAlphabet_InputIsMixingWithNumberAndAlphabet_ReturnStringContainOnlyNumber()
        {
            var input = "1a2b3cd4e5fghijk6l";
            var result = input.RemoveAllAlphabet();
            Assert.AreEqual("123456", result);
        }
        
        [Test]
        [Parallelizable]
        public void RemoveAllAlphabet_PassNullInput_ReturnEmptyString()
        {
            string input = null;
            var result = input.RemoveAllAlphabet();
            Assert.AreEqual(string.Empty, result);
        }
        
        [Test]
        [Parallelizable]
        public void RemoveAllAlphabet_PassEmptyStringInput_ReturnEmptyString()
        {
            var input = string.Empty;
            var result = input.RemoveAllAlphabet();
            Assert.AreEqual(string.Empty, result);
        }
    }
}
