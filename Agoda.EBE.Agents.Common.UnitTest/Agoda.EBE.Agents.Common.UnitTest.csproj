﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <IsPackable>false</IsPackable>
    <LangVersion>default</LangVersion>
    <Nullable>enable</Nullable>
    <RunSettingsFilePath>$(MSBuildProjectDirectory)/test.runsettings</RunSettingsFilePath>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Wrapper\**" />
    <Compile Include="Wrapper\UpcClientWebServiceConfigurableModeTest.cs" />
    <EmbeddedResource Remove="Wrapper\**" />
    <None Remove="Wrapper\**" />
    <None Update="TestData\BookingAction\PropertyBookingAction_Normal.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="TestData\BookingAction\PropertyBookingAction_WithProtoState.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Agoda.EBE.Framework" Version="1.1.0.2903" />
    <PackageReference Include="Agoda.Builds.Metrics" Version="1.0.113" />
    <PackageReference Include="Agoda.ExternalLoyaltyApi.Client" Version="24.12.1" />
    <PackageReference Include="Agoda.PayoutApi.Client.Generated" Version="0.82.3-RELEASE" />
    <PackageReference Include="Agoda.Payments.Upc.Integrate.Client" Version="1.0.8318.25625" />
    <PackageReference Include="Agoda.Upc.Structure" Version="2.0.7961.31976" />
    <PackageReference Include="coverlet.collector" Version="3.1.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="JunitXml.TestLogger" Version="3.0.110" />
    <PackageReference Include="Microsoft.CodeAnalysis.FxCopAnalyzers" Version="3.3.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="15.9.0" />
    <PackageReference Include="NSubstitute" Version="3.1.0" />
    <PackageReference Include="NUnit" Version="3.12.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="3.11.0" />
    <PackageReference Include="Roslynator.Analyzers" Version="4.12.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Shouldly" Version="3.0.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="TeamCity.VSTest.TestAdapter" Version="1.0.15" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Agoda.EBE.Agents.Common\Agoda.EBE.Agents.Common.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Messaging\EnigmaMetric\EnigmaClientMetricProcessorTest.cs">
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Remove="Util\Samples\ValidBookingState.json" />
    <EmbeddedResource Include="Util\Samples\ValidBookingState.json" />
  </ItemGroup>
</Project>
