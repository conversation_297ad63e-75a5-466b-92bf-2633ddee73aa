using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using NSubstitute;
using NUnit.Framework;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Service
{
    [TestFixture]
    public class WhiteLabelHelperServiceTest
    {
        private IMessaging _mockMessaging;
        private WhiteLabelHelperService _service;

        [SetUp]
        public void Setup()
        {
            _mockMessaging = Substitute.For<IMessaging>();
            _service = new WhiteLabelHelperService(_mockMessaging);
        }


        [Test]
        public void ResolveWhiteLabelId_WithWhitelabelId_ReturnsWhitelabelId()
        {
            const int bookingId = 1;
            const int whitelabelId = 2;
            var result = _service.ResolveWhiteLabelId(bookingId, whitelabelId);
            Assert.AreEqual(whitelabelId, result);
        }

        [Test]
        public void ResolveWhiteLabelId_Null_WhiteLabelId()
        {
            const int bookingId = 1;
            var result = _service.ResolveWhiteLabelId(bookingId);
            Assert.AreEqual((int)Enum.WhitelabelId.Agoda, result);
        }

        [Test]
        [TestCase(null, "")]
        [TestCase(1, "")]
        [TestCase(51, "5234834A-9346-47CE-BD5A-F2B754D3383E")]
        public void ResolveWhiteLabelToken_ReturnsExpectedValue(int? whitelabelId, string expectedResult)
        {
            var result = _service.ResolveWhiteLabelToken(whitelabelId);
            result.ShouldBe(expectedResult);
        }
    }
}