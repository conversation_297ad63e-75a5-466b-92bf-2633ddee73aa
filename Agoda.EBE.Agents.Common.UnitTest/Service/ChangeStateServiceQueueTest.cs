using System;
using Agoda.EBE.Agents.Common.Configuration.Interface;
using Agoda.EBE.Agents.Common.Service;
using Agoda.EBE.Framework.MQ.Interface;
using Agoda.EBE.Framework.MQ.MessageType;
using NSubstitute;
using NUnit.Framework;
using static Agoda.EBE.Agents.Common.UnitTest.Service.ChangeStateServiceMock;

namespace Agoda.EBE.Agents.Common.UnitTest.Service
{
    [TestFixture]
    public class ChangeStateServiceQueueTest
    {
        private ChangeStateServiceQueue _changeStateServiceQueue;
        private IRabbitMQAdapter _rabbitMqAdapter;
        private IOrchestratorConfiguration _configuration;

        [SetUp]
        public void SetUp()
        {
            _rabbitMqAdapter = Substitute.For<IRabbitMQAdapter>();
            _configuration = Substitute.For<IOrchestratorConfiguration>();

            _configuration.OrchestratorQueue.Returns("orchestratorQueue");
            _configuration.OrchestratorHistoryQueue.Returns("orchestratorHistoryQueue");
        }

        [Test]
        public void ChangeBookingState_Response_Success()
        {
            _changeStateServiceQueue = new ChangeStateServiceQueue(_rabbitMqAdapter, _configuration);
            var mockChangeStateRequest = GetChangeBookingStateRequest();
            var changeBookingStateResponse =
                _changeStateServiceQueue.ChangeBookingState(mockChangeStateRequest);
            Assert.NotNull(changeBookingStateResponse);
            Assert.AreEqual(true, changeBookingStateResponse.IsSuccess);
            _rabbitMqAdapter.Received(1).PublishMessage(Arg.Is<OrchestratorTask>(x => 
                    x.SourceComponent == "NetCoreAgent" &&
                    x.IsPartialTransfer == mockChangeStateRequest.IsPartialTransfer &&
                    x.IsSyncTransfer == mockChangeStateRequest.IsSyncTransfer), Arg.Is<string>(x => x == "orchestratorQueue"), Arg.Is<int>(0), Arg.Is<int>(0));
        }
        
        [Test]
        public void ChangeBookingState_Response_Failure()
        {
            _rabbitMqAdapter
                .When(q => q.PublishMessage(Arg.Is<OrchestratorTask>(x => x.SourceComponent == "NetCoreAgent"),
                    Arg.Is<string>(x => x == "orchestratorQueue"), Arg.Is<int>(0), Arg.Is<int>(0)))
                .Do(q => throw new Exception("cannot publish message"));
            _changeStateServiceQueue = new ChangeStateServiceQueue(_rabbitMqAdapter, _configuration);
            Assert.Throws(typeof(Exception),
                () => _changeStateServiceQueue.ChangeBookingState(GetChangeBookingStateRequest()));
        }

        [Test]
        public void InsertBookingHistory_Response_Success()
        {
            _rabbitMqAdapter.PublishMessage(Arg.Any<OrchestratorTask>(), Arg.Any<string>());
            _changeStateServiceQueue = new ChangeStateServiceQueue(_rabbitMqAdapter, _configuration);
            var insertBookingHistoryResponse =
                _changeStateServiceQueue.InsertBookingHistory(GetInsertBookingHistoryRequest());
            Assert.NotNull(insertBookingHistoryResponse);
            Assert.AreEqual(true, insertBookingHistoryResponse.IsSuccess);
        }

        [Test]
        public void InsertBookingHistory_Response_Failure()
        {
            _rabbitMqAdapter
                .When(q => q.PublishMessage(Arg.Any<OrchestratorTask>(), Arg.Any<string>(), Arg.Is<int>(0), Arg.Is<int>(0)))
                .Do(q => throw new Exception("cannot publish message"));
            _changeStateServiceQueue = new ChangeStateServiceQueue(_rabbitMqAdapter, _configuration);
            Assert.Throws(typeof(Exception),
                () => _changeStateServiceQueue.InsertBookingHistory(GetInsertBookingHistoryRequest()));
        }
    }
}