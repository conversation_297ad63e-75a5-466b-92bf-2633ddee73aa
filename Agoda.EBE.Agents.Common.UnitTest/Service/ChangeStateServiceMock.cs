using System;
using Agoda.EBE.Agents.Common.Request;
using Agoda.EBE.Workflow.Structure;
using Agoda.EBE.Workflow.Structure.Request;

namespace Agoda.EBE.Agents.Common.UnitTest.Service
{
    public static class ChangeStateServiceMock
    {
        public static CommonChangeBookingStateRequest GetChangeBookingStateRequest()
        {
            var request = new CommonChangeBookingStateRequest()
            {
                ItineraryID = 1,
                BookingID = 1,
                WorkflowActionResultID = 1,
                WorkflowReasonID = 1,
                UserID = Guid.NewGuid(),
                Remark = "remark",
                NodeIdentifier = "NORMAL-190",
                IsPartialTransfer = true,
                IsSyncTransfer = true,
                OperationId = 1
            };
            return request;
        }

        public static InsertBookingHistoryRequest GetInsertBookingHistoryRequest()
        {
            var request = new InsertBookingHistoryRequest()
            {
                BookingId = 1,
                WorkflowActionResultId = 1,
                WorkflowReasonId = 1,
                UserId = Guid.NewGuid(),
                Remark = "remark"
            };
            return request;
        }
    }
}