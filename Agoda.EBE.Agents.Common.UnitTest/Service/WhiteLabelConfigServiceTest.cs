using System;
using System.Collections.Generic;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.EBE.Framework.Messaging;
using Agoda.WhiteLabelApi.Client;
using Agoda.WhiteLabelApi.Client.Models;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using NUnit.Framework;
using Shouldly;
using IWhiteLabelExperimentManager = Agoda.WhiteLabelApi.Client.Services.IExperimentManager;

namespace Agoda.EBE.Agents.Common.UnitTest.Service
{
    [TestFixture]
    public class WhiteLabelConfigServiceTest
    {
        private  IWhiteLabelClient _whiteLabelClient;
        private  IWhiteLabelExperimentManager _whiteLabelExperimentManager;
        private  IMessaging _messaging;

        [SetUp]
        public void SetUp()
        {
            _whiteLabelClient = Substitute.For<IWhiteLabelClient>();
            _whiteLabelExperimentManager = Substitute.For<IWhiteLabelExperimentManager>();
            _messaging = Substitute.For<IMessaging>();
        }
        
        [Test]
        [TestCase("mockNULLWlToken")]
        [TestCase("mockEmptyWlToken")]
        [TestCase("mockNormalWlToken")]
        public void GetMPBEConfigByKey_ReturnExpectedValue(string mockWlToken)
        {
            IWhiteLabelConfigService service =
                new WhiteLabelConfigService(_whiteLabelClient, _whiteLabelExperimentManager, _messaging);
            var measurement = Substitute.For<IEBEMeasurementMessage>();
            _messaging.MeasurementMessageFactory.CreateNewMeasurement().Returns(measurement);
            int mockBid = 1234;

            MPBE mockNullMPBE = null;

            MPBE mockEmptyMPBE = new MPBE();
            
            MPBE mockNormalMPBE = new MPBE();
            mockNormalMPBE.IsRetrieveCcInfoAfterFraud = true;
            mockNormalMPBE.IsProductLevelSettlement = true;
            mockNormalMPBE.IsSkipValidate3DS = true;
            mockNormalMPBE.IsSkipFilterCCOFByPaymentMethod = true;
            mockNormalMPBE.IsUseUPI = true;
            mockNormalMPBE.HideAvailablePaymentMethods = true;
            mockNormalMPBE.IsEnableVehicleEmail = true;
            mockNormalMPBE.IsDisableDelaySettlement = true;
            mockNormalMPBE.IsUseNonUpiCartEmail = true;
            mockNormalMPBE.IsForceTestBooking = true;
            mockNormalMPBE.IsSupportManualBox = true;
                
            _whiteLabelClient.GetFeatureConfigByKey<MPBE>(Arg.Any<string>(),Arg.Any<IWhiteLabelExperimentManager>(), "mockNULLWlToken",Arg.Any<string>(),Arg.Any<int>()).Returns(mockNullMPBE);
            _whiteLabelClient.GetFeatureConfigByKey<MPBE>(Arg.Any<string>(),Arg.Any<IWhiteLabelExperimentManager>(), "mockEmptyWlToken",Arg.Any<string>(),Arg.Any<int>()).Returns(mockEmptyMPBE);
            _whiteLabelClient.GetFeatureConfigByKey<MPBE>(Arg.Any<string>(),Arg.Any<IWhiteLabelExperimentManager>(), "mockNormalWlToken",Arg.Any<string>(),Arg.Any<int>()).Returns(mockNormalMPBE);


            MPBE response = service.GetMPBEConfigByKey(mockWlToken, mockBid, false);

            if (mockWlToken == "mockNULLWlToken" || mockWlToken == "mockEmptyWlToken")
            {
                response.IsRetrieveCcInfoAfterFraud.ShouldBeNull();
                response.IsProductLevelSettlement.ShouldBeNull();
                response.IsSkipValidate3DS.ShouldBeNull();
                response.IsSkipFilterCCOFByPaymentMethod.ShouldBeNull();
                response.IsUseUPI.ShouldBeNull();
                response.HideAvailablePaymentMethods.ShouldBeNull();
                response.IsEnableVehicleEmail.ShouldBeNull();
                response.IsDisableDelaySettlement.ShouldBeNull();
                response.IsUseNonUpiCartEmail.ShouldBeNull();
                response.IsForceTestBooking.ShouldBeNull();
                response.IsSupportManualBox.ShouldBeNull();
            }
            else
            {
                response.IsRetrieveCcInfoAfterFraud.ShouldBe(true);
                response.IsProductLevelSettlement.ShouldBe(true);
                response.IsSkipValidate3DS.ShouldBe(true);
                response.IsSkipFilterCCOFByPaymentMethod.ShouldBe(true);
                response.IsUseUPI.ShouldBe(true);
                response.HideAvailablePaymentMethods.ShouldBe(true);
                response.IsEnableVehicleEmail.ShouldBe(true);
                response.IsDisableDelaySettlement.ShouldBe(true);
                response.IsUseNonUpiCartEmail.ShouldBe(true);
                response.IsForceTestBooking.ShouldBe(true);
                response.IsSupportManualBox.ShouldBe(true);
            }

            _messaging.MeasurementMessageFactory.Received().CreateNewMeasurement();
            measurement.Received().BeginTrack();
            measurement.Received().EndTrack(Enum.WhitelabelClientMeasurement.GetFeatureConfigByKey, true);
        }

        [Test]
        [TestCase(1234, "mockWlKey", true)]
        [TestCase(5678, null, false)]
        public void GetWhitelabelKey_ShouldReturnExpectedResult(int whiteLabelId, string expectedKey, bool shouldSucceed)
        {
            IWhiteLabelConfigService service =
                new WhiteLabelConfigService(_whiteLabelClient, _whiteLabelExperimentManager, _messaging);

            var measurement = Substitute.For<IEBEMeasurementMessage>();
            _messaging.MeasurementMessageFactory.CreateNewMeasurement().Returns(measurement);

            if (shouldSucceed)
            {
                _whiteLabelClient.GetWhiteLabelKey(whiteLabelId).Returns(expectedKey);
            }
            else
            {
                _whiteLabelClient.GetWhiteLabelKey(whiteLabelId).Throws(new Exception("Mock exception"));
            }

            var result = service.GetWhiteLabelKey(whiteLabelId);

            if (shouldSucceed)
            {
                Assert.AreEqual(expectedKey, result);
            }
            else
            {
                Assert.IsNull(result);
                _messaging.ExceptionMessage.Received().Send(
                    Arg.Is<string>(s => s.Contains($"WhiteLabelClient.GetWhiteLabelKey returns Null Key: whiteLabelId: {whiteLabelId}")),
                    Arg.Is<Exception>(ex => ex.Message == "Mock exception"),
                    LogLevel.WARN);
            }

            _messaging.MeasurementMessageFactory.Received().CreateNewMeasurement();
            measurement.Received().BeginTrack();
            measurement.Received().EndTrack(Enum.WhitelabelClientMeasurement.GetWhiteLabelKey, shouldSucceed);
        }
        
        [Test]
        [TestCase("upc", true)]
        [TestCase("fraud", false)]
        public void DisableAgent_ReturnExpectedValue(string agentName, bool result)
        {
            var disableAgents = new List<string> {"upc", "processPayment", "emailCustomer", "emailHotel"};
            IWhiteLabelConfigService service =
                new WhiteLabelConfigService(_whiteLabelClient, _whiteLabelExperimentManager, _messaging);
            BookingWorkflow bookingWorkflowConfig = new BookingWorkflow(disableAgents, null, null, null);
            bool response = service.DisableAgent(bookingWorkflowConfig, agentName);

            response.ShouldBe(result);
        }

        [Test]
        [TestCase("mockNullBookingWorkflow", 332, false)]
        [TestCase("mockNullMainDisableAgentBookingWorkflow",332, false)]
        [TestCase("mockNullDmcControlSettings",332, true)]
        [TestCase("mockDefaultDmcControlSettings",332, true)]
        [TestCase("mockYcsDmcControlSettings", 332,false)]
        [TestCase("mockWithoutDefaultDmcControlSettings",332, false)]
        [TestCase("mockNullBookingWorkflow", 29014, false)]
        [TestCase("mockNullMainDisableAgentBookingWorkflow",29014, false)]
        [TestCase("mockNullDmcControlSettings",29014, true)]
        [TestCase("mockDefaultDmcControlSettings",29014, true)]
        [TestCase("mockYcsDmcControlSettings", 29014,true)]
        [TestCase("mockWithoutDefaultDmcControlSettings",29014, true)]
        public void IsDisabledAgentWithDmc_ReturnExpectedValue(string mockBookingWorkflowFeature, int dmcId, bool result)
        {
            BookingWorkflow bookingWorkflowConfig = getBookingWorkflowMock(mockBookingWorkflowFeature);
            IWhiteLabelConfigService service =
                new WhiteLabelConfigService(_whiteLabelClient, _whiteLabelExperimentManager, _messaging);
            bool response = service.IsDisabledAgentWithDmc(bookingWorkflowConfig, "upc", dmcId);

            response.ShouldBe(result);
        }

        [Test]
        [TestCase("matchDmcWithFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSupportSupplierMember, true)]
        [TestCase("matchDmcWithFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSimulateCancelSuccessAndAcknowledge, true)]
        [TestCase("matchDmcWithFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessBookingSupplierData, true)]
        [TestCase("matchDmcWithFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessSupplierPaymentGatewayData, true)]
        [TestCase("matchDmcWithoutFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSupportSupplierMember, false)]
        [TestCase("matchDmcWithoutFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSimulateCancelSuccessAndAcknowledge, false)]
        [TestCase("matchDmcWithoutFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessBookingSupplierData, false)]
        [TestCase("matchDmcWithoutFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessSupplierPaymentGatewayData, false)]
        [TestCase("defaultDmcWithFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSupportSupplierMember, true)]
        [TestCase("defaultDmcWithFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSimulateCancelSuccessAndAcknowledge, true)]
        [TestCase("defaultDmcWithFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessBookingSupplierData, true)]
        [TestCase("defaultDmcWithFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessSupplierPaymentGatewayData, true)]
        [TestCase("defaultDmcWithoutFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSupportSupplierMember, false)]
        [TestCase("defaultDmcWithoutFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSimulateCancelSuccessAndAcknowledge, false)]
        [TestCase("defaultDmcWithoutFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessBookingSupplierData, false)]
        [TestCase("defaultDmcWithoutFlagValue", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessSupplierPaymentGatewayData, false)]
        [TestCase("bookingWorkflowWithoutDmcControlSettings", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSupportSupplierMember, false)]
        [TestCase("bookingWorkflowWithoutDmcControlSettings", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSimulateCancelSuccessAndAcknowledge, false)]
        [TestCase("bookingWorkflowWithoutDmcControlSettings", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessBookingSupplierData, false)]
        [TestCase("bookingWorkflowWithoutDmcControlSettings", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessSupplierPaymentGatewayData, false)]
        [TestCase("", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSupportSupplierMember, false)]
        [TestCase("", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSimulateCancelSuccessAndAcknowledge, false)]
        [TestCase("", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessBookingSupplierData, false)]
        [TestCase("", 332, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessSupplierPaymentGatewayData, false)]
        public void IsBookingWorkflowByDmcEnableFlag_ReturnExpectedValue(string mockCase, int dmcId, Enum.WhiteLabelBookingWorkflowByDmcFlag flagName, bool result)
        {
            BookingWorkflow bookingWorkflowConfig = getBookingWorkflowForDmcEnableFlagMock(mockCase);
            IWhiteLabelConfigService service =
                new WhiteLabelConfigService(_whiteLabelClient, _whiteLabelExperimentManager, _messaging);
            bool response = service.IsBookingWorkflowByDmcEnableFlag(bookingWorkflowConfig, dmcId, flagName);
            
            response.ShouldBe(result);
        }

        private BookingWorkflow getBookingWorkflowMock(string mockBookingWorkflowFeature)
        {
            var disableAgents = new List<string> {"upc", "processPayment", "emailCustomer", "emailHotel"};
            var defaultDmcControlSettingsModel = new DmcControlSettingModel(null, disableAgents, null, null);
            var definedDmcControlSettingsModel = new DmcControlSettingModel(332, new List<string> {"processPayment", "emailCustomer"}, null, null);

            switch(mockBookingWorkflowFeature) 
            {
                case "mockNullBookingWorkflow":
                    return null;
                case "mockNullMainDisableAgentBookingWorkflow":
                    return new BookingWorkflow(null, null, null, null);
                case "mockNullDmcControlSettings":
                    return new BookingWorkflow(disableAgents, null, null, null);
                case "mockDefaultDmcControlSettings":
                    return new BookingWorkflow(disableAgents, null, null, null, new List<DmcControlSettingModel> {defaultDmcControlSettingsModel});
                case "mockWithoutDefaultDmcControlSettings":
                    return new BookingWorkflow(disableAgents, null, null, null, new List<DmcControlSettingModel> {definedDmcControlSettingsModel});
                default: //mockYcsDmcControlSettings
                    return new BookingWorkflow(null, null, null, null, new List<DmcControlSettingModel> {defaultDmcControlSettingsModel, definedDmcControlSettingsModel});
            }
        }

        private BookingWorkflow getBookingWorkflowForDmcEnableFlagMock(string mockCase)
        {
            switch (mockCase)
            {
                case "matchDmcWithFlagValue":
                    return new BookingWorkflow(null, null, null, null, new List<DmcControlSettingModel>
                    {
                        new DmcControlSettingModel(null,
                            null,
                            null,
                            null,
                            true,
                            true,
                            true,
                            true),
                        new DmcControlSettingModel(332,
                            null,
                            null,
                            null,
                            true,
                            true,
                            true,
                            true,
                            null,
                            null,
                            true),
                        new DmcControlSettingModel(29014,
                            null,
                            null,
                            null,
                            true,
                            true,
                            true,
                            true,
                            null,
                            null,
                            true)
                    });
                case "matchDmcWithoutFlagValue":
                    return new BookingWorkflow(null, null, null, null, new List<DmcControlSettingModel>
                    {
                        new DmcControlSettingModel(null,
                            null,
                            null,
                            null,
                            true,
                            true,
                            true,
                            true),
                        new DmcControlSettingModel(332,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null)
                    });
                case "defaultDmcWithFlagValue":
                    return new BookingWorkflow(null, null, null, null, new List<DmcControlSettingModel>
                    {
                        new DmcControlSettingModel(null,
                            null,
                            null,
                            null,
                            true,
                            true,
                            true,
                            true,
                            null,
                            null,
                            true)
                    });
                case "defaultDmcWithoutFlagValue":
                    return new BookingWorkflow(null, null, null, null, new List<DmcControlSettingModel>
                    {
                        new DmcControlSettingModel(null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null)
                    });
                case "jtbDmcWithFlagValue":
                    return new BookingWorkflow(null, null, null, null, new List<DmcControlSettingModel>
                    {
                        new (dmcId: 332,
                            isJtbDmcBooking: false),
                        new (dmcId: 29014,
                            isJtbDmcBooking: true)
                    });
                case "bookingWorkflowWithoutDmcControlSettings":
                    return new BookingWorkflow(null, null, null, null);
                default: //null bookingWorkflow
                    return null;
            }
        }

        [Test]
        [TestCase(2, "A", 29014)]
        [TestCase(2, "B", 29014)]
        [TestCase(3, "A", 29014)]
        [TestCase(3, "B", 29014)]
        [TestCase(4, "A", 29014)]
        [TestCase(4, "B", 29014)]
        public void VerifyJtbGenericFeature(int whitelabelId, string experiment, int dmcId)
        {
            IWhiteLabelConfigService service =
                new WhiteLabelConfigService(_whiteLabelClient, _whiteLabelExperimentManager, _messaging);

            _whiteLabelClient.GetFeatureConfigByKey<BookingWorkflow>(Arg.Any<string>(),
                Arg.Any<IWhiteLabelExperimentManager>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<int>()).Returns(getBookingWorkflowForDmcEnableFlagMock("matchDmcWithFlagValue"));
            
            _whiteLabelClient.GetWhiteLabelKey(Arg.Any<int>()).Returns(Guid.NewGuid().ToString());
            
            _whiteLabelClient
                .IsFeatureEnabled(Arg.Any<string>(),  Arg.Any<Guid>(),  Arg.Any<string>(),
                   Arg.Any<int>(), Arg.Any<IWhiteLabelExperimentManager>())
                .Returns(true);
            
            _whiteLabelClient
                .IsFeatureEnabled(Arg.Any<string>(),
                    Arg.Any<IWhiteLabelExperimentManager>(),
                    Arg.Any<int>(),Arg.Any<string>(), Arg.Any<int>())
                .Returns(true);

            service.IsJtbDmcBooking(whitelabelId, false, dmcId).ShouldBe(true);

            switch (whitelabelId)
            {
                case (int)Enum.WhitelabelId.Jtb:
                    service.IsJtbWl(whitelabelId, false)
                        .ShouldBe(true);
                    break;
                case (int)Enum.WhitelabelId.Japanican:
                    service.IsJapanicanWl(whitelabelId, false)
                        .ShouldBe(true);
                    break;
                case (int)Enum.WhitelabelId.Rurubu:
                    service.IsRurubuWl(whitelabelId, false)
                        .ShouldBe(true);
                    break;
                default:
                    Assert.Fail();
                    break;
            }
        }

        [Test]
        [TestCase(4, "A", 29014)]
        [TestCase(4, "B", 29014)]
        [TestCase(4, "A", 332)]
        [TestCase(4, "B", 332)]
        public void VerifyRurubuWhiteLabel(int whitelabelId, string experiment, int dmcId)
        {
            IWhiteLabelConfigService service =
                new WhiteLabelConfigService(_whiteLabelClient, _whiteLabelExperimentManager, _messaging);

            _whiteLabelClient
                .GetFeatureConfigByKey<BookingWorkflow>(Arg.Any<string>(), Arg.Any<IWhiteLabelExperimentManager>(),
                    Arg.Any<string>(), Arg.Any<string>(), Arg.Any<int>())
                .Returns(getBookingWorkflowForDmcEnableFlagMock("jtbDmcWithFlagValue"));

            _whiteLabelClient.GetWhiteLabelKey(Arg.Any<int>()).Returns(Guid.NewGuid().ToString());

            _whiteLabelClient
                .IsFeatureEnabled(Arg.Any<string>(), Arg.Any<Guid>(), Arg.Any<string>(), Arg.Any<int>(),
                    Arg.Any<IWhiteLabelExperimentManager>())
                .Returns(true);

            service.IsRurubuWl(whitelabelId, false)
                .ShouldBe(true);
            
            service.IsRurubuDomestic(whitelabelId, false, dmcId).ShouldBe(dmcId == (int)Enum.DmcId.JTB);
        }

    }
}
