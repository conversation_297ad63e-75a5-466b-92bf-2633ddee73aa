using System;
using System.Collections.Generic;
using Agoda.EBE.Agents.Common.DataAccess.Interface;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Model;
using Agoda.EBE.Agents.Common.Request;
using Agoda.EBE.Agents.Common.Service;
using Agoda.EBE.Agents.Common.Util;
using Agoda.EBE.Framework.ClientBase;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.MQ.MessageType;
using Agoda.EBE.Framework.Object;
using Agoda.EBE.Framework.Objects;
using Agoda.EBE.Workflow.Client;
using Agoda.EBE.Workflow.Structure;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using NUnit.Framework;
using static Agoda.EBE.Agents.Common.UnitTest.Service.ChangeStateServiceMock;

namespace Agoda.EBE.Agents.Common.UnitTest.Service
{
    [TestFixture]
    public class DeduplicationServiceTest
    {
        private IDeduplicationService _deduplicationService;
        private IChangeStateService _changeStateService;
        private IBookingActionUtils _bookingActionUtils;
        private IBFDBDataAccess _bfdbDataAccess;
        private IMessaging _messaging;
        private IEBEExceptionMessage _exceptionMock;

        [SetUp]
        public void SetUp()
        {
            _changeStateService = Substitute.For<IChangeStateService>();
            _bookingActionUtils = Substitute.For<IBookingActionUtils>();
            _bfdbDataAccess = Substitute.For<IBFDBDataAccess>();
            _messaging = Substitute.For<IMessaging>();
            _exceptionMock = Substitute.For<IEBEExceptionMessage>();
            _messaging.ExceptionMessage.Returns(_exceptionMock);
            _deduplicationService = new DeduplicationService(_changeStateService, _bookingActionUtils, _bfdbDataAccess, _messaging);
            _changeStateService.ChangeBookingState(Arg.Any<CommonChangeBookingStateRequest>()).Returns(new ChangeBookingStateResponse(){IsSuccess = true});
        }

        [Test]
        public void PreProcess_FoundAgentResult_CallChangeStateWithExistingResult()
        {
            var itineraryId = 111;
            var bookingId = 222;
            var mockNodeIdentifier1 = "MockNodeIdentifier1";
            var mockNodeIdentifier2 = "MockNodeIdentifier2";
            var mockAgentResult1 = new Result
            {
                Code = 998,
                Message = "mock remark",
                Status = 0
            };
            var mockAgentResult2 = new Result
            {
                Code = 1,
                Message = "mock remark",
                Status = 0
            };
            var mockAgentResultMap = new Dictionary<string, Result>();
            mockAgentResultMap.Add(mockNodeIdentifier1, mockAgentResult1);
            mockAgentResultMap.Add(mockNodeIdentifier2, mockAgentResult2);
            var propertyBookingAction = new BookingActionWithState();
            propertyBookingAction.ItineraryId = itineraryId;
            propertyBookingAction.BookingId = bookingId;
            var bookingActionState = new BookingActionState();
            bookingActionState.AgentResultMap = mockAgentResultMap;
            propertyBookingAction.State = bookingActionState;
            _bookingActionUtils.GetBookingAction(Arg.Is<int>(bookingId), Arg.Any<String>()).Returns(propertyBookingAction);

            var message = new DefaultMessage
            {
                BookingId = bookingId,
                NodeIdentifier = mockNodeIdentifier1
            };

            var preProcessResponse = _deduplicationService.preProcess(message, "mockAgentName");
            Assert.AreEqual(true, preProcessResponse.IsExistingAgentResultFound);
            Assert.AreEqual(true, preProcessResponse.ChangeBookingStateResponse.IsSuccess);
            _changeStateService.Received(1).ChangeBookingState(Arg.Is<CommonChangeBookingStateRequest>(x => 
                x.ItineraryID == itineraryId &&
                x.BookingID == bookingId &&
                x.WorkflowActionResultID == mockAgentResult1.Code &&
                x.NodeIdentifier == mockNodeIdentifier1 &&
                x.Remark == mockAgentResult1.Message));
        }
        
        [Test]
        public void PreProcess_NotFoundExistingAgentResult()
        {
            var itineraryId = 111;
            var bookingId = 222;
            var mockAgentResultMap = new Dictionary<string, Result>();
            var propertyBookingAction = new BookingActionWithState();
            propertyBookingAction.ItineraryId = itineraryId;
            propertyBookingAction.BookingId = bookingId;
            var bookingActionState = new BookingActionState();
            bookingActionState.AgentResultMap = mockAgentResultMap;
            propertyBookingAction.State = bookingActionState;
            _bookingActionUtils.GetBookingAction(Arg.Is<int>(bookingId), Arg.Any<String>()).Returns(propertyBookingAction);

            var message = new DefaultMessage
            {
                BookingId = bookingId,
                NodeIdentifier = "random node identifier"
            };

            var preProcessResponse = _deduplicationService.preProcess(message, "mockAgentName");
            Assert.AreEqual(false, preProcessResponse.IsExistingAgentResultFound);
            Assert.IsNull(preProcessResponse.ChangeBookingStateResponse);
            _changeStateService.Received(0).ChangeBookingState(Arg.Any<CommonChangeBookingStateRequest>());
        }
        
        [Test]
        public void PreProcess_UnableToFindBookingAction()
        {
            var bookingId = 222;
            var mockNodeIdentifier = "random node identifier";

            var message = new DefaultMessage
            {
                BookingId = bookingId,
                NodeIdentifier = mockNodeIdentifier
            };

            var preProcessResponse = _deduplicationService.preProcess(message, "mockAgentName");
            
            Assert.AreEqual(false, preProcessResponse.IsExistingAgentResultFound);
            Assert.IsNull(preProcessResponse.ChangeBookingStateResponse);
            _changeStateService.Received(0).ChangeBookingState(Arg.Any<CommonChangeBookingStateRequest>());
        }
        
        [Test]
        public void PostProcess_UpdateAgentResultToBookingActionStateSuccessfully()
        {
            var itineraryId = 111;
            var bookingId = 222;
            var mockWorkflowActionResult = 998;
            var mockRemarkMessage = "mock remark message";
            var mockNodeIdentifier = "random node identifier";
            var mockAgentResultMap = new Dictionary<string, Result>();
            var propertyBookingAction = new BookingActionWithState();
            propertyBookingAction.ItineraryId = itineraryId;
            propertyBookingAction.BookingId = bookingId;
            var bookingActionState = new BookingActionState();
            bookingActionState.AgentResultMap = mockAgentResultMap;
            propertyBookingAction.State = bookingActionState;
            _bookingActionUtils.GetBookingAction(Arg.Is<int>(bookingId), Arg.Any<String>()).Returns(propertyBookingAction);

            var message = new DefaultMessage
            {
                BookingId = bookingId,
                NodeIdentifier = mockNodeIdentifier
            };

            _deduplicationService.postProcess(message, "mockAgentName", mockWorkflowActionResult, mockRemarkMessage);
            
            _bfdbDataAccess.Received(1).UpdateBookingActionState(Arg.Any<long>(), Arg.Is<BookingActionState>(x =>
                x.AgentResultMap[mockNodeIdentifier].Code == mockWorkflowActionResult &&
                x.AgentResultMap[mockNodeIdentifier].Message == mockRemarkMessage));
        }
        
        [Test]
        public void PostProcess_UnableToFindBookingAction()
        {
            var bookingId = 222;
            var mockWorkflowActionResult = 998;
            var mockRemarkMessage = "mock remark message";
            var mockNodeIdentifier = "random node identifier";

            var message = new DefaultMessage
            {
                BookingId = bookingId,
                NodeIdentifier = mockNodeIdentifier
            };

            _deduplicationService.postProcess(message, "mockAgentName", mockWorkflowActionResult, mockRemarkMessage);
            _bfdbDataAccess.Received(0).UpdateBookingActionState(Arg.Any<long>(), Arg.Any<BookingActionState>());
        }
    }
}