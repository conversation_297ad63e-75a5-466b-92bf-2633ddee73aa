using System;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Service;
using Agoda.EBE.Framework.ClientBase;
using Agoda.EBE.Workflow.Client;
using Agoda.EBE.Workflow.Structure;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using NUnit.Framework;
using static Agoda.EBE.Agents.Common.UnitTest.Service.ChangeStateServiceMock;

namespace Agoda.EBE.Agents.Common.UnitTest.Service
{
    [TestFixture]
    public class ChangeStateServiceAPITest
    {
        private ChangeStateServiceAPI _changeStateServiceApi;
        private IWorkflowServiceClient _workflowServiceClient;
        private IMessaging _messaging;

        [SetUp]
        public void SetUp()
        {
            _workflowServiceClient = Substitute.For<IWorkflowServiceClient>();
            _messaging = Substitute.For<IMessaging>();
            _changeStateServiceApi = new ChangeStateServiceAPI(_workflowServiceClient, _messaging);
        }

        [Test]
        public void ChangeBookingState_ApiResponse_Success()
        {
            var response = new ChangeBookingStateResponse
            {
                IsSuccess = true, Status = ConstantEnum.Status.Okay
            };
            var request = GetChangeBookingStateRequest();
            _workflowServiceClient.ChangeBookingState(Arg.Any<ChangeBookingStateRequest>()).Returns(response);

            var changeBookingStateResponse = _changeStateServiceApi.ChangeBookingState(request);
            Assert.AreEqual(true, changeBookingStateResponse.IsSuccess);
            Assert.AreEqual(ConstantEnum.Status.Okay, changeBookingStateResponse.Status);
        }

        [Test]
        public void ChangeBookingState_ApiResponse_Failure()
        {
            var request = GetChangeBookingStateRequest();
            _workflowServiceClient.ChangeBookingState(Arg.Any<ChangeBookingStateRequest>())
                .Throws(new Exception("workflow service client not available"));

            Assert.Throws(typeof(Exception), () => _changeStateServiceApi.ChangeBookingState(request));
        }

        [Test]
        public void InsertBookingHistory_ApiResponse_Success()
        {
            var response = new InsertBookingHistoryResponse {IsSuccess = true};
            var request = GetInsertBookingHistoryRequest();
            _workflowServiceClient.InsertBookingHistory(request).Returns(response);

            var insertBookingHistoryResponse = _changeStateServiceApi.InsertBookingHistory(request);
            Assert.AreEqual(true, insertBookingHistoryResponse.IsSuccess);
        }

        [Test]
        public void InsertBookingHistory_ApiResponse_Failure()
        {
            var request = GetInsertBookingHistoryRequest();
            _workflowServiceClient.InsertBookingHistory(request)
                .Throws(new Exception("workflow service client not available"));

            Assert.Throws(typeof(Exception), () => _changeStateServiceApi.InsertBookingHistory(request));
        }
    }
}