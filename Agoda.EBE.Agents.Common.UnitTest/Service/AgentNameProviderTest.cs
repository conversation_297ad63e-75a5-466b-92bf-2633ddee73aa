﻿using Agoda.EBE.Agents.Common.Service;
using Agoda.EBE.Agents.Common.Util;
using NSubstitute;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Common.UnitTest.Service
{
    public class AgentNameProviderTest
    {
        [Test]
        [TestCase("UPC_PROVISIONING", "UPC_PROVISIONING", "50004")]
        [TestCase("UPC_PROVISIONING", "UPC_PROVISIONING", "")]
        [TestCase("UPC_PROVISIONING", "UPC_PROVISIONING", null)]
        public void GetAgentNamesCmdLineArg_ValidAgentName(string expected, string agentNames, string port)
        {
            var actual = AgentNameProvider.GetAgentNamesCmdLineArg(new string[] { agentNames, port });
            Assert.AreEqual(expected, actual);
        }

        [Test]
        [TestCase("", "50004")]
        [TestCase(null, "50004")]
        [TestCase(null, "")]
        [TestCase(null, null)]
        public void GetAgentNamesCmdLineArg_InvalidAgentName(string agentNames, string port)
        {
            var actual = AgentNameProvider.GetAgentNamesCmdLineArg(new string[] { agentNames, port });
            Assert.AreEqual(string.Empty, actual);
        }

        [Test]
        [TestCase("UPC_PROVISIONING", "UPC_PROVISIONING", "50004")]
        [TestCase("UPC_PROVISIONING", "UPC_PROVISIONING", "")]
        [TestCase("UPC_PROVISIONING", "UPC_PROVISIONING", null)]
        public void GetAgentNamesCmdLineArg_Cli_ValidAgentName(string expected, string agentNames, string port)
        {
            var cli = Substitute.For<ICommandLineInterface>();
            cli.GetCommandLineArguments().Returns(new string[] { "", agentNames, port });
            var actual = AgentNameProvider.GetAgentNamesCmdLineArg(cli);
            Assert.AreEqual(expected, actual);
        }

        [Test]
        [TestCase("", "50004")]
        [TestCase(null, "50004")]
        [TestCase(null, "")]
        [TestCase(null, null)]
        public void GetAgentNamesCmdLineArg_Cli_InvalidAgentName(string agentNames, string port)
        {
            var cli = Substitute.For<ICommandLineInterface>();
            cli.GetCommandLineArguments().Returns(new string[] { "", agentNames, port });
            var actual = AgentNameProvider.GetAgentNamesCmdLineArg(cli);
            Assert.AreEqual(string.Empty, actual);
        }

        [Test]
        [TestCase("ebe_UPC_PROVISIONING_agent", "UPC_PROVISIONING", "50004")]
        [TestCase("ebe_UPC_PROVISIONING_agent", "UPC_PROVISIONING", "")]
        [TestCase("ebe_UPC_PROVISIONING_agent", "UPC_PROVISIONING", null)]
        public void GetAppNameFromAgentNames_ValidAgentName(string expected, string agentNames, string port)
        {
            var actual = AgentNameProvider.GetAppNameFromAgentNames(new string[] { agentNames, port });
            Assert.AreEqual(expected, actual);
        }

        [Test]
        [TestCase("", "50004")]
        [TestCase(null, "50004")]
        [TestCase(null, "")]
        [TestCase(null, null)]
        public void GetAppNameFromAgentNames_InvalidAgentName(string agentNames, string port)
        {
            var actual = AgentNameProvider.GetAppNameFromAgentNames(new string[] { agentNames, port });
            Assert.AreEqual("ebe_agent", actual);
        }

        [Test]
        [TestCase("ebe_UPC_PROVISIONING_agent", "UPC_PROVISIONING", "50004")]
        [TestCase("ebe_UPC_PROVISIONING_agent", "UPC_PROVISIONING", "")]
        [TestCase("ebe_UPC_PROVISIONING_agent", "UPC_PROVISIONING", null)]
        public void GetAppNameFromAgentNames_Cli_ValidAgentName(string expected, string agentNames, string port)
        {
            var cli = Substitute.For<ICommandLineInterface>();
            cli.GetCommandLineArguments().Returns(new string[] { "", agentNames, port });
            var actual = AgentNameProvider.GetAppNameFromAgentNames(cli);
            Assert.AreEqual(expected, actual);
        }

        [Test]
        [TestCase("", "50004")]
        [TestCase(null, "50004")]
        [TestCase(null, "")]
        [TestCase(null, null)]
        public void GetAppNameFromAgentNames_Cli_InvalidAgentName(string agentNames, string port)
        {
            var cli = Substitute.For<ICommandLineInterface>();
            cli.GetCommandLineArguments().Returns(new string[] { "", agentNames, port });
            var actual = AgentNameProvider.GetAppNameFromAgentNames(cli);
            Assert.AreEqual("ebe_agent", actual);
        }

        [Test]
        [TestCase("UPC_PROVISIONING", "50004", "UPC", "PROVISIONING")]
        [TestCase("UPC_PROVISIONING", "", "UPC", "PROVISIONING")]
        [TestCase("UPC_PROVISIONING", null, "UPC", "PROVISIONING")]
        [TestCase("UPC_ _ _ PROVISIONING_EMAIL ", "5004", "UPC", "PROVISIONING", "EMAIL")]
        [TestCase("", "5004")]
        [TestCase(null, "5004")]
        [TestCase("_", "5004")]
        [TestCase("_ _ _ _ ", "5004")]
        [TestCase(" _ _ _ _ ", "5004")]
        public void GetSplitAgentNames_Cli_ValidAgentName(string agentNames, string port, params string[] expected)
        {
            var cli = Substitute.For<ICommandLineInterface>();
            cli.GetCommandLineArguments().Returns(new string[] { "", agentNames, port });
            var actual = AgentNameProvider.GetSplitAgentNames(cli);
            Assert.AreEqual(expected, actual);
        }
    }
}
