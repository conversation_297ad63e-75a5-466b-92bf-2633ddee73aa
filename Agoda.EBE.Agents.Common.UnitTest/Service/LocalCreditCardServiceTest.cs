using System;
using Agoda.EBE.Agents.Common.DataAccess.Interface;
using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Agents.Common.Service;
using NSubstitute;
using NUnit.Framework;
using Shouldly;

namespace Agoda.EBE.Agents.Common.UnitTest.Service
{
    public class LocalCreditCardServiceTest
    {
        private IEncryptedDataAccess _encryptedDataAccessMock;
        private LocalCreditCardService _service;

        [SetUp]
        public void Setup()
        {
            _encryptedDataAccessMock = Substitute.For<IEncryptedDataAccess>();
            _service = new LocalCreditCardService(_encryptedDataAccessMock);
        }

        [Test]
        public void GetEncryptedCreditCard_HappyCase_ReturnsCreditCardDetail()
        {
            // Arrange
            var itineraryId = 29239234;
            var mockItineraryCreditCardEncrypted = new ItineraryCreditCardEncrypted
            {
                ItineraryId = itineraryId,
                CcEncrypted = "pgckf1OELRPxofihC9AUOkx8NF0wmYZkEeHx98TcvN6XoAlabcRrc27+eBOq4IcUAhlKB3H+CypsDMda3phbnyqciUuAGbJ2o6O/RgUzRyUwTUzHkLVV5RMKAcdFS6Uf9phIcp7fWT0lV9W2fSknRBJjq00QIybXGe1eiFUoOgy7apcl07RCjR/lUnDyHolu1l+za8RcLT9iuYjsEuj6QUvXHE0Bha2j0WDXfnVENRGoSatL0yZ82lBWZJKR3gpjFI3KJQq0EpSmlZCGjU/ROg7lfLsXQKJ+QkuNa7Pva7I1kbILiA1f0ywtonqAZ2K13YD7Voc6lEviBOXpQzILpQ==1bCE740DH6OoDrF2P3ImjY2MiGCiJ3RxtnvgOP4fGDg6bYhXHEjvq/LL47r54BTTA1EEu/E01G5T3umHyJUf/L1p9f7rL3m4XQ2/HLrx8goF0kVYuNaO2fN/v55zJ+7Xo41rlG6TIDc9lZvxoTSRuCAWfzXBVcf86wp9woJlw8x0qzv0ps0qxkvT7GJNaeObzq7CsC3kyVRzMmP6nNw91+XyN8+p3G0rvSCIDsDPQ6g4wfb3Ozp2fZ5EHONY6I1dlATAHwtcIqNv6oucNJ1uASYp6N6V3ljKnLSw4/uDxx/vaUeSMwfyxFM20ufVx8x1rj3VHNNdwxQBFOOMIVF8jA==XYxiM2Cwu/Le1PhGCsQZ77wUIN6JLE8085T+1SzCTBzfLWMSNni5FGUx/RZuVLYKGNEj02qQ/V2neEQkI9pXsds4nWKBLnrMVJxx1WN9v12xo2ytog4ZgiLYpCicqY8uzpdNj3OhTSDqQ5P2QhHop6Lewyp7q0D2ko24XlUl5Oh/HKpH7jWjLFwS/O52kGCEhcAfRAaGRszoxvUHXBEVqZYBBp+hCouvA/IQK/H/C9DXDsH3GcxRIbOfCo983uFlUK5ljAKraka8KjzD9QtUHR29qICCgww/mVOv7q+hIJ83/F4D+CEtLck29M++hgK5MnHeQcq6QHnRQg8A5OYcrg==K5rQ0eHB6W0TFaZZiuoccUeIkdDqT988ZfHjC3PrfcfBIweXLZNSQ2Pa+eeJCp2OnicT0nzk3SFWVkZMscbxztho8r7ZEG8c8a1Sc2D7FQQ+yFoy6LNdmRlS8ek9aBhnk2RR0MuZnSL50Fhamessf+0zwiABv8NuMXEE1C7uJ2mV6FCK7N6ZBiXsVZT1dR/C+YQG6WV21QbHtR9lMRpQoGXU/UOLxKbTvwAuybY7NWs0CcMuRhQovgrxaeEUqY0H1d1icq0KC6hgbWVT/bpTQE4BCVzd9MzHSip+8jMCbmVcjPIxN2qFtpa+FSjlg7m/cNNlXFR6pnBJ4/FHij14bg==0DsEqqinJuLFL75peWuqDR1QL3Ko0G4qoeAZPYBnySqMU5wyjF1EdnXNeC36aQ/Amo3x9keV866fQdxPHF+oJHcjOtwwMHJV91222kucqG6ioC5Yn7bQ+5xdUUrZWh61xDKHrqyFyzmIjqAGcmgqdugn3hbtDtEHNKGLg4lRlyWPfv+UIz227v8kC61TYjJnzg23pYRxV6KYMrilqqdpxFj2A0ONxPHNJ3B348+psoqUFTAhAJO5fxYJtrtYtXX4iIZhi3MmS19zFE3PNIMXGRwsivkTMOzywUuroiYvt0zLoBsZy0NymAvFg4BGDKOVaFmgg/U5+equF82NlyhVPw==",
                CcHash = "mLJixqwuSPYPKLXs6hh6bc8kxLuwfZdevQ5Ht0hoQB94UAOKeIPcczDKEkK3ZU2UEZTIUQHjnXjpjcq/oYspvw==",
                KeyId = 1002,
                RecCreatedWhen = DateTime.Now
            };
            _encryptedDataAccessMock.GetEncryptedCreditCard(itineraryId)
                .Returns(mockItineraryCreditCardEncrypted);
            var mockKey = new EncryptionKey
            {
                EncryptedPrivateKey = "iIl4mSI3KrGl9w/ugr16F7ldhJSNu/ojxhg4i+SWi7E6UcWuQJlQGsWtL04a46b1XsCSK/qXIRLodCI6sBm6ZZwp6Abm+aYYdksNc2zSHzCsB5t865mIqNhAiQqmj1J8phiMuStT8XPAamlvV1fZ4g80yrPh34czI88J9/lobo1mrZ0uCV1dtClzAZmE9f4Y9hQLla/l53O96BQoVqnhybluJBRLem9Qetb3xGVJVwq0/yu5UlqQxruWfwpkKSRjR2jnA7cqYKjcxh1lbUoRxKMetVx/dpdM2p+kFHOUR6O017pMf4r9AvQ1daIXw1PQ11Ht1HJ74Vke5x5vi2OVaw==DIpHLeLSl4V/gyPqAiQaqNmxfW+sUfN+PxaPBvjnyLhKEja2RizZb4LFViUB1Wz89M1C9LWAp9TES4YPwFuzDQFYQ3ODlVnBjFBFmQYFs0moHAt89NZvcspy1Yl0kvFkVQckUisEWfFr3+aXeO9nPC8PqeRid5DGzGDXcQhnOcRDAmxKMPyMBkyN/rLJI2eVZbuMy4m3t7QyBYk0OwRZRm+DpRr6qSc9vZ4QrRdvSMHoxZSoKDXNG7Ec31R9Id1zIPB2RPOYimwqjL+etJ7amu8pKmbaU5fLXBaiCgWlrTRX/t2KgXhGgNAhwFlBCKH7f2IMdpRExlC5xJh/N8MNUw==O4+zdgSZwNcT4JpqGRs71IPjnDPhBTNTf6fOk37kLnC7OY2xr69/B6DTy97iB54MnixeeyTKx4LUp0MXs1yrsSa1OLdQglERNJWUKnYLq/ox2glLwT49H173cu+xFRRP3vQnuWk1jozd5Pw5uDyaYiTxeTvgkglPHkBiB5hT9ZW0QYLC8go/7vz/tVhqcVDkchp9kIncSvbd9qQLET9JEJ01MH0zCeXHXuiI3b14k/u0z0CkhRvw+ZNHHm1LysE0zMYZwn91Jey6iS8NnHZC2ln50vt5pUlb15Fnu5UbY2YGcsTXfOahtif2j4+j7qID5DLr4xLrr+8b9TW6IFElGA==A2k6dPUe4bU/bmqR1iDz7k+c21LAoDP1m3UD6x+V0K+XmV7VeP/JojTM368xNAL0cr0BdOJHsErTz2KC+gNE2qj+Sr7d16dTZQEqZlX5+RzJ4rC+rOAJyrahjOm6LXVroxKgcQnTGQu3dGr+Yn/KvxQcrxXE1Hd83LtwpG18xMA5xPx+OuQaCpDVf1TzG4uKlYvVNYWeJOGwVmEEgoO7wAHOrRhkEs30GjC0u45WtjGiFxM3rnC3igqPTEGhAlT5nIv7Y7ub+1QV7iRiOk1tmsPU/1KSyBj1ifXzVVKFEp9MwWbPA/ZoGvWim5hFd2PV+uOR77QnOviVmmq09wZ40w==FWyj2dZ/cwK9FSaK2fN5XlNZyomNMsQaSwUcWmK9EjkzgTHHEOr1ews4HLKMPAktNDjIVWCgtDScCXPBP4m2gZBtm7YJ7LiNzgDOkjHNSRkofr/OJNuwFUevik2ZD8gSB5AoxPeZo54TldW/R/gwSTE6C/iH802Vx/n55fjg4vAUBuWhM5dbi/IoYcfuGAohwvV1FZL0Lq6/NqIuJgaHPm4Sse9lH6QH28ibFkG/KNMmwct2rJbaYQ7mD5KW2GIeQ9Dpn+T+W8sHmjRJcQWtuN3NvFoB4jyC6NyxzgXlZZhqketQ8Oq42uMWkOJjBvuAsb6hdODREHWM6dCjYtcZSA==WF8CRmfAP/v7u5doaXv1xaNpmmLuGjw2QkVKGpAJpwzf+kAWVn6ms2VeCJItLqLnte0/NGyXlBRCpL7HKFifCzOvsEB9C006wsVdYtvRRQk4IX7xGqKLayy4HW6XOTAlbkOLJ3Y/LjAuvjxrwdy/xHNv4vPqIxs3/UwCpnw9Lwm38sooOvUM/002S5ttbC2QmzEURUk6X725uLXp9/jeuj9H5bOw+3nl6KPgV6DJqY6NftM0GXG7EtdgLrJgCHkhevYlpInhoczVu7eL+QsC7uXTBHZzCO9YcuJFHX8vkEDsiqqJpiFuR217rFLaqiV9xxQQWY0SLopqiMZOW67lTg==jSIqpyUVNn608du1sx3tla1jLSbYzBsvFYMi2qAi7It6GIu3VMCEPmojjN8pMRMuDImmfBb68LEgi3CdsufhE6uHSyXJBBjIO2boCvU//lLE4OM/yVF58ijaLAwzNVR7SFahdupJa9VOGzvSSIR9Hob6jVq1rJckoCWfTy8zQ98lO/6dLAX+odVTrKEvhdtzMMqqFepn21T5Md3IB37xeItMh4fmfXvVKdzIb//s+7fam5eJL4osjFW7SFBuHKZ6JIkZ3KgDdROg0P6FAWeUaRWMbv5LSqo1rS9p4/sw5xa75gKwqNUpxae87jrfMyxgZxx07tgpdRrt85lDR9oa6A==MET0oNTbH8sSbzt7jrzwlicK7xGI3HvKQE3eF+NqJI9IQw1F3C3HNocp6SDdpN2BQlApH6OG0vf9YG7Fia8m2WVWpE3h9h1kNSJyIN0MuYH/2aiGMXS3G09PCcPw61IjpHSMAJcpTKzzuTE/1qzikIeDbEPK2VBk+xVeLBe0ZpgCcRNGfR04lv6K3PMFtDob91RPGmnpxiME1TYEFwI9CGkH1BmmiBqULAAMgCfvR47OwDYDM5MI0M008yBtugrI8qc+jTMG/cz0aZ1/kIVSTkuAVTpeRaspsWbblk6tk85DEnZsDEp4nJ1KPXIfXqMxb3wJJTi7rs+QbWYjOvN+Gg==f341b1s1gonGuqNaaqE+640t8B3zgbQjUMQ/lDyn0CxXJP+0dDuOsZ4HxAEvxC8ItBMWIcStdilOzGf9JJF1Q8HTPg9l+hPYI7io0lJyip8nZcXyHUkT7DVXHt+nAv/QhSpOeAQXRFXzn7FYz99/QBOo5puqIA+bcGNDpzjYvc8msxjkdXNuZvfn+lilSvRNk0Rs7qFItu9M5noYQpbu1UkTdVMTyJmFgzVgycgXcAxxttPT/6PE9JMo4lFm/2gtC4ZVPziml3YoHkSWLTa5MziR+6Qk1jZPCdlClAGW2ncnvWJ0VDbbVD88G0dTZLfQ6f6e/zC4JQ4mAS6EwZWBrA==Jazc2dk6xUNVtH0UiXtbMEUXJapp1srB1fDwZeV9tUBttMMzrphhfeE2oyygRAAP0Q8V25Nq2Y4Ee1rdK60HxvI3XMovEsTr039xK5fCo7IbODGxdUzIieQgCOX3hHkdLAfYxrsakSwSGzHz/VVUsBcT1wpU4B8lHY7x1xZTOqlEosqKG1pVg3S0zlJUKGYJNa5tPkQgwKg5XbtvDA08cyDMf15lK4VWb7PxLIYDPHt9Hzp3LvmaHZfo+HMESaa4RvinS7sE1+swuw81XxF3igToU3LCdXDeAhDRAAXL7gv3J16fDoAOA6YtG9gZ7eR6SyjVkv99WY93LQE4hdGwkw==i+6Ge5LI3Inc08/vzJTGu2Bm4kJTzINOupC+LnWp5u+bcrccEvbj1th3dZ6B/dCcCwg4KbsFYu4l9D5adyzE2vuX2UqAIeZ2iKui9v2DYRz4rsGkz5go/fPk5pyu8ZltI9y/TK4FZfaHQH4Oy3fAxtcgcnNC6AC4F0SeuU1WFZl5r85KZcBCq+S1qUnoA6svueKOW8z4hEhwxiMmGsYymEeOF6ZWMTqArIUZ1wFRbxuEXzWkqnHqiAPVTTVMfxGAUvAspo7H6zOfLbXmK1h8IssUQ1lHFN1HzIlV006/BC4UUe/1NZ2X2u1gn6Otm3svLDqeuZaGdn2n8OqNeyxh9Q==BDqbsQteUmUgNeriZRylzfG3DSiEOV43Q6QQmbdLvKHB4If3e3c/4Rv9oQfTtCobhPxIQPgJ1L9Z/8kQmv5zfAd8XZmiSb3ivgje7+FGuMTPFwTwRWfPWuE+Xx/VPl/hbR0H5yX/YPi7AaW8LfIFeHCMQZq8tWgHUvPHLXMlZdhm01Yjf+YOGjdXI7qDuDwliaOs0BpyPGQPGsqiBnKPCjjlathqy3mvMk6KzLUJkths0sanJfZAVlCpoDqZoZVdw7QGiSQLItaBdVZVWGcKyUvLD8iqmpEifdnp1FqT9bfLGUmjIckOK4xktSobNFvYlDCgIU4E7DvzVuJVym78NA==SqQV6086zUfGKjN2l/ipxIp9dhv+/KXLzjZAuTG3kL9M8PSd04Ju2gxLSBewYj6bWAnUEPFtRHuy0ycEn6Cq4l67IldZX4bwanBlSfj11ZHeY5VMwvr/I//oO4tQyN83AGdVi47IcYqS+abDRYc94IRq6roLTbtvMC+IAGcvUd7kR2VIh2LVOWQ9ys393+BVERDIK31w6uXiDJUy8GMPvlBiNNcLX13zZ6KSUIhpLcNM9Nra8ksphSTfT++5SWy2kywkPtnq8m/7oeNUncfUOqAbTESthEzM3FmhoJ6Ciw+IT397SJzXBBMhdDBOyxfehIKn9ebK2D0Y3j3bALPOPQ==rSIxp9bVDsNJ1siXGo8qsjk56Wh6UYq1G5+CrQ3GfvAVCFx3IAJUJtqOHBpSn5pbTrxdHkV16mhyU/a+BZ3sjSitN12XDom0aIa3eaNKhb9CymfXrcOxkeH407cKDlXYRS7O71OrAvjBLViIw/odhhpmCk79s3dgcL6ry5GPYNA77bZELb9FQLe3IEat4zbWDJjLf/bylwTwhpGGesGPSGgT+xsYZI0P2xhfZVgA0xdsTSTDhMT7aRBpf238ueXcL0QXykTTcht/N92qFbeAdQ2yj6JHZ4SaB8hS+MnX28x9tMlTKTysxpQMMCqT0zO+8kQdwySMU3lYQopte/xyQw==Vkr32XHp0N3XdNRR2cFr6QNTN6uvXgh6wNSRpNgNxZi1ulqcxIQnbiPeDADoJuInMnqZ2Dx2BcypNPBAvBDDchDeSmrnplEJFsqXJy7EXF90gF3yTAOG4yCmc6MadTbPrWRneIBP/b46bqyf7oX4DFEhh+U4Tc+1uMbyQSfpPz+1CRM6mKNqPfCJaTjFxq/hGCx2Sq78rDGQBAMFR8FT172rQcFc6gh8j/AVwPSzXoZUnOuyLWNTWTVr6Vwm4xah3I9E2z7yG1UPyXX9T10DQRfbkdIB389ga/qBjE3ylzbDKfoEK7jBENMNIJwlztx+Dx6iQUfkB6Va+wDzlAVxIA==K9SSQ/EudUgk8bUWmuN9jUBiuutHOoE00tF1grBo8qdS2RMbQntXtBMiyGAvpZTKH5PGybG5XtCu2cWRsPisB/L7q/tE0IufIDLhGlWNAEnu2PD5F3Qq/ea5ueyuYbSpKyPXsC/wdRDCqOHdFMAb2zszqnhQpStSoi7aX/w6zNJkkzpayRphL5uwIyQGZ7NzuQhaj8Hi7VX29h3FW/751See3K3U2w3urPIGpzMLIYAWhCSTWJJEUMFwrDyB7mVtMjbj/rwlSlWlJ9p374Z/N8T54WGqAFE7laO6Qg7D2bGw8xWwAY/xxb6jg4cSWX9DHyPIg+Em4QmS0vAhzcENXg==Kg0BnlstehIIFMWTiryVCWEP7YMoVHg5kPwjPNetRH4wveaXLO5T0xeDERmldMWjYOYAdQdfozAiIHMRZgknrUmuUGLjJ86dehht6aS208WagPR/3QjRF8eEni9y+KLbnuQPRUZjTW9B5p9LSpUZwXhPxF2xq+xlJ9D8fbg1vRqR6NQn722FrHC+SZNfYehBMvr+h+2XnHuAstT2FXaDzzUnK6lGr/sWy6JxsSrcPU8HCrCtsP+NR0gffZbZ+aZW1afKk2Y5CFztjxK4D4Gy3GUBDoAYm/xmwAw/FkpXpLVEu1bz6KJEMUfwqZ8nZLXgeUPiLILvHzyUn8LG04+wOA==h1nvNnGBb3bQAwsoxj7zkDqDVzP1Y4kiqCJs1ar8272NnUIHfOqhMvAP4xlmk0/SBk2x1Gkdagoxa69cPn8OZnrJ43J+rM8c8RD0bfLMxmc/qBx7npHFTBtMuFlLF5fCteEoRI71N5CrK93j1EJtDpitEbJj05/oXnD4k5wg4Nl4xwv/v505jNEoqu1HKoIsuzKlhNS+IO5xhiBAXkSIf66TEUvxr/9qgQyJH8WDWJNzmyL4Nhkmu4W/ftAWgYjZouMuWc4Dq6aWSzT5ivWI44TyxS3ZtWD5QkgWK5qwS0dJQKNkWapJlBNJGG33bMDm9cEcZoxMDyyBVC18dRhXvw==Spyr9a/dNQzK2s3knmk6HIa5+EjrWyY2y2SWs11+scDtqZbuJ/awOUF8H8z736zw6hiVQ7QKlVRe9jjZ2nfdobLZDe2Hbr2N32HouuIIiMQ5c2tbEom4NYGEKzq5iamKBiaIhAdYigUk7AfaXGIe7pwBMxkyZqbO8cLcGwTrSrKxPtCDqa3nY6dIhcVpm0gT2KzeioJ9r5bGsbascfoNNdGpjWudys5muvDFUItesgE+RIbUkZ5bI62KXIkNkPMRlchlUhZm4hLtj0+w92Uiw+GzXYOScR6dKOv9Foznzs8bGqaZV0QfTfB/uuL+wm4SjYRM18rAb5ts9S39mKrvWw==eXm4RNmMb1pABWbUqUd8YSeUjzNt7hI0b0/SNTn1kOjNjcW9eSx7dvi+/G3UkPHkUs0OWxuV2hUXbz0Euc7b5wxZ57+JjBlQtdkvCtyhxKn3oOgw9MuLKRlXhNakwdq13g6sfbJUsYc5oVWgo0/fDmJXsbyW5BSzTWuIjxyQLoFwW4iWWNuARKp46G6pIrd2RQRRnm8etA0qQ1Pc2OROXZI75nZ2mnGxCHV0Yoqo43QKH8ttfvqoYC7QPe5N3UFp95QQ68MUjDStKQ48VFHcwHW6uiZFrclLCF5QVcV3rf3LmqYLZWH3MvowShMIYr9X7PA0kQzv7FcLmp1ihK3c6g==CfDLGFXzCs8SOsesxO05rP1fSBSEa9ftXnBGShQFXzHGEVMnzcwFmCDtMht3ELz2wfuvAnBp15OiThEQzsuIvfwbS66rRGJoN1UmynZrv8W9JM0JvMw0i5DMRRkg2LXj+6e1hGQXn/NXCchETsaXL+5HOPHtUUogrgUeYVRlzaVxRKYlFTRZzuslE6mvd9Z+Pc4RO3JGZg6fw/3RQqtgxgotRWKHcO7DOhmri11YPRg44YNFnzG5fnCRymALkOVq6m0k2Rlyic+gEWY84SKzjQSZJUcPoWfFmHncpgR9amEmxiMj8uWIfRkbMZ5jxnEciJK35Bbtje/xoThenBpc3Q==TaCZgNRcUo5Bx7NIquwtxSw+A2RWrZyeg9XsEoHqASN4QeR63pPJgWXU546Vu+IRg/YfKzUwT+olqJoy1F5XDXx4jtg1Ms7JMMd3BUccAAfY+V4slNt52HuKCyKTvnn9eugsUDbzDcptQYNPxqZZz/M+lDRoGyL1PzH9l+dvX/VrC6e61uByDKWD8OehRUysFQhNXVJWL4PUManS6Dt6xwlobl4DbIlehFmRy9TqRklkybqIxY0Qe19U/4+miSOpMHo6K7rHUfzGUJ1cAXtpKpAhkZE1enk9Wn+zeCsQ+9z9MilYTnMRJxNhePhwHq1pSp6opRLOu1O8HEKSuWfWOA==LW1/A7KU3C5DbjVSYAzS8gdV++ygSMn4giBPulqUSCyXrRFF2ixXX5Ed+ZOChCpktoRlYTxB+Grf1ZBSNO4bCgGiBUm4Ael7OSty2AZpA37t22sfpn4ceU68n10yUC1nWqu67tbSB0zpQJYd+gF2QsRLoovQv4rnrxqNdz1iIMOZ8XwnDSSisdK3Jn6I7Fa7R8fDeVzWwRQpXkGA3bOBGJW2Vry8YvorcWxXh/D9+NamNcx1R55DCBYcLK6LsHSUkDZwrtnHnZQoKjnkkiqr1Et9Axf8lBO26VaSwUSNlbJZWEHhjudUI3Cs7Y1ZXafMyhCcnecGsbE4HM0Jv4MDjg==LM397o/Foxz9qtome7MP80X3rTSzEUyLElJ4PWEnzJv/yeUkdIWiDLczy2g12KmMOYPfunL8C4QKBmkJqlG3ZVBITTQ1NPBU7uroLTy+Hh6DD+z17hind9rtPx4Z/O0USTzem+IhsoIl87s3Yzqonm29ZMp3U6KUWDru4oqOb6+Yx16Xe4qPD9etVseX1bk7NQlBPSkjlkt5T+yOOH4G/hBcu9MVBydVO6d+Y3TDQVysH+MUUB7y7UHDtlOWzT/TjGnSKiYIcNOGfK6MsxRqRuqH00i4TWmkrqGBERlNTdDJj1xZdmjQdAGBolWUKphguL11ONR63OmFvKlXKpYpHQ==hP5FH9tlRhSKDslFcjL8DyYeOxn68wtgpUH38kmE+X8Gx204cn0Wt3BS09ZjytHDJScrTFRc4ectVcXU07KEO/9Hn//7uirVYSN+NHxHEAEMl8UMETHVfGnGmfyvRRg3xg1wir6BlYHQcacGnazZgt+3W/Jx9Cq/XR353/8noJf9k3s2w7AJK2XQPL5bHCsZbph4CaTHj2bvkJO9oCBT/OcE5pZSXVQdjmRA+AvF+s0WPe3cyVtkI3kbtJcy37zMZBKMZ1AhImqDlkGLY2xd0I8bMFVTqYnZoGGJJtskAnncSv8neLxDTuvGZojiH40VQJ8KafroIMLd0OYTNP3YFg==EzGW3iQBVcqgULOWarLwh3iSwhm/keDnEy0P1YxRxgS9EEPbzzTrmpD/boccpXYND82n/13npAhdph3fAZ6SNfSTuzfSqhbP8l++woNYz873GcIThSKioTv1L8dPzCeNuBmYj3bN86YncoCmK7+mBUptak9u6jpQA5qQGxeaKGAfkXLham94m4DKwnhbbQWs2mgDun3BJvwLBEuhc/uTVI0XDVnW9+VZLwh59+54p57TezjoVgGYXPvAtNmiClarNjlYWHcnDYde2MbxEq3yjH0Y/wnUTU6sucCWtj0lhyuYutqSddACfc5goV8rsMY2U5rmw9whxkZbAsV9ddSNKg==bxuXiaGwerFHLTggft6webudue32ERao7MR0DZS+3I68Mglk4LXbqiLxouAmXRjWrBlUZKqNr+zR4pXFq5pW9BR6huiv0zwzkvgLTmjR+JV3TWopXSGzcm15rk2VesRd1PWSzmJ4hItjOaP42YtwuGfdsVawHA8+KIzWA6pq6wO6ehfgmhV/9Y3EeaKjptL2fkLh1k4mQQsal5G3FX5U0UZjMqWeB7Nw+nlSGv7//2zSs/3QOlcNk6SEU1tpDYBguydu/V4y42hGjhzeDs4VPCKWQBo1E9XmCsQ3+knPucYKxVG7Lkwu2K7phC/cbYjMsk+xf/sSU/JAQX4yjMfjCg==Y392IZTzYuRVLASU+YSx9l3Zbq6thML/PvoQdeV0rO/EIR4ehV7lDq+770Y9OxrqnawYkvL1Dzv97jdhdaXwk3jsxXE/CcZTpkSKJ4QWvSufFu2UX2uuofEbjzT9jiJzdzUisNcJOe/9kkSEHjhPc2jHpxFEWJonBkdyvzErEOqA4+TT4dItzoEwVmyEyYXeHSKY6BDaOkIC+4FrF3n+Q+NbMHZQwCNIdRWUwG+ZXaQARN1sq6Gb5TGtu7UqS2r5vAX1HmB2XXPbsaJySyK3p4Mwot0JUHIRa7ovEE2Q19HptaNtl2q2KtUCx9mf24cPRh8dsu5iCHBAvnDAuKm7gA==YKh60ojsGOMsfXSgiJKIOw9GL040bUfySXKYtaKW0Il8KrGUco+1VVkTBm4zGzHGRbQ1NHZ6VhlU5Zl5QzhHdRApIIhlmJ/gHLQ7YgX4TvZR3IfQTs5s2kjFIFRd19rJf3tdNIGsUYTZl86Tpt+26k7lfkVKDCI+S+11jM3ZJJy5ow5GIe9NybIe48N5DjilJKICElLrU1HHIuRsjw+Akd12RCC+1GM74Eld6ozJVDv9j/K1h4RbFgGKpC6tpSmZYP35NII7CUKx+0SChalUieSeT4CTcaKP3QIIH11C33MYm133SUzQnmPP+GIrLdO6++4SVemMjaRSSH27vCAvCQ==mB94CcqYNHzHdCcxxX0KshPEUYJNKj7K49hxXmhYJY1KOxrLIlIU6EMQCvZy/b4HbHUt7bdNVacNP+B0+L1N/uNnH4DqEuoduFN0CbxF72HD9rqXs3QFRPpTW9PmeUPRYAZfQ+3ygSWqbc+6gQoPoIvM7ssqLwcegy9q1P4FFjug+WWe1BSm1e1+OFzm9o3rhzPffvTFWmw3b64VAyqiM8OVIfMzaVg3uhMaQ3TzFRV3g5rNSzpfPt2nZk/DwBbxihe/0b9WOptBjFGtk8nYkWJuzde2QzK4pv6Gikk/LrFXGQy/dbECo4AcsjInAlslWNR5OIznGdsEuhfGrCXMtQ==S+4IGgYmBI8Bt6iUGJ2BVPaGaFEfZb2zxEfw4rquAcyaHqzbp/fO9BBA4jn1Kkof68aXZTWdPYuhGkmjIVBSbGz7BlBfXO+uErfz+ZNsVE83R+E6DGGksmAj2K6tcB32vbDhbX484O6o9g0kGl8ctdNZX6fvjRzkzx3FfjYb2MZpwMMe9nQEOPyE6/UWz+fEPZ1eLAjqIK1FNfaQztmskGtSS3+x6WJN9zCDikIFDD9gD8TfbnvfL/ytIJJG2aAYckj2oCLsfuyFxKBOfdRMbjRXefpkP5EOKHRtbf+jGUwzqeFAMmYwJkB0EeEIwt26q4j5nTH+0Ev+CbQVh707Pg==HE7ThSnY3YraxaSiahd65Ji052P1DW9pkqMxq3e4sWKixsEoO3TVX0rO54zfV8AKr7MPuGI8SkaZoa7EGxbCtzsyGkN9pJVbRkt66zXqeNP5wVcZ6ZiqSYdCWjETQsFQa9uZb1VpxJpfMs9r976m7rC+9U7Cvlxy9OkHVqOfy8GFAOtC39odlGO7H/GWsacDKBDEm361XXrBGccUNCsofJaooQw0Ha3Uo3EtN/unGnhMP/Bk9FkSbhFxQPsonbWFIKN7kHh8re1+r6bNL5cAeoqdqksgAj235UP/dRyYcc0hjTj93X5yjAcq/Y65vB6j2ls8Th1xAMTiSxN+DNSTtw==",
                EncryptionKeyId = 1002,
                PublicKey = "<RSAKeyValue><Modulus>41UMRtFI5BfAp3sjg2Gm1Z/FrpsA3A3W8jYZoRL80EBXkvYrVVMNF5db2urSHClaoDcf9UXX4AXCENSQf869tPkgj4f3UTZxJkX5HjW83KoSpYuXUUz4sB9IWBvAuq8JMmmzQMOd9+q+jeIKta005/5s+2tE54RajbBgGuXGqoODllK0GoBW1wNP2oy/+n2Xw3eIhQ3BeUER4qjirrg2rM2hnqnP+d5Dv7LB+uX2wNtibtEhacHTrfxAnDcZKMhoRUkECop27N3WajefD3TFJ70fie4gxNREVBAuoGki2GLGlkvRcjDx3FlvJpbp0Cv1OLFBEIZJYWW3asrEM0QLpw==</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>"
            };
            _encryptedDataAccessMock.GetEncryptionKeyFromId(mockItineraryCreditCardEncrypted.KeyId)
                .Returns(mockKey);
            
            // Act
            var actual = _service.GetEncryptedCreditCard(itineraryId);
            
            // Assert
            actual.CreditCardNumber.ShouldBe("****************");
            actual.CardholderName.ShouldBe("JOHN SMITH");
            actual.ExpiryDate.ShouldBe("12/2020");
            actual.SecurityCode.ShouldBe("123");
            actual.IssueBank.ShouldBe("Agoda Bank");
        }
    }
}