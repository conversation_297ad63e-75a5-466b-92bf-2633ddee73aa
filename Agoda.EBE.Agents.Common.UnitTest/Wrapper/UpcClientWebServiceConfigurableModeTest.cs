using System;
using System.Collections.Generic;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Wrapper.UPC;
using Agoda.EBE.Framework.Messaging;
using Agoda.Payments.Upc.Integrate.Client;
using Agoda.Upc.Structure;
using NSubstitute;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Common.UnitTest.Wrapper
{
    [TestFixture]
    public class UpcClientWebServiceConfigurableModeTest
    {
        private IMessaging _messaging;
        private IUpcWebServiceClient _localClient;
        private IUpcWebServiceClient _centralClient;

        private UpcClientWebServiceConfigurableMode _upcClientWebServiceConfigurableMode;
        public readonly Dictionary<string, string> _dummyMeasurementTags = new Dictionary<string, string>();
        
        [SetUp]
        public void SetUp()
        {
            var measurementMock = Substitute.For<IEBEMeasurementMessage>();
            measurementMock.Tags.Returns(_dummyMeasurementTags);
            _localClient = Substitute.For<IUpcWebServiceClient>();
            _centralClient = Substitute.For<IUpcWebServiceClient>();
            _messaging = Substitute.For<IMessaging>();
            _upcClientWebServiceConfigurableMode = new UpcClientWebServiceConfigurableMode(_localClient, _centralClient, _messaging);

            _messaging.MeasurementMessageFactory.CreateNewMeasurement().Returns(measurementMock);
            measurementMock.EndTrack(Arg.Any<System.Enum>(), Arg.Any<Boolean>());
        }
        
        [Test]
        public void RequestCC_With_ProductType_Local_Success()
        {
            var request = new RequestCCRequest();
            var mockResponse = new RequestCCResponse() {Status = ConstantEnum.Status.Okay};
            _upcClientWebServiceConfigurableMode.ApiMode = UpcApiMode.Local;

            _localClient.RequestCC(Arg.Any<RequestCCRequest>(), Enums.ProductType.Hotel).Returns(mockResponse);

            var response = _upcClientWebServiceConfigurableMode.RequestCC(request, Enums.ProductType.Hotel);
            Assert.AreEqual(true, response.Status == ConstantEnum.Status.Okay);

            _localClient.Received(1).RequestCC(Arg.Any<RequestCCRequest>(), Enums.ProductType.Hotel);
            _centralClient.Received(0).RequestCC(Arg.Any<RequestCCRequest>(), Enums.ProductType.Hotel);
        }
        
        [Test]
        public void RequestCC_With_ProductType_Central_Success()
        {
            var request = new RequestCCRequest();
            var mockResponse = new RequestCCResponse() {Status = ConstantEnum.Status.Okay};
            _upcClientWebServiceConfigurableMode.ApiMode = UpcApiMode.Central;

            _centralClient.RequestCC(Arg.Any<RequestCCRequest>(), Enums.ProductType.Hotel).Returns(mockResponse);

            var response = _upcClientWebServiceConfigurableMode.RequestCC(request, Enums.ProductType.Hotel);
            Assert.AreEqual(true, response.Status == ConstantEnum.Status.Okay);

            _localClient.Received(0).RequestCC(Arg.Any<RequestCCRequest>(), Enums.ProductType.Hotel);
            _centralClient.Received(1).RequestCC(Arg.Any<RequestCCRequest>(), Enums.ProductType.Hotel);
        }
        
        [Test]
        public void RequestCC_With_ProductType_Wrong_API_MODE_Failure()
        {
            var request = new RequestCCRequest();

            _upcClientWebServiceConfigurableMode.ApiMode = (UpcApiMode) 3;
            
            Assert.Throws<ArgumentOutOfRangeException>(() => _upcClientWebServiceConfigurableMode.RequestCC(request, Enums.ProductType.Hotel));
        }
        
        [Test]
        public void ReplaceCC_Local_Failure()
        {
            var request = new ReplaceCCRequest();

            _upcClientWebServiceConfigurableMode.ApiMode = UpcApiMode.Local;
            
            Assert.Throws<NotImplementedException>(() => _upcClientWebServiceConfigurableMode.ReplaceCC(request));
        }
        
        [Test]
        public void ReplaceCC_Central_Failure()
        {
            var request = new ReplaceCCRequest();

            _upcClientWebServiceConfigurableMode.ApiMode = UpcApiMode.Central;

            Assert.Throws<NotImplementedException>(() => _upcClientWebServiceConfigurableMode.ReplaceCC(request));
        }
        
        [Test]
        public void ReplaceCC_Wrong_API_MODE_Failure()
        {
            var request = new ReplaceCCRequest();

            _upcClientWebServiceConfigurableMode.ApiMode = (UpcApiMode) 3;
            
            Assert.Throws<ArgumentOutOfRangeException>(() => _upcClientWebServiceConfigurableMode.ReplaceCC(request));
        }
        
        [Test]
        public void UpdateCC_Local_Failure()
        {
            var request = new UpdateCCRequest();

            _upcClientWebServiceConfigurableMode.ApiMode = UpcApiMode.Local;
            
            Assert.Throws<NotImplementedException>(() => _upcClientWebServiceConfigurableMode.UpdateCC(request));
        }
        
        [Test]
        public void UpdateCC_Central_Failure()
        {
            var request = new UpdateCCRequest();

            _upcClientWebServiceConfigurableMode.ApiMode = UpcApiMode.Central;

            Assert.Throws<NotImplementedException>(() => _upcClientWebServiceConfigurableMode.UpdateCC(request));
        }
        
        [Test]
        public void UpdateCC_Wrong_API_MODE_Failure()
        {
            var request = new UpdateCCRequest();

            _upcClientWebServiceConfigurableMode.ApiMode = (UpcApiMode) 3;
            
            Assert.Throws<ArgumentOutOfRangeException>(() => _upcClientWebServiceConfigurableMode.UpdateCC(request));
        }
        
        [Test]
        public void CheckCC_Local_Failure()
        {
            var request = new CheckCCRequest();

            _upcClientWebServiceConfigurableMode.ApiMode = UpcApiMode.Local;
            
            Assert.Throws<NotImplementedException>(() => _upcClientWebServiceConfigurableMode.CheckCC(request));
        }
        
        [Test]
        public void CheckCC_Central_Failure()
        {
            var request = new CheckCCRequest();

            _upcClientWebServiceConfigurableMode.ApiMode = UpcApiMode.Central;

            Assert.Throws<NotImplementedException>(() => _upcClientWebServiceConfigurableMode.CheckCC(request));
        }
        
        [Test]
        public void CheckCC_Wrong_API_MODE_Failure()
        {
            var request = new CheckCCRequest();

            _upcClientWebServiceConfigurableMode.ApiMode = (UpcApiMode) 3;
            
            Assert.Throws<ArgumentOutOfRangeException>(() => _upcClientWebServiceConfigurableMode.CheckCC(request));
        }
        
    }
}