Jira Story: [JIRA-1234](https://jira.agoda.local/browse/JIRA-1234)

Changes proposed in this pull request:
- 
- 
- 

PLEASE ENSURE THAT YOU ONLY RAISE A PR WHEN YOUR FEATURE IS READY TO BE REVIEWED AND MERGED.
IF YOU DO NOT PROVIDE TEST EVIDENCE YOUR PR WILL BE REJECTED

Once you raise a PR, your change will be code reviewed by an engineer and tests reviewed by a QA, so make sure you run a full set of regression tests and also add tests for your new feature.
Ensure that your changes covered by unit and integration tests as agreed with QA.  Code Coverage and Static Code Analysis reports would be attached to this PR request.

In case that there are dependencies in other module's PR then you can specify versions below.

Gateway mock version: 
Payment API version: 
Credit card API version: 
Net core agents version: 
Automation testing version: 

Version can be: master | latest | PR-XXX | XX.XXX.X.X

Note:- As of now all remaining dependency to Payment API such as Payment Notification and Creditcard would be used as in QA. In future we will add those in Dockerized Integration Tests

Before PR will be merged, it is recommended to run [TeamCity Release Gate AIAB](https://teamcity.agodadev.io/buildConfiguration/AgodaBackend_BookingSystems_Agents_NetCoreAgents_ReleaseGateAiab?mode=branches#all-projects)
manually against PR. Please choose `Run custom build` and choose your PR in `Changes` tab.
