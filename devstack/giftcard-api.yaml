schema: v1
service: giftcard-api
environments:
  mock:
    spec:
      ports:
        - number: 8080
          protocol: TCP
      artifact:
        image:
          repository: devops/devstack/service_proxifier
          tag: "latest"
      envs:
        PROXY_URL: "http://bpf-mock:8087" # can be replaced with bpf/bpf-wiremock
        LISTEN_PORT: "8080"
      ingress:
        enabled: true
        port: 8080
      healthCheck:
        startup:
          tcpSocket:
            port: 8080
          initialDelaySeconds: 1
          failureThreshold: 60
          periodSeconds: 1
        readiness:
          tcpSocket:
            port: 8080
          failureThreshold: 3
          periodSeconds: 5
      resources:
        requests:
          cpu: 50m
          memory: 128Mi
        limits:
          memory: 128Mi