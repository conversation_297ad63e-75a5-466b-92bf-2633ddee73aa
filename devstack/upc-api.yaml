schema: v1
service: upc-api
# service deprecated and not exists anymore. TODO: cleanup BE services configurations
environments:
  dev:
    spec:
      ports:
        - number: 80
          protocol: TCP
      artifact:
        image:
          repository: devops/devstack/service_proxifier
          tag: "latest"
      envs:
        PROXY_URL: "http://bpf-wiremock:8080"
        LISTEN_PORT: "80"
      ingress:
        enabled: true
        port: 80
      healthCheck:
        startup:
          tcpSocket:
            port: 80
          failureThreshold: 60
          periodSeconds: 1
          initialDelaySeconds: 1
        readiness:
          tcpSocket:
            port: 80
      resources:
        requests:
          cpu: 10m
          memory: 128Mi
        limits:
          memory: 128Mi
