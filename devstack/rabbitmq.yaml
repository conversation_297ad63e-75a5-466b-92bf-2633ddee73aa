schema: v1
service: rabbitmq
environments:
  dev:
    spec:
      ports:
        - number: 5672
          protocol: TCP
        - number: 15672
          protocol: TCP
      ingress:
        port: 15672
        enabled: true
      command:
        - /bin/bash
        - -c
        - |
          rabbitmq-server &
          while ! rabbitmqctl status > /dev/null 2>&1; do sleep 1; done
          rabbitmqctl add_user devrabbit 123456   # pragma: allowlist secret
          rabbitmqctl set_permissions -p / devrabbit ".*" ".*" ".*"
          rabbitmqctl set_user_tags devrabbit administrator
          echo "user devrabbit created!"
          wait
      artifact:
        image:
          # TODO: publish proper image or modify bpf with those features
          repository: bpf/rabbitmq
          tag: "aiab"
      resources:
        requests:
          cpu: 200m
          memory: 512Mi
        limits:
          memory: 1Gi
      healthCheck:
        startup:
          tcpSocket:
            port: 15672
          periodSeconds: 1
          failureThreshold: 300
          successThreshold: 1
        readiness:
          tcpSocket:
            port: 15672
          failureThreshold: 3
          periodSeconds: 5


