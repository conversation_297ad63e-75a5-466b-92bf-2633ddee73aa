schema: v1
service: bpf-wiremock
environments:
  mock:
    spec:
      ports:
        - number: 8080
          protocol: TCP
      artifact:
        image:
          repository: bpf/bpf-wiremock
          tag: latest
      ingress:
        enabled: true
        port: 8080
      healthCheck:
        startup:
          httpGet:
            port: 8080
            path: /api/mytest
          initialDelaySeconds: 1
          periodSeconds: 1
          failureThreshold: 60
          timeoutSeconds: 1
        readiness:
          httpGet:
            port: 8080
            path: /api/mytest
          periodSeconds: 5
          failureThreshold: 5
          timeoutSeconds: 1
      resources:
        requests:
          cpu: 300m
          memory: 1Gi
        limits:
          memory: 2Gi
