schema: v1
service: mdb
environments:
  pinto_qa_data_hotel-list-postbooking:
    spec:
      hostname: mdb
      ports:
        - number: 1433
          protocol: tcp
        - number: 80
          protocol: tcp
        - number: 8080
          protocol: tcp
      artifact:
        image:
          repository: dbdev/qa_sql_mdb_data_hotel-list-postbooking
          tag: latest
      envs:
        EXCLUDE_DB_CONSTRAINTS: "true"
      healthCheck:
        liveness:
          exec:
            command:
              - sh
              - /tmp/pinto/data/scripts/pinto_liveness.sh
          initialDelaySeconds: 60
          periodSeconds: 3
          failureThreshold: 4
        readiness:
          exec:
            command:
              - sh
              - /tmp/pinto/data/scripts/pinto_readiness.sh
          initialDelaySeconds: 60
          periodSeconds: 5
          failureThreshold: 4
        startup:
          exec:
            command:
              - sh
              - /tmp/pinto/data/scripts/pinto_startup_probe.sh
          initialDelaySeconds: 45
          periodSeconds: 10
          failureThreshold: 50
      resources:
        limits:
          memory: 30Gi
        requests:
          cpu: "4"
          memory: 14Gi
      ingresses:
        - enabled: true
          protocol: tcp
          port: 1433
          consulRegistry:
            enabled: true
        - enabled: true
          protocol: tcp
          port: 80
        - enabled: true
          protocol: tcp
          port: 8080