schema: v1
service: bpf-mock
environments:
  mock:
    spec:
      ports:
        - number: 8087
          protocol: TCP
      artifact:
        image:
          repository: bpf/bpf-mock
          tag: latest
      ingresses:
        - port: 8087
          protocol: tcp
      healthCheck:
        startup:
          httpGet:
            port: 8087
            path: /healthcheck
          initialDelaySeconds: 30
          periodSeconds: 2
          failureThreshold: 30
          timeoutSeconds: 1
        readiness:
          httpGet:
            port: 8087
            path: /healthcheck
          periodSeconds: 2
          failureThreshold: 3
          timeoutSeconds: 1
      resources:
        requests:
          cpu: "1"
          memory: 3Gi
        limits:
          memory: 6Gi
