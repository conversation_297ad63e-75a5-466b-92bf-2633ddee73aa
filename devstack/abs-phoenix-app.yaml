schema: v1
service: abs-phoenix-app
environments:
  dev:
    spec:
      ports:
        - number: 8080
          protocol: TCP
      artifact:
        image:
          repository: devops/devstack/service_proxifier
          tag: "latest"
      envs:
        CONFIG: |
          server {
              listen 8080 default_server;
              location / {
                  proxy_pass http://mock-api:8080;
                  proxy_http_version 1.1;
                  proxy_set_header Connection "Upgrade";
              }
          }
      ingress:
        enabled: true
        port: 8080
      healthCheck:
        startup:
          httpGet:
            path: /pingv2 # require upgrade
            port: 8080
          initialDelaySeconds: 1
          periodSeconds: 1
          failureThreshold: 60
          timeoutSeconds: 1
        readiness:
          httpGet:
            path: /pingv2 # require upgrade
            port: 8080
          periodSeconds: 5
          failureThreshold: 5
          timeoutSeconds: 1
      resources:
        requests:
          cpu: 50m
          memory: 256Mi
        limits:
          memory: 256Mi
    dependencies:
      - registry:
          service: mock-api # ABS mock
          environment: mock
          version: latest
