schema: v1
service: couchbase # todo: migrate to db-cbcache
environments:
  dev:
    spec:
      artifact:
        image:
          repository: dbdev/agoda_couchbase_cbcache
          tag: latest
      ports:
        - number: 8091 # Web console
          protocol: TCP
        - number: 8092 # Query service
          protocol: TCP
        - number: 8093 # Query service api
          protocol: TCP
        - number: 8094 # Full-text search service
          protocol: TCP
        - number: 11210 # Data service
          protocol: TCP
        - number: 11211 # Memcached
          protocol: TCP
      envs:
        BUCKETS: external-loyalty customer_api
      ingress:
        port: 8091
        enabled: true
      resources:
        limits:
          memory: 2Gi
        requests:
          cpu: 200m
          memory: 2Gi
      healthCheck:
        startup:
          tcpSocket:
            port: 8091
          initialDelaySeconds: 1
          failureThreshold: 300
          periodSeconds: 1
        readiness:
          tcpSocket:
            port: 8091
          failureThreshold: 3
          periodSeconds: 5
