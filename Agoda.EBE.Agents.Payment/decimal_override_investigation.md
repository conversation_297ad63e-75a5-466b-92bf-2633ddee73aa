I'll help you investigate the codebase to identify all places where decimal rounding logic, the `noDecimal` field, and currency-specific precision are implemented. Let me start by gathering information about the codebase structure and then search for the relevant patterns.

Now let me search for more specific patterns related to currency rounding and decimal precision:

Let me search for more specific patterns related to payment methods and any existing currency-payment method combinations:

Now let me search for any existing configuration or override patterns in the codebase:

Now let me search for stored procedures related to currency:

Let me search for the specific stored procedure that gets currency details:

Let me search for the specific stored procedure:

Let me search for the specific stored procedure in a different way:

Let me search for the actual stored procedure file:

Let me search for more currency-related calculations and any LinePay references:

Now let me search for more specific currency calculation patterns and any existing override mechanisms:

Based on my comprehensive investigation of the codebase, I can now provide you with a detailed analysis of all places where decimal rounding logic, the `noDecimal` field, and currency-specific precision are implemented. Here's my findings:

## Summary of Currency Decimal Precision Implementation

### 1. Core Currency Precision Logic

**File**: `Agoda.EBE.Agents.Payment/Processor/Common/CalculatorProcessor.cs`

````csharp path=Agoda.EBE.Agents.Payment/Processor/Common/CalculatorProcessor.cs mode=EXCERPT
/// <summary>
/// Get decimal precision for the given currency
/// </summary>
/// <param name="currencyKey"></param>
/// <returns>Returns 0 or 2</returns>
public int GetCurrencyDecimalDigitsPrecision(string currencyKey)
{
    var toReturn = 2;
    toReturn = _currencyDecimalDigits.GetOrAdd(currencyKey, (key) => new Lazy<int>(() => GetNumberOfDecimalDigits(key))).Value;

    return toReturn;
}

public int GetNumberOfDecimalDigits(string currency)
{
    try
    {
        var currencyDetail = _dataAccess.GetCurrencyDetail(currency);
        return currencyDetail.NoDecimal;
    }
    catch (Exception e)
    {
        _messaging.ExceptionMessage.Send($"CommonService.GetNumberOfDecimalDigits failed. Current: {currency}", e, LogLevel.WARN);
        return 2;
    }
}
````

**Description**: This is the central method that retrieves the `NoDecimal` field from the `afm_currency` table. It uses caching via `_currencyDecimalDigits` and falls back to 2 decimal places on error.

### 2. Main Rounding Methods

**File**: `Agoda.EBE.Agents.Payment/Processor/Common/CalculatorProcessor.cs`

````csharp path=Agoda.EBE.Agents.Payment/Processor/Common/CalculatorProcessor.cs mode=EXCERPT
public virtual decimal RoundCurrency(decimal number, string currency)
{
    return Math.Round(number, GetCurrencyDecimalDigitsPrecision(currency), MidpointRounding.AwayFromZero);
}

public virtual decimal ProcessCurrency(decimal number, string currency)
{
    int currencyDecimalPoint = GetCurrencyDecimalDigitsPrecision(currency);
    if (currencyDecimalPoint == 0)
    {
        return Math.Truncate(number);
    }
    else
    {
        return Math.Round(number, currencyDecimalPoint, MidpointRounding.AwayFromZero);
    }
}
````

**Description**: 
- `RoundCurrency`: Always rounds using `MidpointRounding.AwayFromZero`
- `ProcessCurrency`: Uses truncation for 0 decimal currencies, rounding for others

### 3. Currency Calculation Usage Points

**File**: `Agoda.EBE.Agents.Payment/Processor/Common/CalculatorProcessor.cs`

````csharp path=Agoda.EBE.Agents.Payment/Processor/Common/CalculatorProcessor.cs mode=EXCERPT
public decimal CalculateLocalAmount(decimal usdAmount, decimal exchangeRate, string localCurrency, bool isSupportTruncateMode = false)
{
    if (isSupportTruncateMode)
    {
        return ProcessCurrency(usdAmount * exchangeRate, localCurrency);
    }
    else
    {
        return RoundCurrency(usdAmount * exchangeRate, localCurrency);
    }
}
````

**Description**: Currency calculations can use either truncate mode (`ProcessCurrency`) or normal rounding (`RoundCurrency`) based on the `isSupportTruncateMode` flag.

### 4. Database Access for Currency Details

**File**: `Agoda.EBE.Agents.Payment/DataAccess/DataAccess.cs`

````csharp path=Agoda.EBE.Agents.Payment/DataAccess/DataAccess.cs mode=EXCERPT
public CurrencyDetail GetCurrencyDetail(string currency)
{
    //todo: Cache
    
    DynamicParameters parameters = new DynamicParameters();
    parameters.Add("currency_code", currency, DbType.AnsiString);

    return _connectionProvider.WithConnection(_configuration.AppEbeConn,
        conn => conn.QueryFirst<CurrencyDetail>("dbo.ebe_afm_currency_select_by_currency_code_v1", parameters,
            commandType: CommandType.StoredProcedure)
        , PaymentStatCode.DbGetCurrencyPrecision);
}
````

**Description**: This method calls the stored procedure `ebe_afm_currency_select_by_currency_code_v1` to get currency details including the `NoDecimal` field.

### 5. Currency Detail Object

**File**: `Agoda.EBE.Agents.Payment/Objects/CurrencyDetail.cs`

````csharp path=Agoda.EBE.Agents.Payment/Objects/CurrencyDetail.cs mode=EXCERPT
public class CurrencyDetail
{
    public int CurrencyId { get; set; }
    public string CurrencyName { get; set; }
    public string CurrencyCode { get; set; }
    public string NumericCode { get; set; }
    public int NoDecimal { get; set; }
}
````

**Description**: The `NoDecimal` property stores the decimal precision from the database.

### 6. Cancellation Processing with Currency Precision

**File**: `Agoda.EBE.Agents.Payment/Processor/Common/CancellationProcessor.cs`

````csharp path=Agoda.EBE.Agents.Payment/Processor/Common/CancellationProcessor.cs mode=EXCERPT
var supplierCurrency = latestCharge.SupplierLocalCurrency;
var supplierExchangeRate = latestCharge.SupplierExchangeRate;
var currencyDecimalPrecisionLocal = _calculatorProcessor.GetCurrencyDecimalDigitsPrecision(supplierCurrency);

var cxlBreakdowns = GetCancellationFeeBreakdown(cxlItem, booking, currentBreakdownItems, isNoCxlFee, supplierCurrency, supplierExchangeRate, currencyDecimalPrecisionLocal);
````

**Description**: Cancellation processing uses currency precision for breakdown calculations.

### 7. Financial Breakdown Calculations

**File**: `Agoda.EBE.Agents.Payment/Processor/Common/CancellationProcessor.cs`

````csharp path=Agoda.EBE.Agents.Payment/Processor/Common/CancellationProcessor.cs mode=EXCERPT
var localCurrencyRoundingNumber = _calculatorProcessor.GetCurrencyDecimalDigitsPrecision(perBookingBreakdown.LocalCurrency);

var itemsPerRoomPerNight = roomNightNetInclusiveItems.Select(item => perBookingBreakdown.CopyWith(
    (c => c.Date, item.Date),
    (c => c.RoomID, item.RoomID),
    (c => c.USDAmount, Math.Round(amountToDistributeUsd, DIGITS_AFTER_DECIMAL_POINT_USD, MidpointRounding.AwayFromZero)),
    (c => c.LocalAmount, Math.Round(amountToDistributeLocal, localCurrencyRoundingNumber, MidpointRounding.AwayFromZero)))
).ToList();
````

**Description**: Financial breakdown calculations use currency-specific precision for local amounts.

### 8. Payment Method Enums (No LinePay Found)

**File**: `Agoda.EBE.Agents.Payment/Enum.cs`

````csharp path=Agoda.EBE.Agents.Payment/Enum.cs mode=EXCERPT
public enum PaymentMethodId
{
    Visa = 1,
    Master = 2,
    JCB = 3,
    Amex = 4,
    PayPal = 5,
    Carte_Bleue = 6,
    IPS = 7,
    IDEAL = 8,
    Alipay = 9,
    Rewards = 11
}
````

**Description**: Current payment method enums don't include LinePay. This would need to be added for the new override logic.

### 9. Existing Override Pattern Example

**File**: `docker/configs/appsettings-prod-central.json`

````json path=docker/configs/appsettings-prod-central.json mode=EXCERPT
"EnableOverridePaymentStatus": true,
"PaymentStatusOverrideMapping": [
    {
        "PaymentResponseStatus": 4,
        "ErrorCode": 455,
        "OverrideStatus": 2
    }
]
````

**Description**: The codebase already has an override pattern for payment status that could serve as a model for currency precision overrides.

## Key Findings for Runtime Override Implementation

### Current Architecture:
1. **Single Source of Truth**: All currency precision comes from `afm_currency.NoDecimal` field
2. **Centralized Logic**: `GetCurrencyDecimalDigitsPrecision()` is the single entry point
3. **Caching**: Currency precision is cached using `ConcurrentDictionary<string, Lazy<int>>`
4. **Two Rounding Modes**: `RoundCurrency` (always rounds) vs `ProcessCurrency` (truncates for 0-decimal currencies)

### Implementation Strategy:
To implement the runtime override based on currency + payment method, you would need to:

1. **Modify `GetCurrencyDecimalDigitsPrecision()`** to accept an additional payment method parameter
2. **Add configuration** similar to the existing `PaymentStatusOverrideMapping` pattern
3. **Update all call sites** to pass payment method information
4. **Add LinePay** to the `PaymentMethodId` enum
5. **Update caching strategy** to include payment method in the cache key

### Critical Call Sites to Update:
- `CalculateLocalAmount()` methods
- `RoundCurrency()` and `ProcessCurrency()` methods  
- Cancellation processing logic
- Financial breakdown calculations
- All currency calculation workflows

The existing architecture is well-structured for this enhancement, as the currency precision logic is already centralized and the codebase has patterns for configuration-based overrides.
