using System;
using System.Linq;
using Agoda.Adp.Messaging.Client;
using Agoda.Config.Consul.KeyValue.FeatureSwitch.Services;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.EBE.Agents.Common.Util;
using Agoda.EBE.Agents.Payment.Objects;
using Agoda.EBE.Agents.Payment.Service.Interface;
using Agoda.EBE.Agents.Payment.Configuration.FeatureSwitches;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.Objects;
using Agoda.WhiteLabelApi.Client.Models;
using Newtonsoft.Json;
using Enum = Agoda.EBE.Agents.Common.Enum;

namespace Agoda.EBE.Agents.Payment.Processor.Common.ExternalLoyalty
{
    public class ExternalLoyaltyRefundBenefitsProcessor : Interface.IExternalLoyaltyRefundBenefitsProcessor
    {
        private readonly IExternalLoyaltyApiService _externalLoyaltyApiService;
        private readonly IMessaging _messaging;
        private readonly IWhiteLabelConfigService _whiteLabelConfigService;
        private readonly IWhiteLabelHelperService _whiteLabelHelperService;
        private readonly IFeatureSwitchService _featureSwitchService;

        public ExternalLoyaltyRefundBenefitsProcessor(
            IMessaging messaging,
            IExternalLoyaltyApiService externalLoyaltyApiService,
            IWhiteLabelConfigService whiteLabelConfigService,
            IWhiteLabelHelperService whiteLabelHelperService,
            IFeatureSwitchService featureSwitchService
            )
        {
            _messaging = messaging;
            _externalLoyaltyApiService = externalLoyaltyApiService;
            _whiteLabelConfigService = whiteLabelConfigService;
            _whiteLabelHelperService = whiteLabelHelperService;
            _featureSwitchService = featureSwitchService;
        }
        public void Process(ItineraryBookingItem bookingItem, CancellationInfo cxlInfo, LoyaltyInfo loyaltyInfo)
        {
            try
                {
                    var shouldSkipDueToRefundBenefitMigration = ShouldSkipDueToRefundBenefitMigration(bookingItem);
                    if (shouldSkipDueToRefundBenefitMigration)
                    {
                        _messaging.ExceptionMessage.Send(
                            $"External refund benefit is skipped due to the migration. Booking ID: {bookingItem.BookingId}",
                            null,
                            LogLevel.INFO,
                            new TagBuilder().AddItineraryTag(bookingItem.ItineraryId).Build());
                        return;
                    }
                    
                    var skipExternalRefundPromo = ShouldSkipExternalRefund(bookingItem, loyaltyInfo);

                    if (skipExternalRefundPromo)
                    {
                        _messaging.ExceptionMessage.Send(
                            $"External refund benefit is skipped by WL Promo Config(Non Citi promocode). Booking ID: {bookingItem.BookingId}",
                            null,
                            LogLevel.INFO,
                            new TagBuilder().AddItineraryTag(bookingItem.ItineraryId).Build());
                        return;
                    }

                    var externalLoyaltyApiServiceResponse = _externalLoyaltyApiService.RefundBenefits(
                        partnerClaimToken: loyaltyInfo.PartnerClaimToken, 
                        memberId: bookingItem.MemberId,
                        bookingId: cxlInfo.BookingId,
                        itineraryId: bookingItem.ItineraryId,
                        whiteLabelId: bookingItem.WhitelabelId.Value,
                        promocode: loyaltyInfo.Promocode,
                        discountAmountUsd: loyaltyInfo.DiscountAmountUsd,
                        bookingAmountUsd: loyaltyInfo.BookingAmountUsd,
                        isTestBooking: bookingItem.IsTestBooking
                        );

                    _messaging.ExceptionMessage.Send(
                        $"ELAPI Refund benefit response: {JsonConvert.SerializeObject(externalLoyaltyApiServiceResponse)}",
                        null,
                        LogLevel.INFO,
                        new TagBuilder().AddItineraryTag(bookingItem.ItineraryId).Build());

                    if (!String.IsNullOrEmpty(externalLoyaltyApiServiceResponse.Message) && externalLoyaltyApiServiceResponse.RefundedCampaign == null)
                    {
                        _messaging.ExceptionMessage.Send(
                            $"ExternalLoyaltyRefundBenefitsProcessor fails. Booking Id: {bookingItem.BookingId}",
                            new Exception(externalLoyaltyApiServiceResponse.Message),
                            LogLevel.FATAL,
                            new TagBuilder().AddItineraryTag(bookingItem.ItineraryId).Build());
                    }
                    else
                    {
                        _messaging.ExceptionMessage.Send(
                            $"External refund benefit is successful. Booking ID: {bookingItem.BookingId}",
                            null,
                            LogLevel.INFO,
                            new TagBuilder().AddItineraryTag(bookingItem.ItineraryId).Build());
                    }
                }
                catch (Exception exception)
                {
                    /* Note. It is by intention to skip the exception. Let CEG handle the case of refund benefit manually. */
                    _messaging.ExceptionMessage.Send(
                        $"ExternalLoyaltyRefundBenefitsProcessor fails. Booking Id: {bookingItem.BookingId}",
                        exception,
                        LogLevel.FATAL,
                        new TagBuilder().AddItineraryTag(bookingItem.ItineraryId).Build());
                }
        }

        private bool ShouldSkipExternalRefund(ItineraryBookingItem bookingItem, LoyaltyInfo loyaltyInfo)
        {
            string whiteLabelToken = _whiteLabelHelperService.ResolveWhiteLabelToken(bookingItem.WhitelabelId);
            Guid guid;
            bool isBenefitRedemptionFeatureEnabled =
                Guid.TryParse(whiteLabelToken, out guid)
                && _whiteLabelConfigService.IsFeatureEnabled(WhiteLabelFeatureName.BenefitRedemption, guid, bookingItem.IsTestBooking, bookingItem.WhitelabelId);
            Console.WriteLine("Benefit Redemption Feature Enabled: ->> " + isBenefitRedemptionFeatureEnabled);    
            if (isBenefitRedemptionFeatureEnabled)
            {
                _messaging.ExceptionMessage.Send(
                    $"External refund benefit is using BenefitRedemption config. Booking ID: {bookingItem.BookingId}",
                    null,
                    LogLevel.INFO,
                    new TagBuilder().AddItineraryTag(bookingItem.ItineraryId).Build());
                var benefitRedemptionConfig =
                    _whiteLabelConfigService.GetFeatureConfigByKey<BenefitRedemption>(whiteLabelToken, WhiteLabelConfigName.BenefitRedemption, bookingItem.IsTestBooking);
                /*
                 * New config
                 * Skip external refund if booking promo is not eligible: doesn't exist in WL config
                 */
                var skipExternalRefundPromo =
                    !benefitRedemptionConfig.EligibleForRedemptionPromotionCodes.Contains(loyaltyInfo.Promocode);
                return skipExternalRefundPromo;
            }

            return true;
        }

        private bool ShouldSkipDueToRefundBenefitMigration(ItineraryBookingItem bookingItem)
        {
            var isRefundBenefitMigrationEnable =
                _featureSwitchService.IsOn<FeatureSwitch_Payment_RefundBenefitMigration>();

            if (!isRefundBenefitMigrationEnable) return false;
            
            string whiteLabelToken = _whiteLabelHelperService.ResolveWhiteLabelToken(bookingItem.WhitelabelId);
            Guid guid;
            bool isBenefitRefundFeatureEnabled =
                Guid.TryParse(whiteLabelToken, out guid)
                && _whiteLabelConfigService.IsFeatureEnabled(WhiteLabelFeatureName.BenefitRefundMigration, guid, bookingItem.IsTestBooking, bookingItem.WhitelabelId);
            
            _messaging.ExceptionMessage.Send(
                $"External(CITI) refund benefit WL Config for: {WhiteLabelFeatureName.BenefitRefundMigration} with " +
                $"WLKey: {guid.ToString()} Booking ID: {bookingItem.BookingId}, WL_ID: {bookingItem.WhitelabelId}; " +
                $"Got isBenefitRefundFeatureEnabled: {isBenefitRefundFeatureEnabled}, and isRefundBenefitMigrationEnable: {isRefundBenefitMigrationEnable}",
                null,
                LogLevel.INFO,
                new TagBuilder().AddItineraryTag(bookingItem.ItineraryId).Build());

            return isBenefitRefundFeatureEnabled;
        }
    }
}