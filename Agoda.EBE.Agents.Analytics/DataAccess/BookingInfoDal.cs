using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Analytics.Object;
using Agoda.EBE.Agents.Common.EbeCommonException;
using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Agents.Common.Util.Data;
using Agoda.EBE.Framework;
using Agoda.EBE.Framework.Data;
using Agoda.EBE.Framework.Extensions;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.TimeZone;
using Newtonsoft.Json;
using CreditCardInfo = Agoda.EBE.Agents.Analytics.Object.CreditCardInfo;
using PaymentInfo = Agoda.EBE.Agents.Analytics.Object.PaymentInfo;

namespace Agoda.EBE.Agents.Analytics.DataAccess
{
    public partial class DataAccess
    {
        public BookingDetail GetBookingDetail(int bookingId, int eventWorkflowStateId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@booking_id", bookingId)
            };
            var stat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            var storeProcedureName = "dbo.ebe_analytics_agent_get_booking_info_v24";

            try
            {
                stat.BeginTrack();
                var ds = _databaseProvider.ExecuteDataSet(storeProcedureName, parameterInfos);
                if (ds.HasRowInTable(0))
                {
                    var row = ds.Tables[0].Rows[0];

                    var benefitIdList = row.ParseDataRowItem<string>("benefit_id_list", string.Empty);
                    var benefitId = benefitIdList.TrimEnd(',');
                    var compressedAdditionalInfoData = row.ParseDataRowItem<byte[]>("additional_info");
                    var additionalInfo = compressedAdditionalInfoData == null
                        ? string.Empty
                        : Encoding.UTF8.GetString(Compression.DecompressBytes(compressedAdditionalInfoData));

                    var clientInfo = GetClientInfo(row, bookingId);
                    var osVersion = clientInfo.OS_Version;
                     //to check if booking is from a third party supplier
                    bool is_pull= row.ParseDataRowItem<bool>("is_pull", false) && row.ParseDataRowItem<bool>("is_third_party_supply", false);
                    
                    var bookingDetail = new BookingDetail
                    {
                        Benefit_Id = benefitId,
                        Additional_Info = additionalInfo,
                        Payment_Info = GetPaymentInfo(row),
                        CreditCard_Info = GetCreditCardInfo(row),
                        Hotel_Info = GetHotelInfo(row),
                        Discount_Info = GetDiscountInfo(row),
                        Customer_Info = GetCustomerInfo(row),
                        Cancellation_Info = GetCancellationInfo(row),
                        Cid_Info = GetCidInfo(row),
                        Client_Info = clientInfo,
                        System_Info = GetSystemInfo(row),
                        Charge_Info = GetChargeInfo(row),
                        Sell_Info = GetSellInfo(row),
                        Room_Info = GetRoomInfo(row),
                        Language_Details = GetLanguageDetails(row),
                        Booking_Id = row.ParseDataRowItem<int>("booking_id", 0),
                        Event_Workflow_State_Id = eventWorkflowStateId,
                        Event_Date = row.ParseDataRowItem<DateTime>("booking_history_date", DateTime.MinValue),
                        Booking_Date = row.ParseDataRowItem<DateTime>("booking_date", DateTime.MinValue),
                        Prebooking_Id = row.ParseDataRowItem<long>("prebooking_id", 0),
                        Itinerary_Id = row.ParseDataRowItem<int>("itinerary_id", 0),
                        Booking_History_Id = row.ParseDataRowItem<long>("booking_history_id", 0),
                        Language_Id = row.ParseDataRowItem<int>("language_id", 0),
                        Site_Id = row.ParseDataRowItem<int>("cid_list", 0),
                        Current_Checkin_Date = row.ParseDataRowItem<DateTime>("booking_date_from", DateTime.MinValue),
                        Current_Checkout_Date = row.ParseDataRowItem<DateTime>("booking_date_until", DateTime.MinValue),
                        Booking_Phase_Description = row.ParseDataRowItem<string>("description", string.Empty),
                        DMC_Code = row.ParseDataRowItem<string>("dmc_code", string.Empty),
                        Storefront_Id = row.ParseDataRowItem<int>("storefront_id", 0),
                        Platform_Id = row.ParseDataRowItem<string>("platform_id", string.Empty),
                        Fraud_Check_Ip = row.ParseDataRowItem<string>("fraud_check_ip", string.Empty),
                        Booking_External_Reference =
                            row.ParseDataRowItem<string>("booking_external_reference", string.Empty),
                        DMC_Id = row.ParseDataRowItem<int>("dmc_id", 0),
                        Fe_Cid = row.ParseDataRowItem<string>("fe_cid", string.Empty),
                        Is_Not_Cc_Required = row.ParseDataRowItem<bool>("is_not_cc_required", false),
                        Rate_Channel = row.ParseDataRowItem<string>("rate_channel", string.Empty),
                        Repeated_Booker = row.ParseDataRowItem<bool>("Repeated_Booker", false),
                        Is_Confirmed_Booking = row.ParseDataRowItem<bool>("is_confirmed_booking", false),
                        Payment_Model = row.ParseDataRowItem<int>("payment_model", 0),
                        Cid_Display_Name = row.ParseDataRowItem<string>("cid_display_name", string.Empty),
                        Whitelabel_Id = row.ParseDataRowItem<int>("whitelabel_id", 0),
                        Multi_Product_Id = row.ParseDataRowItem<int>("multi_product_id", 0),
                        Payment_Gateway_Id = row.ParseDataRowItem<int>("payment_gateway_id", 0),
                        Stay_Package_Type = row.ParseDataRowItem<int>("stay_package_type", 0),
                        Stay_Type = row.ParseDataRowItem<int>("stay_type", 0),
                        Availability_Type = row.ParseDataRowItem<int>("availability_type", 0),
                        Promotion_Code = row.ParseDataRowItem<string>("promotion_code", string.Empty),
                        Promotion_Campaign_Id = row.ParseDataRowItem<int>("promotion_campaign_id", 0),
                        Is_Promotion = row.ParseDataRowItem<bool>("is_promotion", false),
                        Is_Credit_Card_Promotion = row.ParseDataRowItem<bool>("is_credit_card_promotion", false),
                        Is_Test_Booking = row.ParseDataRowItem<bool>("is_test_booking", false),
                        Platform_Group_Name = row.ParseDataRowItem<string>("platform_group_name", string.Empty),
                        Site_Origin = row.ParseDataRowItem<string>("site_origin", string.Empty),
                        Is_AAB = row.ParseDataRowItem<bool>("is_aab", false),
                        Is_Smart_Flex = row.ParseDataRowItem<bool>("is_smart_flex", false),
                        Original_Cancellation_Policy_Code = row.ParseDataRowItem<string>("original_cancellation_policy_code", string.Empty),
                        OS_Version = osVersion,
                        Is_Pull_Third_Party_Supply = is_pull
                    };
                    stat.EndTrack(AnalyticsDalEnum.GetBookingDetail);
                    return bookingDetail;
                }

                throw new ExecuteStoreProcReturnNoRow(storeProcedureName,
                    $"bookingId: {bookingId}");
            }
            catch (Exception ex)
            {
                stat.EndTrack(AnalyticsDalEnum.GetBookingDetail, false);
                _messaging.ExceptionMessage.Send($"GetBookingDetail bookingId: {bookingId} fail", ex, LogLevel.FATAL,
                    new TagBuilder().AddBookingTag(bookingId).Build());
                throw;
            }
        }

        private static PaymentInfo GetPaymentInfo(DataRow dr)
        {
            return new PaymentInfo
            {
                Payment_Amount_USD = dr.ParseDataRowItem<decimal>("total_amount", 0),
                Charge_Option = dr.ParseDataRowItem<string>("charge_option", string.Empty),
                Currency = dr.ParseDataRowItem<string>("payment_currency", string.Empty)
            };
        }

        private static CreditCardInfo GetCreditCardInfo(DataRow dr)
        {
            return new CreditCardInfo
            {
                CreditCard_Type = dr.ParseDataRowItem<string>("creditcard_type", string.Empty),
                CC_ID = dr.ParseDataRowItem<long>("cc_id", 0)
            };
        }

        private static HotelInfo GetHotelInfo(DataRow dr)
        {
            return new HotelInfo
            {
                Hotel_Id = dr.ParseDataRowItem<int>("hotel_id", 0),
                Hotel_Name = dr.ParseDataRowItem<string>("hotel_name", string.Empty),
                No_Of_Rooms = dr.ParseDataRowItem<int>("no_of_rooms", 0),
                No_Of_Children = dr.ParseDataRowItem<int>("no_of_children", 0),
                No_Of_Adults = dr.ParseDataRowItem<int>("no_of_adults", 0),
                No_Of_Extrabeds = dr.ParseDataRowItem<int>("no_of_extrabeds", 0),
                Room_type_ID = dr.ParseDataRowItem<int>("room_type_id", 0),
                Prepayment_Flag = dr.ParseDataRowItem<string>("is_agency_prepay", string.Empty),
                Occupancy = dr.ParseDataRowItem<string>("occupancy", string.Empty),
                Is_Agoda_Reception = dr.ParseDataRowItem<bool>("is_agoda_reception", string.Empty),
                Is_Non_Hotel_Accommodation_Mode = dr.ParseDataRowItem<bool>("is_non_hotel_accommodation_mode", false),
                Is_NHA_For_Display = dr.ParseDataRowItem<bool>("is_nha_for_display", false),
                Is_NHA_Enabled = dr.ParseDataRowItem<int>("is_nha_enabled", 0),
                City_Id = dr.ParseDataRowItem<int>("city_id", 0),
                Country_Id = dr.ParseDataRowItem<int>("country_id", 0),
                Country_Name = dr.ParseDataRowItem<string>("country_name", string.Empty),
                Country_Iso2 = dr.ParseDataRowItem<string>("country_iso2", string.Empty),
                City_Name = dr.ParseDataRowItem<string>("city_name", string.Empty),
                Hotel_Continent = dr.ParseDataRowItem<string>("continent_name", string.Empty),
                Chain_Id = dr.ParseDataRowItem<int>("chain_id", 0),
                Gmt_Offset = dr.ParseDataRowItem<int>("gmt_offset", 0),
                Gmt_Offset_Minutes = dr.ParseDataRowItem<int>("gmt_offset_minutes", 0),
            };
        }

        private DiscountInfo GetDiscountInfo(DataRow dr)
        {
            return new DiscountInfo
            {
                Promotion_Code = dr.ParseDataRowItem<string>("promotion_code", string.Empty)
            };
        }

        private static CustomerInfo GetCustomerInfo(DataRow dr)
        {
            return new CustomerInfo
            {
                Member_Id = dr.ParseDataRowItem<int>("memberid", 0)
            };
        }

        private static CancellationInfo GetCancellationInfo(DataRow dr)
        {
            return new CancellationInfo
            {
                Cancellation_Date = dr.ParseDataRowItem<DateTime>("cxl_date", DateTime.MinValue),
                Cancellation_Policy_Code = dr.ParseDataRowItem<string>("cancellation_policy_code", string.Empty)
            };
        }

        private static ClientInfo GetClientInfo(DataRow dr, int bookingId)
        {
            ClientInfo GetClientInfoFromCompressedDeviceInfo(byte[] compressedDeviceInfoBytes)
            {
                var decompressedDeviceInfo =
                    Encoding.UTF8.GetString(Compression.DecompressBytes(compressedDeviceInfoBytes));
                var deviceInfo = JsonConvert.DeserializeObject<DeviceInfo>(decompressedDeviceInfo);
                return new ClientInfo
                {
                    OS_Name = deviceInfo.osName,
                    OS_Version = deviceInfo.osVersion,
                    Browser_Name = deviceInfo.browserName,
                    Device_Brand = deviceInfo.deviceBrand,
                    Device_Model = deviceInfo.deviceModel,
                    Origin = deviceInfo.origin,
                    Browser_Version = deviceInfo.browserVersion,
                    Browser_Subversion = deviceInfo.browserSubVersion,
                    Browser_Build_Number = deviceInfo.browserBuildNumber,
                    Is_Mobile = deviceInfo.isMobile,
                    Is_Touch = deviceInfo.isTouch,
                    Device_Type_ID = deviceInfo.deviceTypeId,
                    SearchCluster = deviceInfo.searchCluster
                };
            }

            ClientInfo GetClientInfoFromDataRow()
            {
                return new ClientInfo
                {
                    OS_Name = dr.ParseDataRowItem<string>("os_name", string.Empty),
                    OS_Version = dr.ParseDataRowItem<string>("os_version", string.Empty),
                    Browser_Name = dr.ParseDataRowItem<string>("browser_name", string.Empty),
                    Device_Brand = dr.ParseDataRowItem<string>("device_brand", string.Empty),
                    Device_Model = dr.ParseDataRowItem<string>("device_model", string.Empty),
                    Origin = dr.ParseDataRowItem<string>("origin", string.Empty),
                    Browser_Version = dr.ParseDataRowItem<string>("browser_version", string.Empty),
                    Browser_Subversion = dr.ParseDataRowItem<string>("browser_subversion", string.Empty),
                    Browser_Build_Number = dr.ParseDataRowItem<string>("browser_build_number", string.Empty),
                    Is_Mobile = dr.ParseDataRowItem<bool>("is_mobile", false),
                    Is_Touch = dr.ParseDataRowItem<bool>("is_touch", false),
                    Device_Type_ID = "0"
                };
            }

            var compressedDeviceInfo = dr.ParseDataRowItem<byte[]>("device_info");
            ClientInfo clientInfo = null;
            try
            {
                //All bkgs nowsday contain compressed device info
                clientInfo = GetClientInfoFromCompressedDeviceInfo(compressedDeviceInfo);
            }
            catch (Exception ex)
            {
                clientInfo = GetClientInfoFromDataRow();
            }

            return clientInfo.CopyWith(
                (c => c.Client_IP_Address, dr.ParseDataRowItem<string>("client_ip_address", string.Empty)),
                (c => c.Session_Id, dr.ParseDataRowItem<string>("session_id", string.Empty)),
                (c => c.Tracking_Cooking_Id, dr.ParseDataRowItem<string>("tracking_cookie_id", string.Empty)),
                (c => c.Is_User_Logged_In, dr.ParseDataRowItem<bool>("is_user_logged_in", false))
            );
        }

        private static SystemInfo GetSystemInfo(DataRow dr)
        {
            return new SystemInfo
            {
                Server_Name = dr.ParseDataRowItem<string>("server_name", string.Empty),
                Data_Center = dr.ParseDataRowItem<string>("datacenter", string.Empty)
            };
        }

        private static ChargeInfo GetChargeInfo(DataRow dr)
        {
            var originalSellingAmount = dr.ParseDataRowItem<decimal>("original_selling_amount", 0);
            var originalSupplierAmount = dr.ParseDataRowItem<decimal>("original_supplier_amount", 0);
            return new ChargeInfo
            {
                Original_Selling_Amount_USD = originalSellingAmount,
                Original_Supplier_Amount_USD = originalSupplierAmount,
                Original_Margin_Amount_USD = originalSellingAmount - originalSupplierAmount,
                Current_Selling_Amount_USD = dr.ParseDataRowItem<decimal>("current_selling_amount", 0),
                Current_Supplier_Amount_USD = dr.ParseDataRowItem<decimal>("current_supplier_amount", 0)
            };
        }

        private static CidInfo GetCidInfo(DataRow dr)
        {
            return new CidInfo
            {
                Site_Id = dr.ParseDataRowItem<int>("traffic_group_site_id", 0),
                Traffic_Group_Id = dr.ParseDataRowItem<int>("traffic_group_id", 0),
                Traffic_Group_Name = dr.ParseDataRowItem<string>("traffic_group_name", string.Empty),
                Traffic_Id = dr.ParseDataRowItem<int>("traffic_id", 0),
                Traffic_Name = dr.ParseDataRowItem<string>("traffic_name", string.Empty),
                Affiliate_Id = dr.ParseDataRowItem<int>("affiliate_id", 0),
                Affiliate_Name = dr.ParseDataRowItem<string>("affiliate_name", string.Empty),
            };
        }

        private static SellInfo GetSellInfo(DataRow dr)
        {
            var downLiftAmount = dr.ParseDataRowItem<decimal>("downlift_amount_usd", 0);
            var priceLift = downLiftAmount * -1;
            return new SellInfo
            {
                Sell_Tag_Id = dr.ParseDataRowItem<string>("sell_tag_id", string.Empty),
                Pricing_Template_Id = dr.ParseDataRowItem<string>("pricing_template_id", string.Empty),
                Search_Id = dr.ParseDataRowItem<string>("search_id", string.Empty),
                Pricelift = priceLift.ToString(),
                Offer_Id = dr.ParseDataRowItem<string>("offer_id", string.Empty)
            };
        }

        private static RoomInfo GetRoomInfo(DataRow dr)
        {
            return new RoomInfo
            {
                Occupancy = dr.ParseDataRowItem<int>("occupancy", 0)
            };
        }

        private static LanguageDetails GetLanguageDetails(DataRow dr)
        {
            return new LanguageDetails
            {
                Language_Id = dr.ParseDataRowItem<int>("language_id", 0),
                Language_Name = dr.ParseDataRowItem<string>("language_name", string.Empty),
                Language_Abbr = dr.ParseDataRowItem<string>("language_abbr", string.Empty)
            };
        }

        // the below method is just for tests.
        public HotelInfo GetHotelInfoPublic(DataRow dataRow)
            {
                return GetHotelInfo(dataRow);
            }
    }
}
