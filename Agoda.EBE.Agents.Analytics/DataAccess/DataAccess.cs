using System;
using Agoda.EBE.Agents.Analytics.DataAccess.Interface;
using Agoda.EBE.Agents.Analytics.Object;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Util.Data;
using Agoda.EBE.Agents.Common.Util.Data.Interface;

namespace Agoda.EBE.Agents.Analytics.DataAccess
{
    public partial class DataAccess : IDataAccess
    {
        private readonly IMessaging _messaging;
        private readonly AnalyticsConfiguration _configuration;
        private readonly IDatabaseProvider _databaseProvider;

        public DataAccess(IMessaging messaging, AnalyticsConfiguration configuration, IDatabaseProvider databaseProvider)
        {
            _messaging = messaging ?? throw new ArgumentException(nameof(_messaging));
            _configuration = configuration ?? throw new ArgumentException(nameof(_configuration));
            _databaseProvider = databaseProvider ?? throw new ArgumentException(nameof(_databaseProvider));
        }
    }
}