using System;
using System.Linq;
using Agoda.EBE.Agents.Common.Configuration;
using Agoda.EBE.Agents.Common.Configuration.Interface;
using Agoda.EBE.Agents.Common.Interface;

namespace Agoda.EBE.Agents.Analytics.Object
{
    public class AnalyticsConfiguration: ConsoleConfiguration, IConfiguration, IQueueConfiguration
    {
        private const string QueueConnectionStringKey = "RabbitMQ";
        private const string ApplicationEbeConnectionStringKey = "application_ebe";
        
        public string QueueConnectionString => ConnectionStrings?.FirstOrDefault(f => f.Name == QueueConnectionStringKey)?.Value;
        public string AppEbeConn => ConnectionStrings?.FirstOrDefault(f => f.Name == ApplicationEbeConnectionStringKey)?.Value;
        
        public bool IsActive { get; set; }
        public int StoreFrontID { get; set; }
        public int MaxProcessParallel { get; set; }
        public string MetricName { get; set; }
        public Guid UserGuid { get; set; }
        public string ComponentName { get; set; }
        public string QueueName { get; set; }
        public int QueueMaxSubscriber { get; set; }
    }
}