using System;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.Objects;
using Agoda.Rewards.Api.Security.Service;

namespace Agoda.EBE.Agents.Analytics.Object
{
    public class BookingDetail : EBEGenericMessage
    {
        public bool Repeated_Booker { get; set; }
        public int Booking_Id { get; set; }
        public int Event_Workflow_State_Id { get; set; }
        public long Prebooking_Id { get; set; }
        public int Itinerary_Id { get; set; }
        public long Booking_History_Id { get; set; }
        public DateTime Event_Date { get; set; }
        public DateTime Booking_Date { get; set; }
        public int Language_Id { get; set; }
        public int Site_Id { get; set; }
        public DateTime Current_Checkin_Date { get; set; }
        public DateTime Current_Checkout_Date { get; set; }
        public string Booking_Phase_Description { get; set; }
        public string DMC_Code { get; set; }
        public int DMC_Id { get; set; }
        public int Storefront_Id { get; set; }
        public string Platform_Id { get; set; }
        public string Fraud_Check_Ip { get; set; }
        public string Booking_External_Reference { get; set; }
        public string Fe_Cid { get; set; }
        public bool Is_Not_Cc_Required { get; set; }
        public string Rate_Channel { get; set; }
        public string Benefit_Id { get; set; }
        public string Additional_Info { get; set; }
        public PaymentInfo Payment_Info { get; set; }
        public CreditCardInfo CreditCard_Info { get; set; }
        public HotelInfo Hotel_Info { get; set; }
        public DiscountInfo Discount_Info { get; set; }
        public CustomerInfo Customer_Info { get; set; }
        public CancellationInfo Cancellation_Info { get; set; }
        public ClientInfo Client_Info { get; set; }
        public SystemInfo System_Info { get; set; }
        public ChargeInfo Charge_Info { get; set; }
        public SellInfo Sell_Info { get; set; }
        public RoomInfo Room_Info { get; set; }
        public LanguageDetails Language_Details { get; set; }
        public CidInfo Cid_Info { get; set; }
        public bool Is_Confirmed_Booking { get; set; }
        public int Payment_Model { get; set; }
        public string Cid_Display_Name { get; set; }
        public int Whitelabel_Id { get; set; }
        public int Multi_Product_Id { get; set; }
        public int Payment_Gateway_Id { get; set; }
        public int Stay_Package_Type { get; set; }
        public int Stay_Type { get; set; }
        public int Availability_Type { get; set; }
        public string Promotion_Code { get; set; }
        public int Promotion_Campaign_Id { get; set; }
        public bool Is_Promotion { get; set; }
        public bool Is_Credit_Card_Promotion { get; set; }
        public bool Is_Test_Booking { get; set; }
        public string Platform_Group_Name { get; set; }
        public string Site_Origin { get; set; }
        public bool Is_AAB { get; set; }
        public bool Is_Smart_Flex { get; set; }
        public string OS_Version { get; set; }
        public string Original_Cancellation_Policy_Code { get; set; }
        public bool Is_Pull_Third_Party_Supply { get; set; }
    }
}