using Agoda.EBE.Framework.TimeZone;

namespace Agoda.EBE.Agents.Analytics.Object
{
    public class HotelInfo
    {
        public int Hotel_Id { get; set; }
        public string Hotel_Name { get; set; }
        public int No_Of_Rooms { get; set; }
        public int No_Of_Adults { get; set; }
        public int No_Of_Children { get; set; }
        public int No_Of_Extrabeds { get; set; }
        public int Room_type_ID { get; set; }
        public string Prepayment_Flag { get; set; }
        public string Occupancy { get; set; }
        public bool Is_Agoda_Reception { get; set; }
        public bool Is_Non_Hotel_Accommodation_Mode { get; set; }
        public bool Is_NHA_For_Display { get; set; }
        public int Is_NHA_Enabled { get; set; }
        public int Country_Id { get; set; }
        public string Country_Name { get; set; }
        public string Country_Iso2 { get; set; }
        public int City_Id { get; set; }
        public string City_Name { get; set; }
        public string Hotel_Continent { get; set; }
        public int Chain_Id { get; set; }
        public int Gmt_Offset { get; set; }
        public int Gmt_Offset_Minutes { get; set; }
    }
}