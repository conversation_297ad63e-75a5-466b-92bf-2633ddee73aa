using System;

namespace Agoda.EBE.Agents.Analytics.Object
{
    public class DeviceInfo
    {
        public string osName { get; set; }
        public string osVersion { get; set; }
        public string browserName { get; set; }
        public string deviceBrand { get; set; }
        public string deviceModel { get; set; }
        public string origin { get; set; }
        public string browserVersion { get; set; }
        public string browserSubVersion { get; set; }
        public string browserBuildNumber { get; set; }
        public bool isMobile { get; set; }
        public bool isTouch { get; set; }
        public string deviceTypeId { get; set; }
        public string searchCluster { get; set; } = String.Empty;
    }
}