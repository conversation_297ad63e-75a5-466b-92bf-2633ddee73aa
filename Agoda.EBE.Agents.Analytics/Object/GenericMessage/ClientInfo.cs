using System;

namespace Agoda.EBE.Agents.Analytics.Object
{
    public class ClientInfo
    {
        public string OS_Name { get; set; }
        public string OS_Version { get; set; }
        public string Browser_Name { get; set; }
        public string Client_IP_Address { get; set; }
        public string Session_Id { get; set; }        
        public string Tracking_Cooking_Id { get; set; }
        public bool Is_User_Logged_In { get; set; }
        public string Device_Brand { get; set; }
        public string Device_Model { get; set; }
        public string Origin { get; set; }
        public string Browser_Version { get; set; }
        public string Browser_Subversion { get; set; }
        public string Browser_Build_Number { get; set; }
        public bool Is_Mobile { get; set; }
        public bool Is_Touch { get; set; }
        public string Device_Type_ID { get; set; }
        public string Additional_Info { get; set; }
        public string SearchCluster { get; set; } = String.Empty;
    }
}