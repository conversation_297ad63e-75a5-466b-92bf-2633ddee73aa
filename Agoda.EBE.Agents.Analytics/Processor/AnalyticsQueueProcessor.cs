using System;
using System.Threading;
using Agoda.EBE.Agents.Analytics.Object;
using Agoda.EBE.Agents.Analytics.Processor.Interface;
using Agoda.EBE.Agents.Common.Base;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Framework.MQ.Interface;
using Agoda.EBE.Framework.MQ.MessageType;
using EasyNetQ;

namespace Agoda.EBE.Agents.Analytics.Processor
{
    public class AnalyticsQueueProcessor: QueueProcessorBase, IAnalyticsQueueProcessor
    {
        private readonly AnalyticsConfiguration _configuration;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly IAnalyticsDataProcessor _dataProcessor;
        private readonly IMessaging _messaging;
        
        public AnalyticsQueueProcessor(
            IMessaging messaging,
            AnalyticsConfiguration configuration,
            IAnalyticsDataProcessor dataProcessor,
            CancellationTokenSource cancellationTokenSource,            
            IRabbitMQAdapter rabbitMqAdapter
        ) : base(rabbitMqAdapter)
        {
            _messaging = messaging ?? throw new ArgumentException(nameof(_messaging));
            _configuration = configuration ?? throw new ArgumentException(nameof(_configuration));
            _dataProcessor = dataProcessor ?? throw new ArgumentException(nameof(_dataProcessor));
            _cancellationTokenSource = cancellationTokenSource ?? throw new ArgumentNullException(nameof(_cancellationTokenSource));
        }
        
        public void Process()
        {
            SubscribeAndProcess<DefaultMessage>(
                _configuration.QueueName,
                _configuration.QueueMaxSubscriber,
                _configuration.QueueConnectionString,
                _cancellationTokenSource,
                OnReceived);
        }

        private void OnReceived(IMessage<DefaultMessage> message, MessageReceivedInfo info)
        {
            try
            {
                _dataProcessor.Process(message.Body);
            }
            catch (Exception)
            {
                KickToWaitForRetryState(message);
            }
        }

        private void KickToWaitForRetryState(IMessage<DefaultMessage> message) => KickToWaitForRetryState(_messaging,
            message, AnalyticsEnum.PublishToRetryQueue, _configuration.QueueName);
    }
}