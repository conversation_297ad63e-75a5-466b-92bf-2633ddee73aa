using System;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Analytics.DataAccess.Interface;
using Agoda.EBE.Agents.Analytics.Object;
using Agoda.EBE.Agents.Analytics.Processor.Interface;
using Agoda.EBE.Agents.Common.EbeCommonException;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.MQ.MessageType;

namespace Agoda.EBE.Agents.Analytics.Processor
{
    public class AnalyticsDataProcessor: IAnalyticsDataProcessor
    {
        private readonly IMessaging _messaging;
        private readonly AnalyticsConfiguration _configuration;
        private readonly IDataAccess _dataAccess;
        
        public AnalyticsDataProcessor(IMessaging messaging, AnalyticsConfiguration configuration, IDataAccess dataAccess)
        {
            _messaging = messaging ?? throw new ArgumentException(nameof(_messaging));
            _configuration = configuration ?? throw new ArgumentException(nameof(_configuration));
            _dataAccess = dataAccess ?? throw new ArgumentException(nameof(_dataAccess));
        }
        public void Process(IDefaultMessage message)
        {
            var processStat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            try
            {
                processStat.BeginTrack();
                processStat.Tags["is_ignore"] = false.ToString();
                var bookingDetail = _dataAccess.GetBookingDetail(message.BookingId, message.WorkflowStateId);
                if (!bookingDetail.Is_Test_Booking)
                {
                    var isSuccess = bookingDetail.Send();
                    if (!isSuccess) throw new Exception($"Send generic message fail, bookingId: ${message.BookingId}");
                }

                processStat.Tags["origin"] = bookingDetail.Client_Info?.Origin ?? "no_origin_data";
                processStat.EndTrack(AnalyticsEnum.DataProcessorProcess);
            }
            catch (ExecuteStoreProcReturnNoRow ex)
            {
                _messaging.ExceptionMessage.Send($"GetBookingDetail return no row: bookingId: ${message.BookingId}", ex, LogLevel.WARN);
                processStat.Tags["is_ignore"] = true.ToString();
                processStat.EndTrack(AnalyticsEnum.DataProcessorProcess);
            }
            catch(Exception ex)
            {
                _messaging.ExceptionMessage.Send($"DataProcessor Process Failed: bookingId: ${message.BookingId}", ex,
                    LogLevel.FATAL,
                    new TagBuilder().AddBookingTag(message.BookingId).Build());
                processStat.EndTrack(AnalyticsEnum.DataProcessorProcess, false);
                throw;
            }
        }
    }
}