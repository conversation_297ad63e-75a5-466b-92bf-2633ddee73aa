using System;
using System.Threading;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Analytics.Object;
using Agoda.EBE.Agents.Analytics.Processor.Interface;
using Agoda.EBE.Agents.Common.Base;
using Agoda.EBE.Agents.Common.Messaging;
using Autofac;

namespace Agoda.EBE.Agents.Analytics
{
    public class AnalyticsRunner : RunnerBase
    {
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly ContainerBuilder builder;
        private readonly AnalyticsConfiguration _configuration;

        public AnalyticsRunner(string agentName, AnalyticsConfiguration configuration, CancellationTokenSource cancelToken, ContainerBuilder builder)
            : base(agentName, configuration, cancelToken)
        {
            _configuration = configuration;
            _cancellationTokenSource = cancelToken;
            this.builder = builder;
        }

        protected override void GetConfig() { }
        public override void Stop() { }

        public override void Start()
        {
            Console.WriteLine($"Starting {AgentName} agent");
            var container = AnalyticsContainer.RegisterDependencies(_configuration, _cancellationTokenSource, builder);
            using (var root = container.BeginLifetimeScope())
            {
                var messaging = root.Resolve<IMessaging>();
                var configuration = root.Resolve<AnalyticsConfiguration>();

                // Queue processor cannot be disposed after going out of this scope.
                var processor = container.Resolve<IAnalyticsQueueProcessor>();
                
                if (configuration.IsActive) RunAnalyticsProcess(messaging, processor);
                else messaging.ExceptionMessage.Send($"{AgentName} does not run, configuration is set to false", null, LogLevel.INFO);
            }
        }

        private void RunAnalyticsProcess(IMessaging messaging, IAnalyticsQueueProcessor processor)
        {
            Console.WriteLine($"{AgentName} is running");
            messaging.ExceptionMessage.Send($"{AgentName} Agent Started!", null, LogLevel.INFO);
            if (!CancelToken.Token.IsCancellationRequested)
            {
                try
                {
                    processor.Process();
                }
                catch (Exception ex)
                {
                    messaging.ExceptionMessage.Send($"Unable to process {AgentName}", ex, LogLevel.FATAL);
                    throw;
                }
            }
        }
    }
}