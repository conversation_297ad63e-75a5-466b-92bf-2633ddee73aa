using System.Threading;
using Agoda.EBE.Agents.Analytics.DataAccess.Interface;
using Agoda.EBE.Agents.Analytics.Object;
using Agoda.EBE.Agents.Analytics.Processor;
using Agoda.EBE.Agents.Analytics.Processor.Interface;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Util.Data;
using Agoda.EBE.Agents.Common.Util.Data.Interface;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.MQ;
using Agoda.EBE.Framework.MQ.Interface;
using Autofac;

namespace Agoda.EBE.Agents.Analytics
{
    public static class AnalyticsContainer
    {
        public static IContainer RegisterDependencies(AnalyticsConfiguration configuration,
            CancellationTokenSource cancellationTokenSource, ContainerBuilder builder)
        {
            builder.RegisterInstance(configuration).As<AnalyticsConfiguration>();
            builder.Register(c => new RabbitMQAdapter(c.Resolve<AnalyticsConfiguration>().QueueConnectionString)).As<IRabbitMQAdapter>();
            builder.RegisterInstance(cancellationTokenSource).As<CancellationTokenSource>();
            builder.Register(c => new DatabaseProvider(c.Resolve<AnalyticsConfiguration>().AppEbeConn)).As<IDatabaseProvider>();
            builder.RegisterType<DataAccess.DataAccess>().As<IDataAccess>();
            builder.RegisterType<AnalyticsDataProcessor>().As<IAnalyticsDataProcessor>();
            builder.RegisterType<AnalyticsQueueProcessor>().As<IAnalyticsQueueProcessor>();
            builder.Register(c => new MeasurementMessageFactory(c.Resolve<AnalyticsConfiguration>().MetricName,
                c.Resolve<AnalyticsConfiguration>().ComponentName)).As<IMeasurementMessageFactory>();
            builder.Register(c => new AnalyticsAgentExceptionMessage(c.Resolve<AnalyticsConfiguration>().StoreFrontID))
                .As<IEBEExceptionMessage>();
            builder.RegisterType<Messaging>().As<IMessaging>();

            return builder.Build();
        }
    }
}