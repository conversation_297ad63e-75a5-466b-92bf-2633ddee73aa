using System;
using System.Configuration;
using System.Data;
using Agoda.EBE.Agents.Analytics.DataAccess;
using Agoda.EBE.Agents.Analytics.Object;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Agents.Common.Util.Data.Interface;
using Agoda.EBE.Framework.TestUtils;
using Agoda.EBE.Framework.TestUtils.Objects;
using Moq;
using NUnit.Framework;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Text;
using Agoda.EBE.Framework;
using System.Reflection;
using Agoda.EBE.Framework.Data;
using Agoda.EBE.Framework.Messaging;

[TestFixture]
public class BookingInfoDalSpec
{
    [Test]
    public void GetHotelInfo_ShouldValidateGmtOffsetAndGmtOffsetMinutes()
    {
        // Arrange
        var dataTable = new DataTable();
        dataTable.Columns.Add("hotel_id", typeof(int));
        dataTable.Columns.Add("hotel_name", typeof(string));
        dataTable.Columns.Add("no_of_rooms", typeof(int));
        dataTable.Columns.Add("no_of_children", typeof(int));
        dataTable.Columns.Add("no_of_adults", typeof(int));
        dataTable.Columns.Add("no_of_extrabeds", typeof(int));
        dataTable.Columns.Add("room_type_id", typeof(int));
        dataTable.Columns.Add("is_agency_prepay", typeof(string));
        dataTable.Columns.Add("occupancy", typeof(string));
        dataTable.Columns.Add("is_agoda_reception", typeof(bool));
        dataTable.Columns.Add("is_non_hotel_accommodation_mode", typeof(bool));
        dataTable.Columns.Add("is_nha_for_display", typeof(bool));
        dataTable.Columns.Add("is_nha_enabled", typeof(int));
        dataTable.Columns.Add("city_id", typeof(int));
        dataTable.Columns.Add("country_id", typeof(int));
        dataTable.Columns.Add("country_name", typeof(string));
        dataTable.Columns.Add("country_iso2", typeof(string));
        dataTable.Columns.Add("city_name", typeof(string));
        dataTable.Columns.Add("continent_name", typeof(string));
        dataTable.Columns.Add("chain_id", typeof(int));
        dataTable.Columns.Add("gmt_offset", typeof(int));
        dataTable.Columns.Add("gmt_offset_minutes", typeof(int));

        var dataRow = dataTable.NewRow();
        dataRow["hotel_id"] = 123;
        dataRow["hotel_name"] = "Sample Hotel";
        dataRow["no_of_rooms"] = 10;
        dataRow["no_of_children"] = 2;
        dataRow["no_of_adults"] = 4;
        dataRow["no_of_extrabeds"] = 1;
        dataRow["room_type_id"] = 101;
        dataRow["is_agency_prepay"] = "Yes";
        dataRow["occupancy"] = "4 Adults, 2 Children";
        dataRow["is_agoda_reception"] = true;
        dataRow["is_non_hotel_accommodation_mode"] = false;
        dataRow["is_nha_for_display"] = true;
        dataRow["is_nha_enabled"] = 1;
        dataRow["city_id"] = 1001;
        dataRow["country_id"] = 2001;
        dataRow["country_name"] = "Sample Country";
        dataRow["country_iso2"] = "SC";
        dataRow["city_name"] = "Sample City";
        dataRow["continent_name"] = "Sample Continent";
        dataRow["chain_id"] = 3001;
        dataRow["gmt_offset"] = 6;
        dataRow["gmt_offset_minutes"] = 30;
        dataTable.Rows.Add(dataRow);

        // Act
        var mockConfiguration = new Mock<AnalyticsConfiguration>();
        var mockMessaging = new Mock<IMessaging>();
        var mockDatabaseProvider = new Mock<IDatabaseProvider>();
        var dataAccess = new DataAccess(mockMessaging.Object, mockConfiguration.Object, mockDatabaseProvider.Object);

        var hotelInfo = dataAccess.GetHotelInfoPublic(dataRow);

        // Assert
        Assert.AreEqual(6, hotelInfo.Gmt_Offset, "Gmt_Offset should be 6.");
        Assert.AreEqual(30, hotelInfo.Gmt_Offset_Minutes, "Gmt_Offset_Minutes should be 30.");
    }

    [Test]
    public void GetHotelInfo_ShouldHandleNoOffsetReturnedByDB()
    {
        // Arrange
        var dataTable = new DataTable();
        dataTable.Columns.Add("hotel_id", typeof(int));
        dataTable.Columns.Add("hotel_name", typeof(string));
        dataTable.Columns.Add("no_of_rooms", typeof(int));
        dataTable.Columns.Add("no_of_children", typeof(int));
        dataTable.Columns.Add("no_of_adults", typeof(int));
        dataTable.Columns.Add("no_of_extrabeds", typeof(int));
        dataTable.Columns.Add("room_type_id", typeof(int));
        dataTable.Columns.Add("is_agency_prepay", typeof(string));
        dataTable.Columns.Add("occupancy", typeof(string));
        dataTable.Columns.Add("is_agoda_reception", typeof(bool));
        dataTable.Columns.Add("is_non_hotel_accommodation_mode", typeof(bool));
        dataTable.Columns.Add("is_nha_for_display", typeof(bool));
        dataTable.Columns.Add("is_nha_enabled", typeof(int));
        dataTable.Columns.Add("city_id", typeof(int));
        dataTable.Columns.Add("country_id", typeof(int));
        dataTable.Columns.Add("country_name", typeof(string));
        dataTable.Columns.Add("country_iso2", typeof(string));
        dataTable.Columns.Add("city_name", typeof(string));
        dataTable.Columns.Add("continent_name", typeof(string));
        dataTable.Columns.Add("chain_id", typeof(int));
        dataTable.Columns.Add("gmt_offset", typeof(int));
        dataTable.Columns.Add("gmt_offset_minutes", typeof(int));

        var dataRow = dataTable.NewRow();
        dataRow["hotel_id"] = 123;
        dataRow["hotel_name"] = "Sample Hotel";
        dataRow["no_of_rooms"] = 10;
        dataRow["no_of_children"] = 2;
        dataRow["no_of_adults"] = 4;
        dataRow["no_of_extrabeds"] = 1;
        dataRow["room_type_id"] = 101;
        dataRow["is_agency_prepay"] = "Yes";
        dataRow["occupancy"] = "4 Adults, 2 Children";
        dataRow["is_agoda_reception"] = true;
        dataRow["is_non_hotel_accommodation_mode"] = false;
        dataRow["is_nha_for_display"] = true;
        dataRow["is_nha_enabled"] = 1;
        dataRow["city_id"] = 1001;
        dataRow["country_id"] = 2001;
        dataRow["country_name"] = "Sample Country";
        dataRow["country_iso2"] = "SC";
        dataRow["city_name"] = "Sample City";
        dataRow["continent_name"] = "Sample Continent";
        dataRow["chain_id"] = 3001;
        dataRow["gmt_offset"] = DBNull.Value;
        dataRow["gmt_offset_minutes"] = DBNull.Value;
        dataTable.Rows.Add(dataRow);

        var mockConfiguration = new Mock<AnalyticsConfiguration>();
        var mockMessaging = new Mock<IMessaging>();
        var mockDatabaseProvider = new Mock<IDatabaseProvider>();
        var dataAccess = new DataAccess(mockMessaging.Object, mockConfiguration.Object, mockDatabaseProvider.Object);

        // Act
        var hotelInfo = dataAccess.GetHotelInfoPublic(dataRow);

        // Assert
        Assert.AreEqual(0, hotelInfo.Gmt_Offset, "Gmt_Offset should default to 0 when not returned by DB.");
        Assert.AreEqual(0, hotelInfo.Gmt_Offset_Minutes, "Gmt_Offset_Minutes should default to 0 when not returned by DB.");
    }

    [Test]
    public void DeviceInfo_SearchCluster_ShouldSerializeAndDeserialize()
    {
        var deviceInfo = new DeviceInfo
        {
            osName = "Android",
            searchCluster = "hk-int-2x"
        };
        
        var json = JsonConvert.SerializeObject(deviceInfo);
        var deserializedDeviceInfo = JsonConvert.DeserializeObject<DeviceInfo>(json);
        
        Assert.AreEqual("hk-int-2x", deserializedDeviceInfo.searchCluster);
    }

    [Test]
    public void DeviceInfo_EmptySearchCluster_ShouldSerializeAndDeserialize()
    {
        var deviceInfo = new DeviceInfo
        {
            osName = "Android",
        };
        
        var json = JsonConvert.SerializeObject(deviceInfo);
        var deserializedDeviceInfo = JsonConvert.DeserializeObject<DeviceInfo>(json);
        
        Assert.IsEmpty(deserializedDeviceInfo.searchCluster);
        Assert.AreEqual("Android",deserializedDeviceInfo.osName);
    }
    
    [Test]
    public void ClientInfo_EmptySearchCluster_ShouldSerializeAndDeserialize()
    {
        var clientInfo = new ClientInfo
        {
            SearchCluster = ""
        };
        Assert.IsEmpty(clientInfo.SearchCluster);
    }

    [Test]
    public void ClientInfo_SearchCluster_ShouldStoreValue()
    {
        var clientInfo = new ClientInfo
        {
            SearchCluster = "am-int-4x"
        };
        
        Assert.AreEqual("am-int-4x", clientInfo.SearchCluster);
    }

    [Test]
    public void DataRow_SearchCluster_ShouldParseCorrectly()
    {
        var rowDataWithSearchCluster = new List<DataPropertyObject>
        {
            new DataPropertyObject("searchcluster", "am-pc-4x", typeof(string))
        };
        var dataRow = DataRowUtil.FromDataRowObject(rowDataWithSearchCluster);
        var searchClusterValue = dataRow["searchcluster"]?.ToString();
        
        Assert.AreEqual("am-pc-4x", searchClusterValue);
    }

    [Test]
    public void GetClientInfo_CompressedDeviceInfo_WithSearchCluster_ShouldMapCorrectly()
    {
        var deviceInfoJson = @"{""osName"":""Android"",""searchCluster"":""hk-poc-2a""}";
        var compressedData = Compression.CompressBytes(Encoding.UTF8.GetBytes(deviceInfoJson));
        
        var dataRow = CreateDataRowForGetClientInfo(compressedData);
        var clientInfo = InvokeGetClientInfo(dataRow, 12345);
        
        Assert.AreEqual("hk-poc-2a", clientInfo.SearchCluster);
    }

    [Test]
    public void GetClientInfo_FallbackToDataRow_WithSearchCluster_ShouldMapCorrectly()
    {
        var invalidCompressedData = new byte[] { 0x1, 0x2, 0x3 };
        var dataRow = CreateDataRowForGetClientInfo(invalidCompressedData);
        var clientInfo = InvokeGetClientInfo(dataRow, 9999);

        // Assert
        Assert.IsEmpty(clientInfo.SearchCluster);
    }

    [Test]
    public void GetBookingDetail_ShouldReturnBookingDetail()
    {
        // Arrange
        var bookingId = 123;
        var eventWorkflowStateId = 456;
        
        DataTable dt = new DataTable();
        dt.Columns.Add("booking_id", typeof(int));
        dt.Columns.Add("benefit_id_list", typeof(string));
        dt.Columns.Add("additional_info", typeof(byte[]));
        dt.Columns.Add("total_amount", typeof(decimal));
        dt.Columns.Add("charge_option", typeof(string));
        dt.Columns.Add("payment_currency", typeof(string));
        dt.Columns.Add("creditcard_type", typeof(string));
        dt.Columns.Add("cc_id", typeof(long));
        dt.Columns.Add("hotel_id", typeof(int));
        dt.Columns.Add("hotel_name", typeof(string));
        dt.Columns.Add("no_of_rooms", typeof(int));
        dt.Columns.Add("no_of_children", typeof(int));
        dt.Columns.Add("no_of_adults", typeof(int));
        dt.Columns.Add("no_of_extrabeds", typeof(int));
        dt.Columns.Add("room_type_id", typeof(int));
        dt.Columns.Add("is_agency_prepay", typeof(string));
        dt.Columns.Add("occupancy", typeof(string));
        dt.Columns.Add("is_agoda_reception", typeof(bool));
        dt.Columns.Add("is_non_hotel_accommodation_mode", typeof(bool));
        dt.Columns.Add("is_nha_for_display", typeof(bool));
        dt.Columns.Add("is_nha_enabled", typeof(int));
        dt.Columns.Add("city_id", typeof(int));
        dt.Columns.Add("country_id", typeof(int));
        dt.Columns.Add("country_name", typeof(string));
        dt.Columns.Add("country_iso2", typeof(string));
        dt.Columns.Add("city_name", typeof(string));
        dt.Columns.Add("continent_name", typeof(string));
        dt.Columns.Add("chain_id", typeof(int));
        dt.Columns.Add("gmt_offset", typeof(int));
        dt.Columns.Add("gmt_offset_minutes", typeof(int));
        dt.Columns.Add("promotion_code", typeof(string));
        dt.Columns.Add("memberid", typeof(int));
        dt.Columns.Add("cxl_date", typeof(DateTime));
        dt.Columns.Add("cancellation_policy_code", typeof(string));
        dt.Columns.Add("device_info", typeof(byte[]));
        dt.Columns.Add("os_name", typeof(string));
        dt.Columns.Add("os_version", typeof(string));
        dt.Columns.Add("browser_name", typeof(string));
        dt.Columns.Add("device_brand", typeof(string));
        dt.Columns.Add("device_model", typeof(string));
        dt.Columns.Add("origin", typeof(string));
        dt.Columns.Add("browser_version", typeof(string));
        dt.Columns.Add("browser_subversion", typeof(string));
        dt.Columns.Add("browser_build_number", typeof(string));
        dt.Columns.Add("is_mobile", typeof(bool));
        dt.Columns.Add("is_touch", typeof(bool));
        dt.Columns.Add("searchcluster", typeof(string));
        dt.Columns.Add("client_ip_address", typeof(string));
        dt.Columns.Add("session_id", typeof(string));
        dt.Columns.Add("tracking_cookie_id", typeof(string));
        dt.Columns.Add("is_user_logged_in", typeof(bool));
        dt.Columns.Add("server_name", typeof(string));
        dt.Columns.Add("datacenter", typeof(string));
        dt.Columns.Add("original_selling_amount", typeof(decimal));
        dt.Columns.Add("original_supplier_amount", typeof(decimal));
        dt.Columns.Add("current_selling_amount", typeof(decimal));
        dt.Columns.Add("current_supplier_amount", typeof(decimal));
        dt.Columns.Add("traffic_group_site_id", typeof(int));
        dt.Columns.Add("traffic_group_id", typeof(int));
        dt.Columns.Add("traffic_group_name", typeof(string));
        dt.Columns.Add("traffic_id", typeof(int));
        dt.Columns.Add("traffic_name", typeof(string));
        dt.Columns.Add("affiliate_id", typeof(int));
        dt.Columns.Add("affiliate_name", typeof(string));
        dt.Columns.Add("sell_tag_id", typeof(string));
        dt.Columns.Add("pricing_template_id", typeof(string));
        dt.Columns.Add("search_id", typeof(string));
        dt.Columns.Add("downlift_amount_usd", typeof(decimal));
        dt.Columns.Add("offer_id", typeof(string));
        dt.Columns.Add("language_name", typeof(string));
        dt.Columns.Add("language_abbr", typeof(string));
        dt.Columns.Add("booking_history_date", typeof(DateTime));
        dt.Columns.Add("booking_date", typeof(DateTime));
        dt.Columns.Add("prebooking_id", typeof(long));
        dt.Columns.Add("itinerary_id", typeof(int));
        dt.Columns.Add("booking_history_id", typeof(long));
        dt.Columns.Add("language_id", typeof(int));
        dt.Columns.Add("cid_list", typeof(int));
        dt.Columns.Add("booking_date_from", typeof(DateTime));
        dt.Columns.Add("booking_date_until", typeof(DateTime));
        dt.Columns.Add("description", typeof(string));
        dt.Columns.Add("dmc_code", typeof(string));
        dt.Columns.Add("storefront_id", typeof(int));
        dt.Columns.Add("platform_id", typeof(string));
        dt.Columns.Add("fraud_check_ip", typeof(string));
        dt.Columns.Add("booking_external_reference", typeof(string));
        dt.Columns.Add("dmc_id", typeof(int));
        dt.Columns.Add("fe_cid", typeof(string));
        dt.Columns.Add("is_not_cc_required", typeof(bool));
        dt.Columns.Add("rate_channel", typeof(string));
        dt.Columns.Add("Repeated_Booker", typeof(bool));
        dt.Columns.Add("is_confirmed_booking", typeof(bool));
        dt.Columns.Add("payment_model", typeof(int));
        dt.Columns.Add("cid_display_name", typeof(string));
        dt.Columns.Add("whitelabel_id", typeof(int));
        dt.Columns.Add("multi_product_id", typeof(int));
        dt.Columns.Add("payment_gateway_id", typeof(int));
        dt.Columns.Add("stay_package_type", typeof(int));
        dt.Columns.Add("stay_type", typeof(int));
        dt.Columns.Add("availability_type", typeof(int));
        dt.Columns.Add("promotion_campaign_id", typeof(int));
        dt.Columns.Add("is_promotion", typeof(bool));
        dt.Columns.Add("is_credit_card_promotion", typeof(bool));
        dt.Columns.Add("is_test_booking", typeof(bool));
        dt.Columns.Add("platform_group_name", typeof(string));
        dt.Columns.Add("site_origin", typeof(string));
        dt.Columns.Add("is_aab", typeof(bool));
        dt.Columns.Add("is_smart_flex", typeof(bool));
        dt.Columns.Add("original_cancellation_policy_code", typeof(string));
        dt.Columns.Add("is_pull", typeof(bool));
        dt.Columns.Add("is_third_party_supply", typeof(bool));
        
        var dr = dt.NewRow();
        dr["booking_id"] = 123;
        dr["benefit_id_list"] = "1,2,3,";
        dr["additional_info"] = DBNull.Value;
        dr["total_amount"] = 100.50m;
        dr["charge_option"] = "prepay";
        dr["payment_currency"] = "USD";
        dr["creditcard_type"] = "VISA";
        dr["cc_id"] = 789L;
        dr["hotel_id"] = 456;
        dr["hotel_name"] = "Test Hotel";
        dr["no_of_rooms"] = 2;
        dr["no_of_children"] = 1;
        dr["no_of_adults"] = 3;
        dr["no_of_extrabeds"] = 0;
        dr["room_type_id"] = 101;
        dr["is_agency_prepay"] = "Yes";
        dr["occupancy"] = "0";
        dr["is_agoda_reception"] = true;
        dr["is_non_hotel_accommodation_mode"] = false;
        dr["is_nha_for_display"] = false;
        dr["is_nha_enabled"] = 0;
        dr["city_id"] = 1001;
        dr["country_id"] = 2001;
        dr["country_name"] = "Test Country";
        dr["country_iso2"] = "TC";
        dr["city_name"] = "Test City";
        dr["continent_name"] = "Test Continent";
        dr["chain_id"] = 3001;
        dr["gmt_offset"] = 7;
        dr["gmt_offset_minutes"] = 0;
        dr["promotion_code"] = "PROMO123";
        dr["memberid"] = 999;
        dr["cxl_date"] = DBNull.Value;
        dr["cancellation_policy_code"] = "FLEX";
        dr["device_info"] = DBNull.Value;
        dr["os_name"] = "Windows";
        dr["os_version"] = "10";
        dr["browser_name"] = "Chrome";
        dr["device_brand"] = "Generic";
        dr["device_model"] = "Desktop";
        dr["origin"] = "web";
        dr["browser_version"] = "91.0";
        dr["browser_subversion"] = "4472.124";
        dr["browser_build_number"] = "1";
        dr["is_mobile"] = false;
        dr["is_touch"] = false;
        dr["client_ip_address"] = "***********";
        dr["session_id"] = "session123";
        dr["tracking_cookie_id"] = "cookie123";
        dr["is_user_logged_in"] = true;
        dr["server_name"] = "test-server";
        dr["datacenter"] = "test-dc";
        dr["original_selling_amount"] = 120.00m;
        dr["original_supplier_amount"] = 80.00m;
        dr["current_selling_amount"] = 100.50m;
        dr["current_supplier_amount"] = 70.00m;
        dr["traffic_group_site_id"] = 1;
        dr["traffic_group_id"] = 2;
        dr["traffic_group_name"] = "Test Traffic Group";
        dr["traffic_id"] = 3;
        dr["traffic_name"] = "Test Traffic";
        dr["affiliate_id"] = 4;
        dr["affiliate_name"] = "Test Affiliate";
        dr["sell_tag_id"] = "sell123";
        dr["pricing_template_id"] = "price123";
        dr["search_id"] = "search123";
        dr["downlift_amount_usd"] = -10.00m;
        dr["offer_id"] = "offer123";
        dr["language_name"] = "English";
        dr["language_abbr"] = "EN";
        dr["booking_history_date"] = DateTime.Now;
        dr["booking_date"] = DateTime.Now.AddDays(-1);
        dr["prebooking_id"] = 12345L;
        dr["itinerary_id"] = 678;
        dr["booking_history_id"] = 98765L;
        dr["language_id"] = 1;
        dr["cid_list"] = 5;
        dr["booking_date_from"] = DateTime.Now.AddDays(1);
        dr["booking_date_until"] = DateTime.Now.AddDays(3);
        dr["description"] = "Confirmed";
        dr["dmc_code"] = "DMC123";
        dr["storefront_id"] = 10;
        dr["platform_id"] = "web";
        dr["fraud_check_ip"] = "***********";
        dr["booking_external_reference"] = "EXT123";
        dr["dmc_id"] = 20;
        dr["fe_cid"] = "FE123";
        dr["is_not_cc_required"] = false;
        dr["rate_channel"] = "B2C";
        dr["Repeated_Booker"] = true;
        dr["is_confirmed_booking"] = true;
        dr["payment_model"] = 1;
        dr["cid_display_name"] = "Test Display";
        dr["whitelabel_id"] = 30;
        dr["multi_product_id"] = 40;
        dr["payment_gateway_id"] = 50;
        dr["stay_package_type"] = 1;
        dr["stay_type"] = 1;
        dr["availability_type"] = 1;
        dr["promotion_campaign_id"] = 100;
        dr["is_promotion"] = true;
        dr["is_credit_card_promotion"] = false;
        dr["is_test_booking"] = false;
        dr["platform_group_name"] = "Test Platform";
        dr["site_origin"] = "test-origin";
        dr["is_aab"] = false;
        dr["is_smart_flex"] = true;
        dr["original_cancellation_policy_code"] = "FLEX";
        dr["is_pull"] = false;
        dr["is_third_party_supply"] = false;
        dt.Rows.Add(dr);

        DataSet ds = new DataSet();
        ds.Tables.Add(dt);

        var mockConfiguration = new Mock<AnalyticsConfiguration>();
        var mockMessaging = new Mock<IMessaging>();
        var mockDatabaseProvider = new Mock<IDatabaseProvider>();
        
        // Set up the MeasurementMessageFactory mock
        var mockMeasurementMessageFactory = new Mock<IMeasurementMessageFactory>();
        var mockMeasurementMessage = new Mock<IEBEMeasurementMessage>();
        mockMeasurementMessageFactory.Setup(x => x.CreateNewMeasurement())
            .Returns(mockMeasurementMessage.Object);
        mockMessaging.Setup(x => x.MeasurementMessageFactory)
            .Returns(mockMeasurementMessageFactory.Object);
        
        // Set up the ExceptionMessage mock
        var mockExceptionMessage = new Mock<IEBEExceptionMessage>();
        mockMessaging.Setup(x => x.ExceptionMessage)
            .Returns(mockExceptionMessage.Object);
        
        mockDatabaseProvider.Setup(x => x.ExecuteDataSet(It.IsAny<string>(), It.IsAny<List<Agoda.EBE.Agents.Common.Object.ParameterInfo>>()))
            .Returns(ds);
        
        var dataAccess = new DataAccess(mockMessaging.Object, mockConfiguration.Object, mockDatabaseProvider.Object);

        // Act
        var result = dataAccess.GetBookingDetail(bookingId, eventWorkflowStateId);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(result.Booking_Id, 123);
        Assert.AreEqual(result.Event_Workflow_State_Id, 456);
        Assert.AreEqual(result.Benefit_Id, "1,2,3");
        Assert.AreEqual(result.Additional_Info, string.Empty);
        Assert.AreEqual(result.Payment_Info.Payment_Amount_USD, 100.50m);
        Assert.AreEqual(result.Payment_Info.Charge_Option, "prepay");
        Assert.AreEqual(result.Payment_Info.Currency, "USD");
        Assert.AreEqual(result.CreditCard_Info.CreditCard_Type, "VISA");
        Assert.AreEqual(result.CreditCard_Info.CC_ID, 789L);
        Assert.AreEqual(result.Hotel_Info.Hotel_Id, 456);
        Assert.AreEqual(result.Hotel_Info.Hotel_Name, "Test Hotel");
        Assert.AreEqual(result.Hotel_Info.No_Of_Rooms, 2);
        Assert.AreEqual(result.Hotel_Info.No_Of_Children, 1);
        Assert.AreEqual(result.Hotel_Info.No_Of_Adults, 3);
        Assert.AreEqual(result.Hotel_Info.No_Of_Extrabeds, 0);
        Assert.AreEqual(result.Hotel_Info.Room_type_ID, 101);
        Assert.AreEqual(result.Hotel_Info.Prepayment_Flag, "Yes");
        Assert.AreEqual(result.Hotel_Info.Occupancy, "0");
        Assert.AreEqual(result.Hotel_Info.Is_Agoda_Reception, true);
        Assert.AreEqual(result.Hotel_Info.Is_Non_Hotel_Accommodation_Mode, false);
        Assert.AreEqual(result.Hotel_Info.Is_NHA_For_Display, false);
        Assert.AreEqual(result.Hotel_Info.Is_NHA_Enabled, 0);
        Assert.AreEqual(result.Hotel_Info.City_Id, 1001);
        Assert.AreEqual(result.Hotel_Info.Country_Id, 2001);
        Assert.AreEqual(result.Hotel_Info.Country_Name, "Test Country");
        Assert.AreEqual(result.Hotel_Info.Country_Iso2, "TC");
        Assert.AreEqual(result.Hotel_Info.City_Name, "Test City");
        Assert.AreEqual(result.Hotel_Info.Hotel_Continent, "Test Continent");
        Assert.AreEqual(result.Hotel_Info.Chain_Id, 3001);
        Assert.AreEqual(result.Hotel_Info.Gmt_Offset, 7);
        Assert.AreEqual(result.Hotel_Info.Gmt_Offset_Minutes, 0);
        Assert.AreEqual(result.Discount_Info.Promotion_Code, "PROMO123");
        Assert.AreEqual(result.Customer_Info.Member_Id, 999);
        Assert.AreEqual(result.Cancellation_Info.Cancellation_Date, DateTime.MinValue);
        Assert.AreEqual(result.Cancellation_Info.Cancellation_Policy_Code, "FLEX");
        Assert.AreEqual(result.Client_Info.OS_Name, "Windows");
        Assert.AreEqual(result.Client_Info.OS_Version, "10");
        Assert.AreEqual(result.Client_Info.Browser_Name, "Chrome");
        Assert.AreEqual(result.Client_Info.Device_Brand, "Generic");
        Assert.AreEqual(result.Client_Info.Device_Model, "Desktop");
        Assert.AreEqual(result.Client_Info.Origin, "web");
        Assert.AreEqual(result.Client_Info.Browser_Version, "91.0");
        Assert.AreEqual(result.Client_Info.Browser_Subversion, "4472.124");
        Assert.AreEqual(result.Client_Info.Browser_Build_Number, "1");
        Assert.AreEqual(result.Client_Info.Is_Mobile, false);
        Assert.AreEqual(result.Client_Info.Is_Touch, false);
        Assert.AreEqual(result.Client_Info.Client_IP_Address, "***********");
        Assert.AreEqual(result.Client_Info.Session_Id, "session123");
        Assert.AreEqual(result.Client_Info.Tracking_Cooking_Id, "cookie123");
        Assert.AreEqual(result.Client_Info.Is_User_Logged_In, true);
        Assert.AreEqual(result.System_Info.Server_Name, "test-server");
        Assert.AreEqual(result.System_Info.Data_Center, "test-dc");
        Assert.AreEqual(result.Charge_Info.Original_Selling_Amount_USD, 120.00m);
        Assert.AreEqual(result.Charge_Info.Original_Supplier_Amount_USD, 80.00m);
        Assert.AreEqual(result.Charge_Info.Original_Margin_Amount_USD, 40.00m);
        Assert.AreEqual(result.Charge_Info.Current_Selling_Amount_USD, 100.50m);
        Assert.AreEqual(result.Charge_Info.Current_Supplier_Amount_USD, 70.00m);
        Assert.AreEqual(result.Cid_Info.Site_Id, 1);
        Assert.AreEqual(result.Cid_Info.Traffic_Group_Id, 2);
        Assert.AreEqual(result.Cid_Info.Traffic_Group_Name, "Test Traffic Group");
        Assert.AreEqual(result.Cid_Info.Traffic_Id, 3);
        Assert.AreEqual(result.Cid_Info.Traffic_Name, "Test Traffic");
        Assert.AreEqual(result.Cid_Info.Affiliate_Id, 4);
        Assert.AreEqual(result.Cid_Info.Affiliate_Name, "Test Affiliate");
        Assert.AreEqual(result.Sell_Info.Sell_Tag_Id, "sell123");
        Assert.AreEqual(result.Sell_Info.Pricing_Template_Id, "price123");
        Assert.AreEqual(result.Sell_Info.Search_Id, "search123");
        Assert.AreEqual(result.Sell_Info.Pricelift, "10.00");
        Assert.AreEqual(result.Sell_Info.Offer_Id, "offer123");
        Assert.AreEqual(result.Room_Info.Occupancy, 0);
        Assert.AreEqual(result.Language_Details.Language_Id, 1);
        Assert.AreEqual(result.Language_Details.Language_Name, "English");
        Assert.AreEqual(result.Language_Details.Language_Abbr, "EN");
        Assert.AreEqual(result.Prebooking_Id, 12345L);
        Assert.AreEqual(result.Itinerary_Id, 678);
        Assert.AreEqual(result.Booking_History_Id, 98765L);
        Assert.AreEqual(result.Language_Id, 1);
        Assert.AreEqual(result.Site_Id, 5);
        Assert.AreEqual(result.DMC_Code, "DMC123");
        Assert.AreEqual(result.Storefront_Id, 10);
        Assert.AreEqual(result.Platform_Id, "web");
        Assert.AreEqual(result.Fraud_Check_Ip, "***********");
        Assert.AreEqual(result.Booking_External_Reference, "EXT123");
        Assert.AreEqual(result.DMC_Id, 20);
        Assert.AreEqual(result.Fe_Cid, "FE123");
        Assert.AreEqual(result.Is_Not_Cc_Required, false);
        Assert.AreEqual(result.Rate_Channel, "B2C");
        Assert.AreEqual(result.Repeated_Booker, true);
        Assert.AreEqual(result.Is_Confirmed_Booking, true);
        Assert.AreEqual(result.Payment_Model, 1);
        Assert.AreEqual(result.Cid_Display_Name, "Test Display");
        Assert.AreEqual(result.Whitelabel_Id, 30);
        Assert.AreEqual(result.Multi_Product_Id, 40);
        Assert.AreEqual(result.Payment_Gateway_Id, 50);
        Assert.AreEqual(result.Stay_Package_Type, 1);
        Assert.AreEqual(result.Stay_Type, 1);
        Assert.AreEqual(result.Availability_Type, 1);
        Assert.AreEqual(result.Promotion_Code, "PROMO123");
        Assert.AreEqual(result.Promotion_Campaign_Id, 100);
        Assert.AreEqual(result.Is_Promotion, true);
        Assert.AreEqual(result.Is_Credit_Card_Promotion, false);
        Assert.AreEqual(result.Is_Test_Booking, false);
        Assert.AreEqual(result.Platform_Group_Name, "Test Platform");
        Assert.AreEqual(result.Site_Origin, "test-origin");
        Assert.AreEqual(result.Is_AAB, false);
        Assert.AreEqual(result.Is_Smart_Flex, true);
        Assert.AreEqual(result.Original_Cancellation_Policy_Code, "FLEX");
        Assert.AreEqual(result.OS_Version, "10");
        Assert.AreEqual(result.Is_Pull_Third_Party_Supply, false);
    }

    [Test]
    public void GetBookingDetail_ShouldReturnNullBookingDetail()
    {
        // Arrange
        var bookingId = 123;
        var eventWorkflowStateId = 456;
        DataSet ds = new DataSet();
        
        var mockConfiguration = new Mock<AnalyticsConfiguration>();
        var mockMessaging = new Mock<IMessaging>();
        var mockDatabaseProvider = new Mock<IDatabaseProvider>();
        
        // Set up the MeasurementMessageFactory mock
        var mockMeasurementMessageFactory = new Mock<IMeasurementMessageFactory>();
        var mockMeasurementMessage = new Mock<IEBEMeasurementMessage>();
        mockMeasurementMessageFactory.Setup(x => x.CreateNewMeasurement())
            .Returns(mockMeasurementMessage.Object);
        mockMessaging.Setup(x => x.MeasurementMessageFactory)
            .Returns(mockMeasurementMessageFactory.Object);
        
        // Set up the ExceptionMessage mock
        var mockExceptionMessage = new Mock<IEBEExceptionMessage>();
        mockMessaging.Setup(x => x.ExceptionMessage)
            .Returns(mockExceptionMessage.Object);
        
        mockDatabaseProvider.Setup(x => x.ExecuteDataSet(It.IsAny<string>(), It.IsAny<List<Agoda.EBE.Agents.Common.Object.ParameterInfo>>()))
            .Returns(ds);
        
        var dataAccess = new DataAccess(mockMessaging.Object, mockConfiguration.Object, mockDatabaseProvider.Object);
        
        // Act & Assert
        Assert.Throws<Agoda.EBE.Agents.Common.EbeCommonException.ExecuteStoreProcReturnNoRow>(() =>
            dataAccess.GetBookingDetail(bookingId, eventWorkflowStateId));
    }

    #region HelperMethods

    private DataRow CreateDataRowForGetClientInfo(byte[] compressedDeviceInfo)
    {
        var rowData = new List<DataPropertyObject>
        {
            new DataPropertyObject("device_info", compressedDeviceInfo, typeof(byte[])),
            new DataPropertyObject("client_ip_address", "127.0.0.1", typeof(string)),
            new DataPropertyObject("session_id", "1", typeof(string)),
            new DataPropertyObject("tracking_cookie_id", "12345aaa", typeof(string)),
            new DataPropertyObject("is_user_logged_in", false, typeof(bool)),
            new DataPropertyObject("os_name", "iOS18", typeof(string)),
            new DataPropertyObject("os_version", "1.0", typeof(string)),
            new DataPropertyObject("browser_name", "chrome", typeof(string)),
            new DataPropertyObject("device_brand", "apple", typeof(string)),
            new DataPropertyObject("device_model", "16promax", typeof(string)),
            new DataPropertyObject("origin", "1", typeof(string)),
            new DataPropertyObject("browser_version", "1.0", typeof(string)),
            new DataPropertyObject("browser_subversion", "1.0.1", typeof(string)),
            new DataPropertyObject("browser_build_number", "123", typeof(string)),
            new DataPropertyObject("is_mobile", false, typeof(bool)),
            new DataPropertyObject("is_touch", false, typeof(bool)),
            new DataPropertyObject("searchcluster", string.Empty, typeof(string))
        };

        return DataRowUtil.FromDataRowObject(rowData);
    }

    private ClientInfo InvokeGetClientInfo(DataRow dataRow, int bookingId)
    {
        var method = typeof(DataAccess).GetMethod("GetClientInfo", 
            BindingFlags.NonPublic | BindingFlags.Static);
        
        if (method == null)
            throw new InvalidOperationException("GetClientInfo method not found");
            
        return (ClientInfo)method.Invoke(null, new object[] { dataRow, bookingId });
    }

    #endregion
}