using System;
using System.Linq;
using Agoda.Adp.Messaging.Client.Schema;
using Agoda.Adp.Messaging.Client.TestUtils;
using Agoda.EBE.Agents.Analytics.Object;
using NUnit.Framework;
using Shouldly;
using Assert = NUnit.Framework.Assert;

namespace Agoda.EBE.Agents.Analytics.UnitTest.Object.GenericMessage
{
    [TestFixture]
    public class BookingDetailSpec
    {
        [Test]
        public void CompatibilityTest()
        {
            var message = new BookingDetail();
            var res = MessagingTestUtils.CheckSchemaCompatibility(message);
            res.CompatibilityCode.ShouldNotBe(CompatibilityCheckRes.compat_no);
        }
        
        [Test]
        public void SchemaContainDroppedFields()
        {
            var message = new BookingDetail();
            var res = MessagingTestUtils.CheckSchemaDroppedFields(message);
 
            if (res.ContainsDroppedField)
            {
                Assert.Fail(string.Join(", ", res.ErrorField.Select((key, value) => key + ", " + value).ToList()));
            }
        }
    }
}