﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <IsPackable>false</IsPackable>
    <LangVersion>default</LangVersion>
    <Nullable>enable</Nullable>
    <RunSettingsFilePath>$(MSBuildProjectDirectory)/test.runsettings</RunSettingsFilePath>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Agoda.Adp.Messaging.Client.TestUtils" Version="2.7.0.4" />
    <PackageReference Include="Agoda.Builds.Metrics" Version="1.0.113" />
    <PackageReference Include="coverlet.collector" Version="3.1.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="JunitXml.TestLogger" Version="3.0.110" />
    <PackageReference Include="Microsoft.CodeAnalysis.FxCopAnalyzers" Version="3.3.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="NSubstitute" Version="4.0.0" />
    <PackageReference Include="nunit" Version="3.12.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="3.11.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="15.9.0" />
    <PackageReference Include="CleanMoq" Version="1.0.0" />
    <PackageReference Include="Roslynator.Analyzers" Version="4.12.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Shouldly" Version="3.0.0" />
    <PackageReference Include="Agoda.EBE.Framework" Version="1.1.0.2903" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="TeamCity.VSTest.TestAdapter" Version="1.0.9" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Agoda.EBE.Agents.Analytics\Agoda.EBE.Agents.Analytics.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Agoda.Adp.Messaging.Client.TestUtils, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
      <HintPath>..\..\..\..\..\.nuget\packages\agoda.adp.messaging.client.testutils\2.4.1.4\lib\netstandard2.0\Agoda.Adp.Messaging.Client.TestUtils.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>
