Input: Changes in git diff format
Additional Context: File Content
Please provide feedback for the input with a focus on actionable improvements, while keeping the additional context in mind. Consider the following criteria when reviewing the code:

Code quality
Code readability
Code maintainability
Code performance
Best practices
Security

Instructions:

While additional context information is provided, your feedback should primarily focus on the input (Changes in git diff format).
Limit your feedback to actionable suggestions related to the specific code changes in the file.
Present your feedback as bullet points.
Refrain from providing general feedback, summaries, explanations of changes, or praising good additions.
If no actionable feedback is found or necessary, simply reply with "LGTM" in the review section.