schema: v1
service: ebe-netcore-agents
environments:
  dev:
    spec:
      artifact:
        image:
          repository: bpf/agents
          # If you can't build image locally, please create a merge request and use the mr image tag.
          tag: ${IMAGE_TAG:-latest}
          build:
            context: .
            dockerfile: docker/Dockerfile
      initContainers:
        - name: update-afm-config
          command:
            - /bin/bash
            - -c
            - /opt/mssql-tools/bin/sqlcmd -S mdb -U sa -P agoda123* -Q "use agoda_core; ALTER INDEX IX_afm_configuration ON Agoda_Core.dbo.afm_configuration DISABLE;"
            - /opt/mssql-tools/bin/sqlcmd -S mdb -U sa -P agoda123* -Q "update Agoda_Core.dbo.afm_language set language_use = 1"
          artifact:
            image:
              repository: aiab/microsoft/mssql-tools
              tag: latest
      healthCheck:
        startup:
          exec:
            command:
              - ./publish/healthcheck.sh
              - "5000"
              - "5001"
              - "5002"
              - "5003"
              - "5004"
              - "5005"
          initialDelaySeconds: 30
          timeoutSeconds: 2
          periodSeconds: 5
          failureThreshold: 10
          successThreshold: 1
        readiness:
          exec:
            command:
              - ./publish/healthcheck.sh
              - "5000"
              - "5001"
              - "5002"
              - "5003"
              - "5004"
              - "5005"
          timeoutSeconds: 2
          periodSeconds: 5
          failureThreshold: 10
          successThreshold: 1
      resources:
        requests:
          cpu: "200m"
          memory: 4Gi
        limits:
          memory: 12Gi
      envs:
        IS_DEVSTACK: "true"
        LANG: en_US.UTF-8
        PortNumber: "5000"
        TZ: Asia/Bangkok
        http_proxy: ${http_proxy}
        https_proxy: ${https_proxy}
        no_proxy: "${no_proxy},*************,*************"
        RABBITMQ_HOST: rabbitmq
        RABBITMQ_USERNAME: devrabbit
        RABBITMQ_PASSWORD: 123456 # pragma: allowlist secret
        Agent: UPC_Provisioning_Email_Payment_Analytics_PushBookingSummary
        VAULT_ADDR: http://localhost:8200
        Provisioning__InstantBookConfiguration__YcsMinimumRemainingTimeMs: "60000"
        Provisioning__InstantBookConfiguration__NonYcsMinimumRemainingTimeMs: "60000"
      ports:
        - number: 5000
          protocol: http
        - number: 5001
          protocol: http
        - number: 5002
          protocol: http
        - number: 5003
          protocol: http
        - number: 5004
          protocol: http
        - number: 5005
          protocol: http
      ingresses:
        - enabled: true
          port: 5000
        - enabled: true
          port: 5001
        - enabled: true
          port: 5002
        - enabled: true
          port: 5003
        - enabled: true
          port: 5004
        - enabled: true
          port: 5005
      consul:
        enabled: true
      vault:
        enabled: true
      waitFor:
        services:
          - name: bfdb
            port: 1433
          - name: cdb
            port: 1433
          - name: mdc
            port: 1433
          - name: mdb
            port: 1433
          - name: rabbitmq
            port: 5672
    dependencies:
      # Db Section
      - registry:
          service: bfdb
          environment: pinto_qa_data_hotel-list-postbooking
          version: latest
      - registry:
          service: cdb
          environment: pinto_qa_data_hotel-list-postbooking
          version: latest
      - registry:
          service: paydb
          environment: pinto_qa_data_hotel-list-postbooking
          version: latest
      - registry:
          service: mdc
          environment: pinto_qa_data_enigma
          version: latest
      - include:
          file: devstack/mdb.yaml
          env: pinto_qa_data_hotel-list-postbooking

      # RabbitMQ Section
      - include:
          file: devstack/rabbitmq.yaml
          env: dev
      
      # EBE Section
      - registry:
          service: ebe-workflow-webapi
          environment: dev
          version: latest
      - registry:
          service: ebe-payment-api-netcore
          environment: dev
          version: master
      - registry:
          service: ebe-creditcard-service-netcore
          environment: central
          version: master

      # Whitelabel Section
      - registry:
          service: whitelabel-loyalty-api
          environment: dev
          version: latest
      - registry:
          service: whitelabel-service-api
          environment: dev
          version: latest

      # BPF Section
      - include:
          file: devstack/bpf-mock.yaml
          env: mock
      - include:
          file: devstack/bpf-wiremock.yaml
          env: mock

      - registry:
          service: customerapi
          environment: dev
          version: latest
      
      - include:
          file: devstack/giftcard-api.yaml
          env: mock
      
      - registry:
          service: booking-api
          environment: dev
          version: latest

      - registry:
          service: enigma
          environment: dev
          version: latest

      - include:
          file: devstack/abs-phoenix-app.yaml
          env: dev
      
      - include:
          file: devstack/upc-api.yaml
          env: dev
      
      ### Ideally this dependency should be removed once customer-api and external-loyalty has migrate to use db-cbcache registry.
      - include:
          file: devstack/couchbase.yaml
          env: dev
