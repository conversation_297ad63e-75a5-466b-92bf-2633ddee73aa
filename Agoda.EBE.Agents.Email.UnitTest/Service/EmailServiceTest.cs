﻿// <copyright file="EmailServiceTest.cs" company="Agoda Company Co., Ltd.">
// AGODA ® is a registered trademark of AGIP LLC, used under license by Agoda Company Co., Ltd.. Agoda is part of Priceline (NASDAQ:PCLN)
// </copyright>

using Agoda.Config.Consul.KeyValue.FeatureSwitch.Services;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess.Interface;
using Agoda.EBE.Agents.Email.DataAccess.Interface;
using Agoda.EBE.Agents.Email.Object.Configuration;
using Agoda.EBE.Agents.Email.Object.Configuration.FeatureSwitches;
using Agoda.EBE.Agents.Email.Object.Exceptions;
using Agoda.EBE.Agents.Email.SendEmail.Object;
using Agoda.EBE.Agents.Email.Service;
using Agoda.EBE.Agents.Email.Service.Helpers.Interface;
using Agoda.EBE.Agents.Email.Util;
using Agoda.EBE.Framework;
using Agoda.EBE.Framework.EmailContent.Objects;
using Agoda.EBE.Framework.EmailContent.Objects.Interface;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.Objects.Email;
using Agoda.EBE.Framework.TestUtils;
using Agoda.EBE.Framework.TestUtils.Objects;
using Agoda.Rewards.Api.Definition;
using NSubstitute;
using Shouldly;
using CommonEnum = Agoda.EBE.Agents.Common.Enum;

namespace Agoda.EBE.Agents.Email.UnitTest.Service
{
    using Agoda.EBE.Agents.Email.Object;
    using Agoda.EBE.Agents.Email.Object.Interface;
    using Moq;
    using Moq.Protected;
    using NUnit.Framework;
    using System;
    using System.Collections.Generic;
    using System.Net;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;

    [TestFixture]
    [Category("Email_Agent")]
    [Category("EmailService")]
    public class EmailServiceTest
    {
        // For SOAP protocal
        //private readonly Dictionary<string, string> headers = new Dictionary<string, string>() {
        //    { "SOAPAction", "http://tempuri.org/Send" },
        //    { "Content-Type", "text/xml; charset=utf-8" }
        //};

        private IEmailConfiguration _configuration;
        private IDatabaseAccess _dataAccess;
        private IMessaging _messaging;
        private IDeliveryApiService _deliveryApiService;
        private ICommunicationApiService _communicationApiService;
        private IBookingContactFromContactsApi _bookingContact;
        private IPigeonHelper _pigeonHelper;
        private IWhiteLabelHelperService _wlHelperService;
        private IWhiteLabelConfigService _wlConfigService;
        private IFeatureSwitchService _featureSwitchService;
        private IEmailSendingEligibilityService _emailSendingEligibilityService;
        private const string DefaultString = "default";
        private const int DefaultId = 1;
        public readonly Dictionary<string, string> _dummyMeasurementTags = new Dictionary<string, string>();

        [SetUp]
        public void SetUp()
        {
            var measurementMock = Substitute.For<IEBEMeasurementMessage>();
            measurementMock.Tags.Returns(_dummyMeasurementTags);

            //mock: IMessaging
            _configuration = Substitute.For<IEmailConfiguration>();
            _configuration.IsStoreContent.Returns(true);
            _configuration.DeliveryApiClientId.Returns(5003);
            _configuration.IsEnableUseCusCo.Returns(true);
            _configuration.AllowedSmsTemplateIds.Returns(new List<int> { 4224, 4226 });
            _configuration.NotAllowedCuscoTemplateIdsForEmailLogin.Returns(new List<int> { 4224, 4226 });
            _configuration.AmendmentConfirmationCuscoTemplateId.Returns(4224);
            _configuration.MeasurementEmailTemplateIds.Returns(new List<int> { 123 });
            _configuration.IsPigeonEnabled.Returns(false);
            _configuration.IsCommunicationApiEnabled.Returns(true);
            _configuration.IsEnabledFailedInPigeon.Returns(true);
            _deliveryApiService = Substitute.For<IDeliveryApiService>();
            _communicationApiService = Substitute.For<ICommunicationApiService>();
            _pigeonHelper = Substitute.For<IPigeonHelper>();
            _pigeonHelper.Send(Arg.Any<int>(), Arg.Any<int>(), Arg.Any<int>(), Arg.Any<IBookingItem>(),
                    Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<int>())
                .Returns(p => new PigeonResponse(
                    false,
                    string.Empty,
                    string.Empty,
                    false,
                    false,
                    (int)Email.Enum.PigeonStatusCode.FailPigeonNotSupported,
                    Email.Enum.PigeonStatusCode.FailInternalError.ToString())
                );
            _dataAccess = Substitute.For<IDatabaseAccess>();
            _bookingContact = Substitute.For<IBookingContactFromContactsApi>();
            _bookingContact.PaxContact.Returns(
                new PaxContact
                {
                    Email = DefaultString,
                    Fax = DefaultString,
                    FirstName = DefaultString,
                    LastName = DefaultString,
                    LoginTypes = new List<LoginType> { LoginType.Basic },
                    Phone = DefaultString
                });
            _bookingContact.HotelContact.Returns(
                new HotelContactFromContactsApi
                {
                    Id = DefaultId,
                    Name = DefaultString,
                    Email = DefaultString,
                    LanguageId = DefaultId,
                });
            _bookingContact.SupplierContact.Returns(
                new Contact
                {
                    Email = DefaultString,
                    Fax = DefaultString,
                    LoginTypes = new List<LoginType> { LoginType.Basic },
                    Phone = DefaultString
                });
            _bookingContact.MemberId.Returns(123456);
            _messaging = Substitute.For<IMessaging>();
            _messaging.MeasurementMessageFactory.CreateNewMeasurement().Returns(measurementMock);
            measurementMock.EndTrack(Arg.Any<System.Enum>(), Arg.Any<Boolean>());

            _wlHelperService = Substitute.For<IWhiteLabelHelperService>();
            _wlHelperService.ResolveWhiteLabelId(Arg.Any<int>(), Arg.Any<int?>())
                .Returns((int)Common.Enum.WhitelabelId.Agoda);

            _wlConfigService = Substitute.For<IWhiteLabelConfigService>();
            _wlConfigService.GetWhiteLabelKey(Arg.Any<int>()).Returns(Guid.Empty.ToString());
            
            _featureSwitchService = Substitute.For<IFeatureSwitchService>();
            _featureSwitchService.IsOn<FeatureSwitch_Email_SendEmailViaCommunicationApi>().Returns(true);
            
            // Mock EmailSendingEligibilityService to allow email sending by default
            _emailSendingEligibilityService = Substitute.For<IEmailSendingEligibilityService>();
            _emailSendingEligibilityService.ShouldSkipSendEmail(Arg.Any<IBookingItem>(), Arg.Any<string>(), Arg.Any<int>(), Arg.Any<int>()).Returns(false);
        }

        // For POST method.
        private readonly Dictionary<string, string> headers = new Dictionary<string, string>()
        {
            { "Content-Type", "application/x-www-form-urlencoded" }
        };

        [Test(Description = "return True for Phone Login User and if Cusco Template Id is not in the not allowed List")]
        public void EmailServiceTest_TestPhoneLoginWithTemplateNotInAllowedTemplateId()
        {
            EmailService svc = new EmailService(_configuration, _dataAccess, _messaging, _deliveryApiService,
                _communicationApiService, _pigeonHelper, _wlHelperService, _wlConfigService, _featureSwitchService, _emailSendingEligibilityService);
            var loginTypes = new List<LoginType>();
            loginTypes.Add(LoginType.PhoneNumber);
            var result =
                svc.isCuscoFlagEnabled(4223, loginTypes, _configuration.NotAllowedCuscoTemplateIdsForEmailLogin);
            Assert.AreEqual(result, true);
        }

        [Test(Description = "return True for Phone Login User and if Cusco Template Id is in the not allowed List")]
        public void EmailServiceTest_TestPhoneLoginWithTemplateInAllowedTemplateId()
        {
            EmailService svc = new EmailService(_configuration, _dataAccess, _messaging, _deliveryApiService,
                _communicationApiService, _pigeonHelper, _wlHelperService, _wlConfigService, _featureSwitchService, _emailSendingEligibilityService);
            var loginTypes = new List<LoginType>();
            loginTypes.Add(LoginType.PhoneNumber);
            var result =
                svc.isCuscoFlagEnabled(4224, loginTypes, _configuration.NotAllowedCuscoTemplateIdsForEmailLogin);
            Assert.AreEqual(result, true);
        }

        [Test(Description = "return True for Email Login User and if Cusco Template Id is not in the not allowed List")]
        public void EmailServiceTest_TestEmailLoginWithTemplateNotInAllowedTemplateId()
        {
            EmailService svc = new EmailService(_configuration, _dataAccess, _messaging, _deliveryApiService,
                _communicationApiService, _pigeonHelper, _wlHelperService, _wlConfigService, _featureSwitchService, _emailSendingEligibilityService);
            var loginTypes = new List<LoginType>();
            loginTypes.Add(LoginType.Basic);
            var result =
                svc.isCuscoFlagEnabled(4223, loginTypes, _configuration.NotAllowedCuscoTemplateIdsForEmailLogin);
            Assert.AreEqual(result, true);
        }

        [Test(Description = "return False for Email Login User and if Cusco Template Id is in the not allowed List")]
        public void EmailServiceTest_TestEmailLoginWithTemplateInAllowedTemplateId()
        {
            EmailService svc = new EmailService(_configuration, _dataAccess, _messaging, _deliveryApiService,
                _communicationApiService, _pigeonHelper, _wlHelperService, _wlConfigService, _featureSwitchService, _emailSendingEligibilityService);
            var loginTypes = new List<LoginType>();
            loginTypes.Add(LoginType.Basic);
            var result =
                svc.isCuscoFlagEnabled(4224, loginTypes, _configuration.NotAllowedCuscoTemplateIdsForEmailLogin);
            Assert.AreEqual(result, false);
        }

        [Test(Description = "return NoEmail in action result when sending email process for Bcom booking")]
        public void EmailServiceTest_TestProcessSendingEmail_ToBcom_NoEmailActionResult()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);
            _bookingContact.DmcId = (int)CommonEnum.DmcId.Bcom;

            var result =
                emailService.ProcessSendingEmail(
                    DefaultId
                    , DefaultId
                    , (int)Common.Enum.DmcId.Bcom
                    , DefaultId
                    , DefaultId
                    , 4223
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , ConstantEnum.MailTemplateTo.DMC
                    , _bookingContact
                );

            communicationApiServiceMock.Verify(d => d.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            ), Times.Never);
            Assert.AreEqual(Email.Enum.EmailActionResult.NoEmail, result.ActionResult);
            Assert.AreEqual("Sending email to DMC is disabled for BCOM booking", result.Remark);
        }

        [Test(Description = "return Success in action result when sending email process for YCS booking")]
        public void EmailServiceTest_TestProcessSendingEmail_ToYCS_SuccessActionResult()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            communicationApiServiceMock.Setup(d => d.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).Returns(new EmailResponse(Guid.NewGuid(), string.Empty));
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(new BookingItem()
            {
                WorkflowStateId = 118,
                WhitelabelId = 1
            });
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);
            _bookingContact.DmcId = (int)CommonEnum.DmcId.YCS;

            var result =
                emailService.ProcessSendingEmail(
                    DefaultId
                    , DefaultId
                    , (int)Common.Enum.DmcId.YCS
                    , DefaultId
                    , DefaultId
                    , 4223
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , ConstantEnum.MailTemplateTo.DMC
                    , _bookingContact
                );

            communicationApiServiceMock.Verify(d => d.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            ), Times.Once);
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
        }

        [Test(Description = "return Success in action result when sending email process for Citi")]
        public void EmailServiceTest_TestProcessSendingEmail_ToCiti_SuccessActionResult()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            communicationApiServiceMock.Setup(d => d.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).Returns(new EmailResponse(Guid.NewGuid(), string.Empty));
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(new BookingItem()
            {
                WorkflowStateId = 118,
                WhitelabelId = 51
            });
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);
            _bookingContact.DmcId = (int)CommonEnum.DmcId.YCS;

            var result =
                emailService.ProcessSendingEmail(
                    DefaultId
                    , DefaultId
                    , (int)Common.Enum.DmcId.YCS
                    , DefaultId
                    , DefaultId
                    , 4223
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , ConstantEnum.MailTemplateTo.DMC
                    , _bookingContact
                );

            communicationApiServiceMock.Verify(d => d.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            ), Times.Once);
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
        }

        [Test(Description = "return Success when using sending email alert method for Cusco")]
        public void EmailServiceTest_TestProcessSendingEmailAlertCusco_Success()
        {
            var emailGuid = Guid.NewGuid();
            var dataAccessMock = new Mock<IDatabaseAccess>();
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(new BookingItem()
            {
                WorkflowStateId = 118,
                WhitelabelId = 1
            });

            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            communicationApiServiceMock.Setup(accessor => accessor.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).Returns(new EmailResponse(emailGuid, "", "cusco"));

            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            var result =
                emailService.ProcessSendingEmailAlert(
                    DefaultId
                    , DefaultString
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , DefaultId
                );

            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            Assert.AreEqual(emailGuid, result.EmailGuid);
            Assert.AreEqual(true, result.IsCuscoEmail);
        }

        [Test(Description = "return Success in action result when sending email process with Pigeon")]
        public void EmailServiceTest_SendViaPigeon_SuccessActionResult()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var pigeonHelperMock = new Mock<IPigeonHelper>();
            pigeonHelperMock.Setup(p => p.Send(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()
            )).Returns(
                new PigeonResponse(
                    true,
                    string.Empty,
                    "000a6393-5025-44f2-aacc-d49f7fd46bd6",
                    false,
                    true,
                    (int)Email.Enum.PigeonStatusCode.SuccessSend,
                    Email.Enum.PigeonStatusCode.SuccessSend.ToString())
            );
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, pigeonHelperMock.Object, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);
            _bookingContact.DmcId = (int)CommonEnum.DmcId.YCS;

            var result =
                emailService.ProcessSendingEmail(
                    DefaultId
                    , DefaultId
                    , (int)Common.Enum.DmcId.YCS
                    , DefaultId
                    , 88
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , ConstantEnum.MailTemplateTo.DMC
                    , _bookingContact
                );

            Assert.AreEqual("Sent by Pigeon", result.Remark);
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
        }

        [Test(Description =
            "return No Email in action result when sending email process with Pigeon on No recipient return case")]
        public void EmailServiceTest_SendViaPigeon_SuccessSkip_ActionResult()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var pigeonHelperMock = new Mock<IPigeonHelper>();
            pigeonHelperMock.Setup(p => p.Send(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()
            )).Returns(
                new PigeonResponse(
                    true,
                    string.Empty,
                    null,
                    true,
                    false,
                    (int)Email.Enum.PigeonStatusCode.SuccessSkipContactApiEmpty,
                    Email.Enum.PigeonStatusCode.SuccessSkipContactApiEmpty.ToString())
            );
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, pigeonHelperMock.Object, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);
            _bookingContact.DmcId = (int)CommonEnum.DmcId.YCS;

            var result =
                emailService.ProcessSendingEmail(
                    DefaultId
                    , DefaultId
                    , (int)Common.Enum.DmcId.YCS
                    , DefaultId
                    , 88
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , ConstantEnum.MailTemplateTo.DMC
                    , _bookingContact
                );

            Assert.AreEqual(Email.Enum.PigeonStatusCode.SuccessSkipContactApiEmpty.ToString(), result.Remark);
            Assert.AreEqual(Email.Enum.EmailActionResult.NoEmail, result.ActionResult);
        }

        [Test(Description = "return No Email in action result when sending email process with Pigeon Failed case")]
        public void EmailServiceTest_SendViaPigeon_Failed_NoEmail_ActionResult()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var pigeonHelperMock = new Mock<IPigeonHelper>();
            pigeonHelperMock.Setup(p => p.Send(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()
            )).Returns(
                new PigeonResponse(
                    false,
                    string.Empty,
                    null,
                    false,
                    false,
                    (int)Email.Enum.PigeonStatusCode.FailInternalError,
                    Email.Enum.PigeonStatusCode.FailInternalError.ToString())
            );
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, pigeonHelperMock.Object, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);
            _bookingContact.DmcId = (int)CommonEnum.DmcId.YCS;

            var result =
                emailService.ProcessSendingEmail(
                    DefaultId
                    , DefaultId
                    , (int)Common.Enum.DmcId.YCS
                    , DefaultId
                    , 88
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , ConstantEnum.MailTemplateTo.DMC
                    , _bookingContact
                );

            Assert.AreEqual("Pigeon Failed to build", result.Remark);
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
        }

        [Test(Description = "Skip sending Customer Receipt email via Cusco")]
        public void EmailServiceTest_SendViaPigeon_SuccessSkip_SendViaCusco_Skip()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var pigeonHelperMock = new Mock<IPigeonHelper>();
            pigeonHelperMock.Setup(p => p.Send(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()
            )).Returns(
                new PigeonResponse(
                true,
                string.Empty,
                null,
                false,
                false,
                (int)Email.Enum.PigeonStatusCode.SuccessSend,
                Email.Enum.PigeonStatusCode.SuccessSend.ToString())
            );
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(new BookingItem()
            {
                WorkflowStateId = 118,
                WhitelabelId = 1
            });
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService, communicationApiServiceMock.Object, pigeonHelperMock.Object, _wlHelperService, _wlConfigService, _featureSwitchService, _emailSendingEligibilityService);
            _bookingContact.DmcId = (int)CommonEnum.DmcId.YCS;

            var result =
                emailService.ProcessSendingEmail(
                    DefaultId
                    , DefaultId
                    , (int)Common.Enum.DmcId.YCS
                    , DefaultId
                    , DefaultId
                    , (int)Email.Enum.CuscoTemplateId.CustomerReceipt
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , ConstantEnum.MailTemplateTo.Customer
                    , _bookingContact
                );

            Assert.AreEqual(EmailUtil.GetDefaultRequestIdForSkippedEmail(), result.EmailGuid);
            Assert.AreEqual("Skipped for CuscoTemplateId: 14094", result.Remark);
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
        }

        [Test(Description = "return whiteLabelId, resellDeadline")]
        public void EmailServiceTest_GetCuscoPlaceHolderData_Success()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var pigeonHelperMock = new Mock<IPigeonHelper>();
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, pigeonHelperMock.Object, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            const int bookingId = 12345;
            const int languageId = 1;
            const int memberId = 9999;
            const int hotelId = 1111;
            const int cuscoTemplateId = 4224;
            DateTime dmcDueDate = new DateTime(2023, 01, 10);
            const string name = "test name";
            const string address = "test address";
            var bookingItem = new BookingItem()
            {
                WhitelabelId = 1,
                DmcDueDate = dmcDueDate
            };

            var result = emailService.GetCuscoPlaceHolderData(bookingId, languageId, memberId, hotelId, bookingItem,
                name, address);
            var expected = new Dictionary<string, string>
            {
                { "bookingId", "12345" },
                { "languageId", "1" },
                { "memberId", "9999" },
                { "hotelId", "1111" },
                { "whiteLabelId", "1" },
                { "cname", "test name" },
                { "caddr", "test address" },
                { "resellDeadline", "2023-01-10" }
            };
            Assert.AreEqual(expected, result);
        }

        [Test(Description = "Do not return whiteLabelId, resellDeadline")]
        public void EmailServiceTest_GetCuscoPlaceHolderData_Success_WhenWhitelabelIdAndDmcDueDateAreNulls()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var pigeonHelperMock = new Mock<IPigeonHelper>();
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, pigeonHelperMock.Object, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            var bookingId = 12345;
            var languageId = 1;
            var memberId = 9999;
            var hotelId = 1111;
            var name = "test name";
            var address = "test address";
            var bookingItem = new BookingItem();

            var result = emailService.GetCuscoPlaceHolderData(bookingId, languageId, memberId, hotelId, bookingItem,
                name, address);
            var expected = new Dictionary<string, string>
            {
                { "bookingId", "12345" },
                { "languageId", "1" },
                { "memberId", "9999" },
                { "hotelId", "1111" },
                { "cname", "test name" },
                { "caddr", "test address" }
            };
            Assert.AreEqual(expected, result);
        }

        [Test(Description = "return name, address")]
        public void EmailServiceTest_GetCuscoPlaceHolderData_Success_WhenNameAndAddressAreNulls()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var pigeonHelperMock = new Mock<IPigeonHelper>();
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, pigeonHelperMock.Object, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            var bookingId = 12345;
            var languageId = 1;
            var memberId = 9999;
            var hotelId = 1111;
            var dmcDueDate = new DateTime(2023, 01, 10);
            var bookingItem = new BookingItem()
            {
                WhitelabelId = 1,
                DmcDueDate = dmcDueDate
            };

            var result =
                emailService.GetCuscoPlaceHolderData(bookingId, languageId, memberId, hotelId, bookingItem, null, null);
            var expected = new Dictionary<string, string>
            {
                { "bookingId", "12345" },
                { "languageId", "1" },
                { "memberId", "9999" },
                { "hotelId", "1111" },
                { "whiteLabelId", "1" },
                { "resellDeadline", "2023-01-10" }
            };
            Assert.AreEqual(expected, result);
        }

        [Test(Description = "Throw exception if you go to WinEmail Flow")]
        [TestCase(51)]
        [TestCase(58)]
        public void EmailServiceTest_TestProcessSendingEmail_FailAndThrowForWinEmail(int wlid)
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(new BookingItem()
            {
                WorkflowStateId = 118,
                WhitelabelId = wlid
            });
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);
            _bookingContact.DmcId = (int)CommonEnum.DmcId.YCS;

            Should.Throw<SendViaDeprecatedWinEmailException>(() =>
                emailService.ProcessSendingEmail(
                    DefaultId,
                    DefaultId,
                    (int)Common.Enum.DmcId.YCS,
                    DefaultId,
                    10000,
                    0,
                    DefaultId,
                    DefaultId,
                    DefaultId,
                    ConstantEnum.MailTemplateTo.Customer,
                    _bookingContact
                )
            );
        }

        [Test(Description = "Throw exception if it's Rurubu and SendViaPigeon fail")]
        [TestCase(4)]
        public void EmailServiceTest_TestProcessSendingEmail_SendViaPigeon_Fail_AndThrowForRurubu(int wlid)
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var wlServiceMock = new Mock<IWhiteLabelConfigService>();
            wlServiceMock.Setup(p => p.GetWhiteLabelKey(It.IsAny<int>())).Returns("RURUBU");
            var pigeonHelperMock = new Mock<IPigeonHelper>();
            pigeonHelperMock.Setup(p => p.Send(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()
            )).Returns(
                new PigeonResponse(
                    false,
                    string.Empty,
                    null,
                    false,
                    false,
                    (int)Email.Enum.PigeonStatusCode.SuccessSkipContactApiEmpty,
                    Email.Enum.PigeonStatusCode.SuccessSkipContactApiEmpty.ToString())
            );
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(new BookingItem()
            {
                WorkflowStateId = 118,
                WhitelabelId = wlid
            });
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, pigeonHelperMock.Object, _wlHelperService, wlServiceMock.Object,
                _featureSwitchService, _emailSendingEligibilityService);
            _bookingContact.DmcId = (int)CommonEnum.DmcId.YCS;

            Should.Throw<SendViaDeprecatedWinEmailException>(() =>
                emailService.ProcessSendingEmail(
                    DefaultId,
                    DefaultId,
                    (int)Common.Enum.DmcId.YCS,
                    DefaultId,
                    10000,
                    0,
                    DefaultId,
                    DefaultId,
                    DefaultId,
                    ConstantEnum.MailTemplateTo.Customer,
                    _bookingContact
                )
            );
        }

        [Test(Description = "return Success in action result when sending email process for template id 0")]
        public void EmailServiceTest_TestProcessSendingEmail_SkipWebService_SuccessActionResult()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(new BookingItem()
            {
                WorkflowStateId = 118,
                WhitelabelId = 51
            });
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            _bookingContact.DmcId = (int)CommonEnum.DmcId.YCS;

            var result =
                emailService.ProcessSendingEmail(
                    DefaultId
                    , DefaultId
                    , (int)Common.Enum.DmcId.YCS
                    , DefaultId
                    , 0
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , DefaultId
                    , ConstantEnum.MailTemplateTo.DMC
                    , _bookingContact
                );

            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            Assert.AreEqual("05b701c2-2366-4593-8f4c-bbb03d2a4961", result.EmailGuid.ToString());
            Assert.AreEqual("Skipped for TemplateId: 0", result.Remark);
        }

        public BookingItem getMockBookingInfo(int bookingLanguageId)
        {
            var rowDataFromDb = new List<DataPropertyObject>
            {
                new DataPropertyObject("booking_id", 12345678, typeof(int)),
                new DataPropertyObject("workflow_state_id", 111, typeof(int)),
                new DataPropertyObject("workflow_action_id", 22, typeof(int)),
                new DataPropertyObject("ss_id_fb", DBNull.Value, typeof(int)), //int32
                new DataPropertyObject("language_id", bookingLanguageId, typeof(int)),
                new DataPropertyObject("workflow_parameter_value",
                    "<Parameter><ModuleID>2</ModuleID><TemplateID>88</TemplateID><Priority>1</Priority><EmailFormat>2</EmailFormat><NotesTypeID>2</NotesTypeID></Parameter>",
                    typeof(string)),
                new DataPropertyObject("dmc_code", "YCS", typeof(string)),
                new DataPropertyObject("dmc_id", 332, typeof(int)),
                new DataPropertyObject("dmc_type", 2, typeof(int)),
                new DataPropertyObject("booking_date_from", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("booking_date_until", "2012-04-08 00:00:00", typeof(DateTime)),
                new DataPropertyObject("other_special_needs", "Good Sea View", typeof(string)),
                new DataPropertyObject("is_multiple_bookings", "false", typeof(bool)),
                new DataPropertyObject("is_fraud_verify_sent", "false", typeof(bool)),
                new DataPropertyObject("itinerary_id", 293, typeof(int)),
                new DataPropertyObject("cid_list", 1234, typeof(int)),
                new DataPropertyObject("payment_model", 1, typeof(int)),
                new DataPropertyObject("cancellation_info",
                    string.Empty, typeof(string)),
                new DataPropertyObject("request_type", 14, typeof(int)),
                new DataPropertyObject("amendment_id", 123456, typeof(int)),
                new DataPropertyObject("is_skip_customer_cxl_confirm_email", "false", typeof(bool)),
                new DataPropertyObject("whitelabel_id", 1, typeof(int)),
                new DataPropertyObject("dmc_due_date", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("origin", "HK", typeof(string)),
                new DataPropertyObject("device_type_id", 0, typeof(string)),
                new DataPropertyObject("tracking_cookie_id", "457c86dd-3046-4759-a924-8c1c7b2cb0be", typeof(string)),
                new DataPropertyObject("pricefreeze_booking_id", 12345678, typeof(int))
            };
            var dataRow = DataRowUtil.FromDataRowObject(rowDataFromDb);
            return new BookingItem(dataRow);
        }

        [Test(Description = "GetShouldFallbackAfterPigeonFailed returns default true when bookingItem.WhitelabelId is null")]
        public void EmailServiceTest_GetShouldFallbackAfterPigeonFailed_WithNullWhitelabelId_ReturnsDefaultTrue()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var wlConfigServiceMock = new Mock<IWhiteLabelConfigService>();
            var bookingItem = new BookingItem { WhitelabelId = null };
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(bookingItem);
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, wlConfigServiceMock.Object,
                _featureSwitchService, _emailSendingEligibilityService);

            // Test via SendEmail functionality since GetShouldFallbackAfterPigeonFailed is private
            // When WhitelabelId is null, it should fallback to Cusco sending, but since cuscoTemplateId is 0,
            // it will throw SendViaDeprecatedWinEmailException
            Should.Throw<SendViaDeprecatedWinEmailException>(() =>
                emailService.ProcessSendingEmail(
                    DefaultId,
                    DefaultId,
                    (int)Common.Enum.DmcId.YCS,
                    DefaultId,
                    DefaultId,
                    0, // cuscoTemplateId = 0 to trigger non-Cusco path
                    DefaultId,
                    DefaultId,
                    DefaultId,
                    ConstantEnum.MailTemplateTo.Customer,
                    _bookingContact
                )
            );
        }

        [Test(Description = "GetShouldFallbackAfterPigeonFailed returns default true when wlToken is null or empty")]
        public void EmailServiceTest_GetShouldFallbackAfterPigeonFailed_WithNullWlToken_ReturnsDefaultTrue()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var wlConfigServiceMock = new Mock<IWhiteLabelConfigService>();
            var bookingItem = new BookingItem { WhitelabelId = 1 };
            
            // Return null for wlToken
            wlConfigServiceMock.Setup(service => service.GetWhiteLabelKey(It.IsAny<int>())).Returns((string)null);
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(bookingItem);
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, wlConfigServiceMock.Object,
                _featureSwitchService, _emailSendingEligibilityService);

            // Test via SendEmail functionality - with null wlToken, it should use default fallback behavior
            Should.Throw<SendViaDeprecatedWinEmailException>(() =>
                emailService.ProcessSendingEmail(
                    DefaultId,
                    DefaultId,
                    (int)Common.Enum.DmcId.YCS,
                    DefaultId,
                    DefaultId,
                    0, // cuscoTemplateId = 0 to trigger non-Cusco path
                    DefaultId,
                    DefaultId,
                    DefaultId,
                    ConstantEnum.MailTemplateTo.Customer,
                    _bookingContact
                )
            );
        }

        [Test(Description = "GetShouldFallbackAfterPigeonFailed returns template config ShouldFallback value when available")]
        public void EmailServiceTest_GetShouldFallbackAfterPigeonFailed_WithTemplateConfig_ReturnsTemplateConfigValue()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var wlConfigServiceMock = new Mock<IWhiteLabelConfigService>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var testWlToken = Guid.NewGuid().ToString();
            var testTemplateId = 88; // HotelVoucher template
            
            // Mock the PigeonEmailListFeatureConfig with ShouldFallback = false
            var pigeonEmailListConfig = new PigeonEmailListFeatureConfig
            {
                AllowedTemplates = new List<PigeonEmailTemplate>
                {
                    new PigeonEmailTemplate
                    {
                        TemplateName = "HotelVoucher",
                        ExperimentName = "TestExperiment",
                        ShouldFallback = false
                    }
                }
            };
            
            wlConfigServiceMock.Setup(service => service.GetWhiteLabelKey(It.IsAny<int>())).Returns(testWlToken);
            wlConfigServiceMock.Setup(service => service.IsFeatureEnabled(
                "PigeonEmailList", It.IsAny<Guid>(), It.IsAny<bool?>(), It.IsAny<int?>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(true);
            wlConfigServiceMock.Setup(service => service.GetFeatureConfigByKey<PigeonEmailListFeatureConfig>(
                testWlToken, "PigeonEmailList", It.IsAny<bool?>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(pigeonEmailListConfig);
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(bookingItem);
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, wlConfigServiceMock.Object,
                _featureSwitchService, _emailSendingEligibilityService);

            // Since ShouldFallback is false and we have a valid cuscoTemplateId, it should not fallback to Cusco
            // This will result in throwing SendViaDeprecatedWinEmailException because shouldFallbackAfterPigeonFailed is false
            Should.Throw<SendViaDeprecatedWinEmailException>(() =>
                emailService.ProcessSendingEmail(
                    DefaultId,
                    DefaultId,
                    (int)Common.Enum.DmcId.YCS,
                    DefaultId,
                    testTemplateId,
                    4223, // valid cuscoTemplateId
                    DefaultId,
                    DefaultId,
                    DefaultId,
                    ConstantEnum.MailTemplateTo.Customer,
                    _bookingContact
                )
            );
        }

        [Test(Description = "PigeonEmailList feature disabled should return default fallback behavior")]
        public void EmailServiceTest_PigeonFeatureDisabled_ReturnsDefaultFallback()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var wlConfigServiceMock = new Mock<IWhiteLabelConfigService>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var testWlToken = "test-wl-token";
            var testTemplateId = 88; // HotelVoucher template
            
            wlConfigServiceMock.Setup(service => service.GetWhiteLabelKey(It.IsAny<int>())).Returns(testWlToken);
            // Feature is disabled
            wlConfigServiceMock.Setup(service => service.IsFeatureEnabled(
                "PigeonEmailList", It.IsAny<Guid>(), It.IsAny<bool?>(), It.IsAny<int?>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(false);
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(bookingItem);
            communicationApiServiceMock.Setup(service => service.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).Returns(new EmailResponse(Guid.NewGuid(), string.Empty));
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, wlConfigServiceMock.Object,
                _featureSwitchService, _emailSendingEligibilityService);

            // Since feature is disabled, it should use default fallback behavior (true)
            // This should successfully send via Cusco
            var result = emailService.ProcessSendingEmail(
                DefaultId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                testTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.Customer,
                _bookingContact
            );

            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            communicationApiServiceMock.Verify(service => service.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            ), Times.Once);
        }

        [Test(Description = "Invalid wlToken format should return default fallback behavior")]
        public void EmailServiceTest_InvalidWlTokenFormat_ReturnsDefaultFallback()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var wlConfigServiceMock = new Mock<IWhiteLabelConfigService>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var invalidWlToken = "invalid-guid-format";
            var testTemplateId = 88; // HotelVoucher template
            
            wlConfigServiceMock.Setup(service => service.GetWhiteLabelKey(It.IsAny<int>())).Returns(invalidWlToken);
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(bookingItem);
            communicationApiServiceMock.Setup(service => service.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).Returns(new EmailResponse(Guid.NewGuid(), string.Empty));
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, wlConfigServiceMock.Object,
                _featureSwitchService, _emailSendingEligibilityService);

            // Since wlToken format is invalid (not a valid GUID), it should use default fallback behavior (true)
            var result = emailService.ProcessSendingEmail(
                DefaultId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                testTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.Customer,
                _bookingContact
            );

            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            communicationApiServiceMock.Verify(service => service.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            ), Times.Once);
        }

        [Test(Description = "Template not found in PigeonTemplateData should return default fallback behavior")]
        public void EmailServiceTest_TemplateNotFoundInPigeonData_ReturnsDefaultFallback()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var wlConfigServiceMock = new Mock<IWhiteLabelConfigService>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var testWlToken = Guid.NewGuid().ToString();
            var unknownTemplateId = 99999; // Template not in PigeonTemplateData
            
            wlConfigServiceMock.Setup(service => service.GetWhiteLabelKey(It.IsAny<int>())).Returns(testWlToken);
            wlConfigServiceMock.Setup(service => service.IsFeatureEnabled(
                "PigeonEmailList", It.IsAny<Guid>(), It.IsAny<bool?>(), It.IsAny<int?>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(true);
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(bookingItem);
            communicationApiServiceMock.Setup(service => service.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).Returns(new EmailResponse(Guid.NewGuid(), string.Empty));
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, wlConfigServiceMock.Object,
                _featureSwitchService, _emailSendingEligibilityService);

            // Since template is not found in PigeonTemplateData, it should use default fallback behavior (true)
            var result = emailService.ProcessSendingEmail(
                DefaultId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                unknownTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.Customer,
                _bookingContact
            );

            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            communicationApiServiceMock.Verify(service => service.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            ), Times.Once);
        }

        [Test(Description = "Template name found but not in allowed templates should return default fallback behavior")]
        public void EmailServiceTest_TemplateNotInAllowedList_ReturnsDefaultFallback()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var wlConfigServiceMock = new Mock<IWhiteLabelConfigService>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var testWlToken = Guid.NewGuid().ToString();
            var testTemplateId = 88; // HotelVoucher template
            
            // Mock the PigeonEmailListFeatureConfig with different template (not HotelVoucher)
            var pigeonEmailListConfig = new PigeonEmailListFeatureConfig
            {
                AllowedTemplates = new List<PigeonEmailTemplate>
                {
                    new PigeonEmailTemplate
                    {
                        TemplateName = "DifferentTemplate",
                        ExperimentName = "TestExperiment",
                        ShouldFallback = false
                    }
                }
            };
            
            wlConfigServiceMock.Setup(service => service.GetWhiteLabelKey(It.IsAny<int>())).Returns(testWlToken);
            wlConfigServiceMock.Setup(service => service.IsFeatureEnabled(
                "PigeonEmailList", It.IsAny<Guid>(), It.IsAny<bool?>(), It.IsAny<int?>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(true);
            wlConfigServiceMock.Setup(service => service.GetFeatureConfigByKey<PigeonEmailListFeatureConfig>(
                testWlToken, "PigeonEmailList", It.IsAny<bool?>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(pigeonEmailListConfig);
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(bookingItem);
            communicationApiServiceMock.Setup(service => service.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).Returns(new EmailResponse(Guid.NewGuid(), string.Empty));
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, wlConfigServiceMock.Object,
                _featureSwitchService, _emailSendingEligibilityService);

            // Since template is not in allowed list, it should use default fallback behavior (true)
            var result = emailService.ProcessSendingEmail(
                DefaultId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                testTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.Customer,
                _bookingContact
            );

            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            communicationApiServiceMock.Verify(service => service.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            ), Times.Once);
        }

        [Test(Description = "Exception in GetPigeonEmailTemplateConfig should return default fallback behavior")]
        public void EmailServiceTest_ExceptionInPigeonTemplateConfig_ReturnsDefaultFallback()
        {
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var wlConfigServiceMock = new Mock<IWhiteLabelConfigService>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var testWlToken = "test-wl-token";
            var testTemplateId = 88; // HotelVoucher template
            
            wlConfigServiceMock.Setup(service => service.GetWhiteLabelKey(It.IsAny<int>())).Returns(testWlToken);
            wlConfigServiceMock.Setup(service => service.IsFeatureEnabled(
                "PigeonEmailList", It.IsAny<Guid>(), It.IsAny<bool?>(), It.IsAny<int?>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(true);
            // Throw exception when getting feature config
            wlConfigServiceMock.Setup(service => service.GetFeatureConfigByKey<PigeonEmailListFeatureConfig>(
                testWlToken, "PigeonEmailList", It.IsAny<bool?>(), It.IsAny<string>(), It.IsAny<int>()))
                .Throws(new Exception("Test exception"));
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(It.IsAny<int>())).Returns(bookingItem);
            communicationApiServiceMock.Setup(service => service.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).Returns(new EmailResponse(Guid.NewGuid(), string.Empty));
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, wlConfigServiceMock.Object,
                _featureSwitchService, _emailSendingEligibilityService);

            // Since exception occurred, it should use default fallback behavior (true)
            var result = emailService.ProcessSendingEmail(
                DefaultId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                testTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.Customer,
                _bookingContact
            );

            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            communicationApiServiceMock.Verify(service => service.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            ), Times.Once);
        }

        #region SkipSendEmail Feature Integration Tests

        [Test(Description = "SkipSendEmail feature should skip email sending when enabled and configured")]
        public void EmailServiceTest_SkipSendEmailEnabled_SkipsEmailSending()
        {
            // Arrange
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var testTemplateId = 88; // HotelVoucher template
            var testBookingId = 12345;
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(testBookingId)).Returns(bookingItem);
            
            // Setup EmailSendingEligibilityService to return true (skip email)
            _emailSendingEligibilityService.ShouldSkipSendEmail(
                Arg.Any<IBookingItem>(), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId)
                .Returns(true);
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                _communicationApiService, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            // Act
            var result = emailService.ProcessSendingEmail(
                testBookingId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                testTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.Customer,
                _bookingContact
            );

            // Assert
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            Assert.AreEqual(EmailUtil.GetDefaultRequestIdForSkippedEmail(), result.EmailGuid);
            Assert.AreEqual("Email sending skipped by SkipSendEmail feature", result.Remark);
            
            // Verify that the eligibility service was called with correct parameters
            _emailSendingEligibilityService.Received(1).ShouldSkipSendEmail(
                Arg.Is<IBookingItem>(item => item.WhitelabelId == 1), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId);
        }

        [Test(Description = "SkipSendEmail feature should continue email sending when disabled")]
        public void EmailServiceTest_SkipSendEmailDisabled_ContinuesEmailSending()
        {
            // Arrange
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var communicationApiServiceMock = new Mock<ICommunicationApiService>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var testTemplateId = 88; // HotelVoucher template
            var testBookingId = 12345;
            var expectedGuid = Guid.NewGuid();
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(testBookingId)).Returns(bookingItem);
            communicationApiServiceMock.Setup(service => service.Send(
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).Returns(new EmailResponse(expectedGuid, "Sent successfully"));
            
            // Setup EmailSendingEligibilityService to return false (continue email sending)
            _emailSendingEligibilityService.ShouldSkipSendEmail(
                Arg.Any<IBookingItem>(), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId)
                .Returns(false);
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                communicationApiServiceMock.Object, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            // Act
            var result = emailService.ProcessSendingEmail(
                testBookingId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                testTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.Customer,
                _bookingContact
            );

            // Assert
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            Assert.AreEqual(expectedGuid, result.EmailGuid);
            Assert.AreEqual("Sent successfully", result.Remark);
            
            // Verify that the eligibility service was called
            _emailSendingEligibilityService.Received(1).ShouldSkipSendEmail(
                Arg.Any<IBookingItem>(), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId);
        }

        [Test(Description = "SkipSendEmail feature should work with DMC email sending")]
        public void EmailServiceTest_SkipSendEmail_WithDMCEmailSending()
        {
            // Arrange
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var testTemplateId = 88; // HotelVoucher template
            var testBookingId = 12345;
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(testBookingId)).Returns(bookingItem);
            
            // Setup EmailSendingEligibilityService to return true (skip email)
            _emailSendingEligibilityService.ShouldSkipSendEmail(
                Arg.Any<IBookingItem>(), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId)
                .Returns(true);
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                _communicationApiService, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            // Act
            var result = emailService.ProcessSendingEmail(
                testBookingId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                testTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.DMC,
                _bookingContact
            );

            // Assert
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            Assert.AreEqual(EmailUtil.GetDefaultRequestIdForSkippedEmail(), result.EmailGuid);
            Assert.AreEqual("Email sending skipped by SkipSendEmail feature", result.Remark);
        }

        [Test(Description = "SkipSendEmail feature should work with Custom email sending")]
        public void EmailServiceTest_SkipSendEmail_WithCustomEmailSending()
        {
            // Arrange
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var testTemplateId = 88; // HotelVoucher template
            var testBookingId = 12345;
            var notificationChannel = new NotificationChannel { Value = "<EMAIL>" };
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(testBookingId)).Returns(bookingItem);
            
            // Setup EmailSendingEligibilityService to return true (skip email)
            _emailSendingEligibilityService.ShouldSkipSendEmail(
                Arg.Any<IBookingItem>(), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId)
                .Returns(true);
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                _communicationApiService, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            // Act
            var result = emailService.ProcessSendingEmail(
                testBookingId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                testTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.Custom,
                _bookingContact,
                notificationChannel
            );

            // Assert
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            Assert.AreEqual(EmailUtil.GetDefaultRequestIdForSkippedEmail(), result.EmailGuid);
            Assert.AreEqual("Email sending skipped by SkipSendEmail feature", result.Remark);
        }

        [Test(Description = "SkipSendEmail feature should log appropriate message when email is skipped")]
        public void EmailServiceTest_SkipSendEmail_LogsSkippedMessage()
        {
            // Arrange
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var testTemplateId = 88; // HotelVoucher template
            var testBookingId = 12345;
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(testBookingId)).Returns(bookingItem);
            
            // Setup EmailSendingEligibilityService to return true (skip email)
            _emailSendingEligibilityService.ShouldSkipSendEmail(
                Arg.Any<IBookingItem>(), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId)
                .Returns(true);
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                _communicationApiService, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            // Act
            var result = emailService.ProcessSendingEmail(
                testBookingId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                testTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.Customer,
                _bookingContact
            );

            // Assert
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
        }

        [Test(Description = "SkipSendEmail feature should work with ProcessSendingEmailAlert")]
        public void EmailServiceTest_SkipSendEmail_WithProcessSendingEmailAlert()
        {
            // Arrange
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var testTemplateId = 88; // HotelVoucher template
            var testBookingId = 12345;
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(testBookingId)).Returns(bookingItem);
            
            // Setup EmailSendingEligibilityService to return true (skip email)
            _emailSendingEligibilityService.ShouldSkipSendEmail(
                Arg.Any<IBookingItem>(), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId)
                .Returns(true);
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                _communicationApiService, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            // Act
            var result = emailService.ProcessSendingEmailAlert(
                testBookingId,
                "<EMAIL>",
                1, // languageId
                1, // sendModuleId
                testTemplateId,
                1, // sendPriority
                1, // sendEmailFormat
                4223 // cuscoTemplateId
            );

            // Assert
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            Assert.AreEqual(EmailUtil.GetDefaultRequestIdForSkippedEmail(), result.EmailGuid);
            Assert.AreEqual("Email sending skipped by SkipSendEmail feature", result.Remark);
        }

        [Test(Description = "SkipSendEmail feature should work with test bookings")]
        public void EmailServiceTest_SkipSendEmail_WithTestBooking()
        {
            // Arrange
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = true };
            var testTemplateId = 88; // HotelVoucher template
            var testBookingId = 12345;
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(testBookingId)).Returns(bookingItem);
            
            // Setup EmailSendingEligibilityService to return true (skip email)
            _emailSendingEligibilityService.ShouldSkipSendEmail(
                Arg.Any<IBookingItem>(), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId)
                .Returns(true);
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                _communicationApiService, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            // Act
            var result = emailService.ProcessSendingEmail(
                testBookingId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                testTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.Customer,
                _bookingContact
            );

            // Assert
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            Assert.AreEqual(EmailUtil.GetDefaultRequestIdForSkippedEmail(), result.EmailGuid);
            
            // Verify that the eligibility service was called with test booking
            _emailSendingEligibilityService.Received(1).ShouldSkipSendEmail(
                Arg.Is<IBookingItem>(item => item.IsTestBooking == true), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId);
        }

        [Test(Description = "SkipSendEmail feature should work with different template types")]
        public void EmailServiceTest_SkipSendEmail_WithDifferentTemplateTypes()
        {
            // Arrange
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var testTemplateId = 79; // CustomerReceipt template
            var testBookingId = 12345;
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(testBookingId)).Returns(bookingItem);
            
            // Setup EmailSendingEligibilityService to return true (skip email)
            _emailSendingEligibilityService.ShouldSkipSendEmail(
                Arg.Any<IBookingItem>(), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId)
                .Returns(true);
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                _communicationApiService, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            // Act
            var result = emailService.ProcessSendingEmail(
                testBookingId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                testTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.Customer,
                _bookingContact
            );

            // Assert
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            Assert.AreEqual(EmailUtil.GetDefaultRequestIdForSkippedEmail(), result.EmailGuid);
            
            // Verify that the eligibility service was called with correct template ID
            _emailSendingEligibilityService.Received(1).ShouldSkipSendEmail(
                Arg.Any<IBookingItem>(), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId);
        }

        [Test(Description = "SkipSendEmail feature should work when combined with Pigeon skip")]
        public void EmailServiceTest_SkipSendEmail_WithPigeonSkip()
        {
            // Arrange
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var pigeonHelperMock = new Mock<IPigeonHelper>();
            var bookingItem = new BookingItem { WhitelabelId = 1, IsTestBooking = false };
            var testTemplateId = 88; // HotelVoucher template
            var testBookingId = 12345;
            
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(testBookingId)).Returns(bookingItem);
            
            // Setup Pigeon to skip
            pigeonHelperMock.Setup(p => p.Send(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()
            )).Returns(new PigeonResponse(
                true,
                string.Empty,
                null,
                false,
                true, // IsSkipped = true
                (int)Email.Enum.PigeonStatusCode.SuccessSkipContactApiEmpty,
                Email.Enum.PigeonStatusCode.SuccessSkipContactApiEmpty.ToString())
            );
            
            // Setup EmailSendingEligibilityService to return true (skip email)
            // This should be called first and should skip before reaching Pigeon
            _emailSendingEligibilityService.ShouldSkipSendEmail(
                Arg.Any<IBookingItem>(), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId)
                .Returns(true);
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                _communicationApiService, pigeonHelperMock.Object, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            // Act
            var result = emailService.ProcessSendingEmail(
                testBookingId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                testTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.Customer,
                _bookingContact
            );

            // Assert
            Assert.AreEqual(Email.Enum.EmailActionResult.Success, result.ActionResult);
            Assert.AreEqual(EmailUtil.GetDefaultRequestIdForSkippedEmail(), result.EmailGuid);
            Assert.AreEqual("Email sending skipped by SkipSendEmail feature", result.Remark);
            
            // Verify that SkipSendEmail was called first and Pigeon was not called
            _emailSendingEligibilityService.Received(1).ShouldSkipSendEmail(
                Arg.Any<IBookingItem>(), 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId);
            
            // Pigeon should not be called since SkipSendEmail returned true first
            pigeonHelperMock.Verify(p => p.Send(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<IBookingItem>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()), Times.Never);
        }

        [Test(Description = "SkipSendEmail feature should handle null booking item gracefully")]
        public void EmailServiceTest_SkipSendEmail_WithNullBookingItem()
        {
            // Arrange
            var dataAccessMock = new Mock<IDatabaseAccess>();
            var testTemplateId = 88; // HotelVoucher template
            var testBookingId = 12345;
            
            // Return null booking item
            dataAccessMock.Setup(accessor => accessor.GetBookingInfo(testBookingId)).Returns((BookingItem)null);
            
            // Setup EmailSendingEligibilityService to return false when booking item is null
            _emailSendingEligibilityService.ShouldSkipSendEmail(
                null, 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId)
                .Returns(false);
            
            var emailService = new EmailService(_configuration, dataAccessMock.Object, _messaging, _deliveryApiService,
                _communicationApiService, _pigeonHelper, _wlHelperService, _wlConfigService,
                _featureSwitchService, _emailSendingEligibilityService);

            // Act & Assert
            // This should throw an exception due to null booking item in downstream processing
            // but the SkipSendEmail check should still be called
            Should.Throw<Exception>(() => emailService.ProcessSendingEmail(
                testBookingId,
                DefaultId,
                (int)Common.Enum.DmcId.YCS,
                DefaultId,
                testTemplateId,
                4223, // valid cuscoTemplateId
                DefaultId,
                DefaultId,
                DefaultId,
                ConstantEnum.MailTemplateTo.Customer,
                _bookingContact
            ));
            
            // Verify that the eligibility service was called with null booking item
            _emailSendingEligibilityService.Received(1).ShouldSkipSendEmail(
                null, 
                Arg.Any<string>(), 
                testBookingId, 
                testTemplateId);
        }

        #endregion


    }
}