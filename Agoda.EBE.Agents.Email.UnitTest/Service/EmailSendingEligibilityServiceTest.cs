using System;
using System.Collections.Generic;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.EBE.Agents.Email.Object.Configuration;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess.Interface;
using Agoda.EBE.Agents.Email.Object.Interface;
using Agoda.EBE.Framework.Messaging;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Email.UnitTest.Service
{
    [TestFixture]
    [Category("EmailSendingEligibilityService")]
    public class EmailSendingEligibilityServiceTest
    {
        private EmailSendingEligibilityService _eligibilityService;
        private IWhiteLabelConfigService _wlConfigServiceMock;
        private IMessaging _messagingMock;
        private IBookingItem _bookingItemMock;

        private const string DefaultWlToken = "12345678-1234-1234-1234-123456789012";
        private const int DefaultTemplateId = 88; // HotelVoucher template
        private const int DefaultBookingId = 12345;

        [SetUp]
        public void SetUp()
        {
            _wlConfigServiceMock = Substitute.For<IWhiteLabelConfigService>();
            _messagingMock = Substitute.For<IMessaging>();
            _eligibilityService = new EmailSendingEligibilityService(_wlConfigServiceMock, _messagingMock);

            // Setup default booking item
            _bookingItemMock = Substitute.For<IBookingItem>();
            _bookingItemMock.WhitelabelId.Returns(1);
            _bookingItemMock.IsTestBooking.Returns(false);
        }

        #region Helper Methods

        private void SetupFeatureEnabled(bool isEnabled = true)
        {
            _wlConfigServiceMock.IsFeatureEnabled(
                "SkipSendEmail", Arg.Any<Guid>(), Arg.Any<bool?>(), Arg.Any<int?>(), Arg.Any<string>(), Arg.Any<int>())
                .Returns(isEnabled);
        }

        private void SetupFeatureConfig(SkipSendEmailFeatureConfig config, string wlToken = DefaultWlToken)
        {
            _wlConfigServiceMock.GetFeatureConfigByKey<SkipSendEmailFeatureConfig>(
                wlToken, "SkipSendEmail", Arg.Any<bool?>(), Arg.Any<string>(), Arg.Any<int>())
                .Returns(config);
        }

        private SkipSendEmailFeatureConfig CreateFeatureConfig(List<SkipSendEmailTemplate> templates)
        {
            return new SkipSendEmailFeatureConfig { AllowedTemplates = templates };
        }

        private SkipSendEmailTemplate CreateTemplate(string templateName, string experimentName = "TestExperiment", bool? shouldSkip = true)
        {
            return new SkipSendEmailTemplate
            {
                TemplateName = templateName,
                ExperimentName = experimentName,
                ShouldSkip = shouldSkip
            };
        }

        private bool ExecuteTest(string wlToken = DefaultWlToken, int templateId = DefaultTemplateId, int bookingId = DefaultBookingId, IBookingItem bookingItem = null)
        {
            return _eligibilityService.ShouldSkipSendEmail(bookingItem ?? _bookingItemMock, wlToken, bookingId, templateId);
        }

        #endregion

        [TestCase(false, "HotelVoucher", "ShouldSkip=false continues email sending")]
        [TestCase(true, "DifferentTemplate", "Template not in allowed list continues email sending")]
        [Test(Description = "SkipSendEmail feature positive cases - email should be sent")]
        public void ShouldSkipSendEmail_PositiveCases_EmailShouldBeSent(bool featureEnabled, string configuredTemplateName, string testDescription)
        {
            // Arrange
            SetupFeatureEnabled(featureEnabled);
            
            if (featureEnabled)
            {
                var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
                {
                    CreateTemplate(configuredTemplateName, shouldSkip: configuredTemplateName == "HotelVoucher" ? false : true)
                });
                SetupFeatureConfig(config);
            }

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsFalse(result, $"Test case: {testDescription}");
        }

        [Test(Description = "SkipSendEmail feature disabled - email should be sent")]
        public void ShouldSkipSendEmail_FeatureDisabled_EmailShouldBeSent()
        {
            // Arrange
            SetupFeatureEnabled(false);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsFalse(result, "Feature disabled should continue email sending");
        }

        [Test(Description = "SkipSendEmail feature negative cases - email should NOT be sent")]
        public void ShouldSkipSendEmail_NegativeCases_EmailShouldNotBeSent()
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
            {
                CreateTemplate("HotelVoucher", shouldSkip: true)
            });
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsTrue(result, "ShouldSkip=true should skip email sending");
        }

        [Test(Description = "Null booking item should continue email sending")]
        public void ShouldSkipSendEmail_NullBookingItem_ReturnsFalse()
        {
            // Act
            var result = ExecuteTest(bookingItem: null);

            // Assert
            Assert.IsFalse(result, "Null booking item should not skip email sending");
        }

        [TestCase(null, "Null wlToken should not skip email sending")]
        [TestCase("", "Empty wlToken should not skip email sending")]
        [Test(Description = "Null or empty wlToken should continue email sending")]
        public void ShouldSkipSendEmail_NullOrEmptyWlToken_ReturnsFalse(string wlToken, string expectedMessage)
        {
            // Act
            var result = ExecuteTest(wlToken: wlToken);

            // Assert
            Assert.IsFalse(result, expectedMessage);
        }

        [Test(Description = "Booking item with null WhitelabelId should continue email sending")]
        public void ShouldSkipSendEmail_NullWhitelabelId_ReturnsFalse()
        {
            // Arrange
            _bookingItemMock.WhitelabelId.Returns((int?)null);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsFalse(result, "Null WhitelabelId should not skip email sending");
        }

        [Test(Description = "Constructor should throw ArgumentNullException for null parameters")]
        public void Constructor_NullParameters_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new EmailSendingEligibilityService(null, _messagingMock));
            Assert.Throws<ArgumentNullException>(() => new EmailSendingEligibilityService(_wlConfigServiceMock, null));
        }

        [Test(Description = "Null AllowedTemplates should continue email sending")]
        public void ShouldSkipSendEmail_NullAllowedTemplates_ReturnsFalse()
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = new SkipSendEmailFeatureConfig { AllowedTemplates = null };
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsFalse(result, "Null AllowedTemplates should not skip email sending");
        }

        [Test(Description = "Null SkipSendEmailFeatureConfig should continue email sending")]
        public void ShouldSkipSendEmail_NullSkipSendEmailFeatureConfig_ReturnsFalse()
        {
            // Arrange
            SetupFeatureEnabled(true);
            SetupFeatureConfig(null);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsFalse(result, "Null SkipSendEmailFeatureConfig should not skip email sending");
        }

        [Test(Description = "Null TemplateName in AllowedTemplates should not match and continue email sending")]
        public void ShouldSkipSendEmail_NullTemplateName_ReturnsFalse()
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
            {
                CreateTemplate(null), // Null template name
                CreateTemplate("") // Empty template name
            });
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsFalse(result, "Null/empty TemplateName should not match and should continue email sending");
        }

        [Test(Description = "Null ShouldSkip should default to false and continue email sending")]
        public void ShouldSkipSendEmail_NullShouldSkip_ReturnsFalse()
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
            {
                CreateTemplate("HotelVoucher", shouldSkip: null)
            });
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsFalse(result, "Null ShouldSkip should default to false and continue email sending");
        }

        [Test(Description = "Empty AllowedTemplates list should continue email sending")]
        public void ShouldSkipSendEmail_EmptyAllowedTemplates_ReturnsFalse()
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>());
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsFalse(result, "Empty AllowedTemplates should not skip email sending");
        }

        [Test(Description = "Mixed null and valid templates should only match valid ones")]
        public void ShouldSkipSendEmail_MixedNullAndValidTemplates_HandlesCorrectly()
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
            {
                CreateTemplate(null), // Should be filtered out
                CreateTemplate("HotelVoucher", shouldSkip: true), // Should match
                CreateTemplate("") // Should be filtered out
            });
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsTrue(result, "Should match the valid template and skip email sending");
        }

        [TestCase("HotelVoucher", "hotelvoucher", Description = "Case insensitive template matching - lowercase config")]
        [TestCase("HotelVoucher", "HOTELVOUCHER", Description = "Case insensitive template matching - uppercase config")]
        [TestCase("HotelVoucher", "HotelVoucher", Description = "Case insensitive template matching - exact match")]
        [TestCase("HotelVoucher", "hotelVOUCHER", Description = "Case insensitive template matching - mixed case config")]
        [Test(Description = "Template name matching should be case insensitive")]
        public void ShouldSkipSendEmail_CaseInsensitiveTemplateMatching_MatchesCorrectly(string actualTemplateName, string configTemplateName)
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
            {
                CreateTemplate(configTemplateName, shouldSkip: true)
            });
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsTrue(result, $"Template '{configTemplateName}' should match '{actualTemplateName}' (case insensitive)");
        }

        [TestCase(999999, Description = "Invalid template ID should not skip email sending")]
        [TestCase(-1, Description = "Negative template ID should not skip email sending")]
        [TestCase(0, Description = "Zero template ID should not skip email sending")]
        [Test(Description = "Invalid template IDs should continue email sending")]
        public void ShouldSkipSendEmail_InvalidTemplateId_ReturnsFalse(int templateId)
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
            {
                CreateTemplate("NonExistentTemplate", shouldSkip: true)
            });
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest(templateId: templateId);

            // Assert
            Assert.IsFalse(result, $"Invalid template ID {templateId} should not skip email sending");
        }

        [Test(Description = "Test booking scenario should work correctly")]
        public void ShouldSkipSendEmail_TestBookingScenario_WorksCorrectly()
        {
            // Arrange
            _bookingItemMock.IsTestBooking.Returns(true);
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
            {
                CreateTemplate("HotelVoucher", shouldSkip: true)
            });
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsTrue(result, "Test booking should work with SkipSendEmail feature");
            
            // Verify that the config service was called with the correct test booking flag
            _wlConfigServiceMock.Received(1).GetFeatureConfigByKey<SkipSendEmailFeatureConfig>(
                DefaultWlToken, "SkipSendEmail", true, Arg.Any<string>(), Arg.Any<int>());
        }

        [TestCase("invalid-guid", Description = "Invalid GUID format should not skip email sending")]
        [TestCase("12345678-1234-1234-1234-12345678901", Description = "Malformed GUID should not skip email sending")]
        [TestCase("not-a-guid-at-all", Description = "Non-GUID string should not skip email sending")]
        [Test(Description = "Invalid wlToken GUID format should continue email sending")]
        public void ShouldSkipSendEmail_InvalidWlTokenGuid_ReturnsFalse(string invalidWlToken)
        {
            // Act
            var result = ExecuteTest(wlToken: invalidWlToken);

            // Assert
            Assert.IsFalse(result, $"Invalid wlToken GUID '{invalidWlToken}' should not skip email sending");
        }

        [Test(Description = "Feature disabled should log appropriate message")]
        public void ShouldSkipSendEmail_FeatureDisabled_LogsInfoMessage()
        {
            // Arrange
            SetupFeatureEnabled(false);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsFalse(result);
        }

        [Test(Description = "Invalid wlToken should log appropriate message")]
        public void ShouldSkipSendEmail_InvalidWlToken_LogsInfoMessage()
        {
            // Arrange
            var invalidWlToken = "invalid-token";

            // Act
            var result = ExecuteTest(wlToken: invalidWlToken);

            // Assert
            Assert.IsFalse(result);
        }

        [Test(Description = "Multiple matching templates should take the first one")]
        public void ShouldSkipSendEmail_MultipleMatchingTemplates_TakesFirstOne()
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
            {
                CreateTemplate("HotelVoucher", "FirstExperiment", shouldSkip: true),
                CreateTemplate("HotelVoucher", "SecondExperiment", shouldSkip: false), // Should be ignored
                CreateTemplate("HotelVoucher", "ThirdExperiment", shouldSkip: true)   // Should be ignored
            });
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsTrue(result, "Should use the first matching template configuration");
        }

        [Test(Description = "Template not found in PigeonTemplateData should log and continue email sending")]
        public void ShouldSkipSendEmail_TemplateNotFoundInPigeonData_LogsAndReturnsFalse()
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
            {
                CreateTemplate("NonExistentTemplate", shouldSkip: true)
            });
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest(templateId: 999999);

            // Assert
            Assert.IsFalse(result, "Template not found should not skip email sending");
        }

        [Test(Description = "Successful template configuration should log matching details")]
        public void ShouldSkipSendEmail_SuccessfulConfiguration_LogsMatchingDetails()
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
            {
                CreateTemplate("HotelVoucher", "TestExperiment", shouldSkip: true)
            });
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsTrue(result);
        }

        [Test(Description = "No matching templates should log zero matches")]
        public void ShouldSkipSendEmail_NoMatchingTemplates_LogsZeroMatches()
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
            {
                CreateTemplate("DifferentTemplate", shouldSkip: true)
            });
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsFalse(result);
        }

        [Test(Description = "Exception in GetPigeonTemplateName should log and continue email sending")]
        public void ShouldSkipSendEmail_ExceptionInGetPigeonTemplateName_LogsAndReturnsFalse()
        {
            // This test is more theoretical since PigeonTemplateData is static,
            // but it tests the exception handling in GetPigeonTemplateName

            // Act
            var result = ExecuteTest(templateId: 999999);

            // Assert
            Assert.IsFalse(result, "Invalid template should continue work normally");
        }

        [Test(Description = "Different template types should work correctly")]
        public void ShouldSkipSendEmail_DifferentTemplateTypes_WorkCorrectly()
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
            {
                CreateTemplate("CustomerReceipt", shouldSkip: true) // Template ID 79
            });
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest(templateId: 79); // CustomerReceipt

            // Assert
            Assert.IsTrue(result, "Different template types should work correctly");
        }

        [Test(Description = "WhiteLabel config service call verification")]
        public void ShouldSkipSendEmail_ServiceCalls_VerifyCorrectParameters()
        {
            // Arrange
            SetupFeatureEnabled(true);
            var config = CreateFeatureConfig(new List<SkipSendEmailTemplate>
            {
                CreateTemplate("HotelVoucher", shouldSkip: true)
            });
            SetupFeatureConfig(config);

            // Act
            var result = ExecuteTest();

            // Assert
            Assert.IsTrue(result);
            
            // Verify IsFeatureEnabled was called with correct parameters
            _wlConfigServiceMock.Received(1).IsFeatureEnabled(
                "SkipSendEmail", 
                Arg.Is<Guid>(g => g.ToString() == DefaultWlToken), 
                false, 
                Arg.Any<int?>(),
                Arg.Any<string>(),
                Arg.Any<int>());
            
            // Verify GetFeatureConfigByKey was called with correct parameters
            _wlConfigServiceMock.Received(1).GetFeatureConfigByKey<SkipSendEmailFeatureConfig>(
                DefaultWlToken, 
                "SkipSendEmail", 
                false,
                Arg.Any<string>(),
                Arg.Any<int>());
        }
        
    }
} 