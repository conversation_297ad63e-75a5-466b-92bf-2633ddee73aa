using System;
using System.Collections.Generic;
using Agoda.Config.Consul.KeyValue.FeatureSwitch.Services;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.EBE.Agents.Email.DataAccess.Interface;
using Agoda.EBE.Framework.Messaging;
using NSubstitute;
using NUnit.Framework;
using Agoda.EBE.Agents.Email.Object;
using Agoda.EBE.Agents.Email.Object.Interface;
using Agoda.EBE.Agents.Email.Service;
using Agoda.EBE.Agents.Email.Service.Helpers.Interface;
using Agoda.EBE.Agents.Email.Service.Interface;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess.Interface;
using Agoda.EBE.Framework;
using Agoda.EBE.Framework.EmailContent.Objects;
using Agoda.EBE.Framework.EmailContent.Objects.Interface;
using Agoda.EBE.Framework.MQ.Interface;
using Agoda.EBE.Framework.MQ.MessageType;
using Agoda.Rewards.Api.Definition;
using Shouldly;
using CommonEnum = Agoda.EBE.Agents.Common.Enum;

namespace Agoda.EBE.Agents.Email.UnitTest.Processor.EmailDataProcessor
{
    [TestFixture]
    [Category("Email_Agent")]
    public class SendEmailFailTest
    {
        private IEBEExceptionMessage _exceptionMock;
        private IDatabaseAccess _dataAccessMock;
        private IHermesHelper _hermesHelperMock;
        private IPigeonHelper _pigeonHelperMock;
        private EmailService _emailServiceMock;
        private IWorkflowService _workflowServiceMock;
        private IMessaging _messagingMock;
        private IRabbitMQAdapter _rabbitMqAdapter;
        private IEmailConfiguration _configurationMock;
        private IEBEMeasurementMessage _measurementMessage;
        private IDeliveryApiService _deliveryApiServiceMock;
        private ICommunicationApiService _communicationApiServiceMock;
        private IBookingContactFromContactsApi _bookingContactMock;
        private IWhiteLabelHelperService _wlHelperService;
        private IWhiteLabelConfigService _wlConfigService;
        private IFeatureSwitchService _featureSwitchService;
        private IEmailSendingEligibilityService _emailSendingEligibilityService;

        private IBookingItem _bookingItemMock;
        private IEmailDetailInfo _emailDetailInfoMock;

        private Exception _exceptionInEmailService;

        private Email.Processor.EmailDataProcessor _dataProcessor;

        [SetUp]
        public void SetUp()
        {
            _exceptionMock = Substitute.For<IEBEExceptionMessage>();
            _messagingMock = Substitute.For<IMessaging>();
            _messagingMock.ExceptionMessage.Returns(_exceptionMock);
            _measurementMessage = new MeasurementMessage("matric", "component");
            _messagingMock.MeasurementMessageFactory.CreateNewMeasurement().Returns(_measurementMessage);
            _deliveryApiServiceMock = Substitute.For<IDeliveryApiService>();
            _communicationApiServiceMock = Substitute.For<ICommunicationApiService>();
            _dataAccessMock = Substitute.For<IDatabaseAccess>();
            _hermesHelperMock = Substitute.For<IHermesHelper>();
            _pigeonHelperMock = Substitute.For<IPigeonHelper>();
            _wlHelperService = Substitute.For<IWhiteLabelHelperService>();
            _wlConfigService = Substitute.For<IWhiteLabelConfigService>();
            _featureSwitchService = Substitute.For<IFeatureSwitchService>();
            _emailSendingEligibilityService = Substitute.For<IEmailSendingEligibilityService>();
            _workflowServiceMock = Substitute.For<IWorkflowService>();
            _rabbitMqAdapter = Substitute.For<IRabbitMQAdapter>();
            _configurationMock = Substitute.For<IEmailConfiguration>();
            
            _emailServiceMock = Substitute.ForPartsOf<EmailService>(_configurationMock, _dataAccessMock,
                _messagingMock, _deliveryApiServiceMock, _communicationApiServiceMock, _pigeonHelperMock, _wlHelperService, _wlConfigService, _featureSwitchService, _emailSendingEligibilityService);

            _dataProcessor = Substitute.ForPartsOf<Email.Processor.EmailDataProcessor>(_dataAccessMock,
                _hermesHelperMock, _emailServiceMock, _workflowServiceMock, _messagingMock, _rabbitMqAdapter,
                _configurationMock, _wlConfigService);

            _bookingItemMock = new BookingItem();
            _emailDetailInfoMock = new EmailDetailInfo
            {
                BookingId = 12345678,
                DmcType = 1,
                DmcId = (int) CommonEnum.DmcId.YCS,
                ContactMethodId = 1,
                WorkflowParameter = new WorkflowParameter
                {
                    ModuleId = 1,
                    TemplateId = 1,
                    Priority = 1,
                    EmailFormat = 1,
                },
                CusCoTemplateId = 1,
                LanguageId = 1,
                MailTemplateTo = ConstantEnum.MailTemplateTo.Customer
            };
            _dataAccessMock.GetBookingInfo(12345678).Returns(_bookingItemMock);
            _dataAccessMock.GetEmailDetailInfo(_bookingItemMock).Returns(_emailDetailInfoMock);

            _dataProcessor.When(_ => _.Validate(Arg.Any<IDefaultMessage>(), Arg.Any<IBookingItem>())).DoNotCallBase();
            _dataProcessor.Validate(Arg.Any<IDefaultMessage>(), Arg.Any<IBookingItem>()).Returns(true);
            _dataProcessor.When(_ => _.IsNeedSendProcessTask(Arg.Any<IBookingItem>())).DoNotCallBase();
            _dataProcessor.IsNeedSendProcessTask(Arg.Any<IBookingItem>()).Returns(true);

            _bookingContactMock = Substitute.For<IBookingContactFromContactsApi>();
            _bookingContactMock.PaxContact.Returns(new PaxContact {LoginTypes = new List<LoginType> {LoginType.Basic}});
            _dataAccessMock.GetBookingContact(Arg.Any<int>(), Arg.Any<int>(),Arg.Any<int?>(), Arg.Any<int>(),
                Arg.Any<ConstantEnum.MailTemplateTo>(), Arg.Any<int?>()).Returns(_bookingContactMock);
            _wlHelperService.ResolveWhiteLabelId(Arg.Any<int>(), Arg.Any<int?>()).Returns((int) CommonEnum.WhitelabelId.Agoda);
        }

        [Test]
        public void Process_HermesNeverSucceedBefore_HermesSucceed_SendEmailFail_ExceptionThrownWithHermesRemarks()
        {
            var message = new DefaultMessage
            {
                BookingId = 12345678,
                AdditionalData = null,
                WorkflowStateId = 250
            };

            const string remarkText = "this is remark text";
            _hermesHelperMock.SendHermesAndGetRemark(_emailDetailInfoMock, false, message.WorkflowStateId, _bookingItemMock)
                .Returns(remarkText);

            //Make SendEmail Fail.
            _exceptionInEmailService = new Exception("something went wrong inside EmailService");
            var whenCallInnerProcessSendingEmail = _emailServiceMock.When(_ => _.ProcessSendingEmail(message.BookingId,
                1, (int) CommonEnum.DmcId.YCS, 1, 1, 1, 1, 1, 1, ConstantEnum.MailTemplateTo.Customer,
                _bookingContactMock));
            whenCallInnerProcessSendingEmail.DoNotCallBase();
            whenCallInnerProcessSendingEmail.Throw(_exceptionInEmailService);

            var ex = Assert.Throws<SendEmailFailException>(() => _dataProcessor.Process(message));

            ex.HermesRemark.ShouldBe(remarkText);
            ex.InnerException.ShouldBe(_exceptionInEmailService);
            _hermesHelperMock.Received(1).SendHermesAndGetRemark(_emailDetailInfoMock, false, message.WorkflowStateId, _bookingItemMock);
            _emailServiceMock.Received(1).ProcessSendingEmail(_emailDetailInfoMock, _bookingContactMock, remarkText);
        }

        [Test]
        public void Process_RetryMessage_HermesAlreadySuccess_ShouldNotCallHermesAgain()
        {
            var remarkBeforeRetryIter = "send email success before retry";

            var message = new DefaultMessage
            {
                BookingId = 12345678,
                AdditionalData = new Dictionary<string, string>
                {
                    {Constants.IsAlreadySendHermesMessageKey, "true"},
                    {Constants.HermesRemarkKey, remarkBeforeRetryIter}
                },
                WorkflowStateId = 250
            };

            //Make SendEmail Success.
            _emailServiceMock.When(_ =>
                    _.ProcessSendingEmail(Arg.Any<EmailDetailInfo>(), Arg.Any<IBookingContactFromContactsApi>(), Arg.Any<string>()))
                .DoNotCallBase();
            var emailGuid = new Guid("fd609ba4-cb35-4bff-a1a5-9891c7de9d88");
            _emailServiceMock
                .ProcessSendingEmail(Arg.Any<EmailDetailInfo>(), Arg.Any<IBookingContactFromContactsApi>(), Arg.Any<string>()).Returns(
                    new ProcessSendingEmailResponse(emailGuid, Enum.EmailActionResult.Success, false, string.Empty));

            _dataProcessor.Process(message);

            _hermesHelperMock.DidNotReceive()
                .SendHermesAndGetRemark(_emailDetailInfoMock, false, message.WorkflowStateId, _bookingItemMock);
            _emailServiceMock.Received(1)
                .ProcessSendingEmail(_emailDetailInfoMock, _bookingContactMock, remarkBeforeRetryIter);

            _workflowServiceMock.Received(1).ChangeStateAndUpdateItinerary(_emailDetailInfoMock,
                Enum.EmailActionResult.Success, Arg.Is<string>(s => s.Contains(remarkBeforeRetryIter)), emailGuid,
                false);
        }
    }
}