using System;
using System.Collections.Generic;
using Agoda.Config.Consul.KeyValue.FeatureSwitch.Services;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.EBE.Agents.Email.DataAccess.Interface;
using Agoda.EBE.Agents.Email.Object;
using Agoda.EBE.Agents.Email.Object.Interface;
using Agoda.EBE.Agents.Email.Service;
using Agoda.EBE.Agents.Email.Service.Helpers.Interface;
using Agoda.EBE.Agents.Email.Service.Interface;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess.Interface;
using Agoda.EBE.Agents.Provisioning.Object;
using Agoda.EBE.Framework.EmailContent.Objects;
using Agoda.EBE.Framework.EmailContent.Objects.Interface;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.MQ.Interface;
using Agoda.EBE.Framework.MQ.MessageType;
using Agoda.EBE.Framework.TestUtils;
using Agoda.EBE.Framework.TestUtils.Objects;
using Agoda.Rewards.Api.Definition;
using NSubstitute;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Email.UnitTest.Processor.EmailDataProcessor
{
    [TestFixture]
    [Category("Email_Agent")]
    public class SendAcrEmailTest
    {
        private IEBEExceptionMessage _exceptionMock;
        private IDatabaseAccess _dataAccessMock;
        private IHermesHelper _hermesHelperMock;
        private IPigeonHelper _pigeonHelperMock;
        private EmailService _emailServiceMock;
        private IWorkflowService _workflowServiceMock;
        private IMessaging _messagingMock;
        private IRabbitMQAdapter _rabbitMqAdapter;
        private IEmailConfiguration _configurationMock;
        private IEBEMeasurementMessage _measurementMessage;
        private IDeliveryApiService _deliveryApiServiceMock;
        private IWhiteLabelHelperService _wlhelperService;
        private IWhiteLabelConfigService _wlConfigService;
        private IFeatureSwitchService _featureSwitchService;
        private ICommunicationApiService _communicationApiServiceMock;
        private IEmailSendingEligibilityService _emailSendingEligibilityService;

        private IEmailDetailInfo _emailDetailInfoMock;
        private Email.Processor.EmailDataProcessor _dataProcessor;
        private IBookingContactFromContactsApi _bookingContactMock;

        [SetUp]
        public void SetUp()
        {
            _exceptionMock = Substitute.For<IEBEExceptionMessage>();
            _messagingMock = Substitute.For<IMessaging>();
            _messagingMock.ExceptionMessage.Returns(_exceptionMock);
            _measurementMessage = new MeasurementMessage("matric", "component");
            _messagingMock.MeasurementMessageFactory.CreateNewMeasurement().Returns(_measurementMessage);
            _deliveryApiServiceMock = Substitute.For<IDeliveryApiService>();
            _communicationApiServiceMock = Substitute.For<ICommunicationApiService>();
            _dataAccessMock = Substitute.For<IDatabaseAccess>();
            _hermesHelperMock = Substitute.For<IHermesHelper>();
            _pigeonHelperMock = Substitute.For<IPigeonHelper>();
            _wlhelperService = Substitute.For<IWhiteLabelHelperService>();
            _workflowServiceMock = Substitute.For<IWorkflowService>();
            _rabbitMqAdapter = Substitute.For<IRabbitMQAdapter>();
            _configurationMock = Substitute.For<IEmailConfiguration>();
            _wlConfigService = Substitute.For<IWhiteLabelConfigService>();
            _featureSwitchService = Substitute.For<IFeatureSwitchService>();
            _emailSendingEligibilityService = Substitute.For<IEmailSendingEligibilityService>();
            
            _emailServiceMock = Substitute.ForPartsOf<EmailService>(_configurationMock, _dataAccessMock,
                _messagingMock, _deliveryApiServiceMock, _communicationApiServiceMock, _pigeonHelperMock, _wlhelperService, _wlConfigService, _featureSwitchService, _emailSendingEligibilityService);
            
            _dataProcessor = Substitute.ForPartsOf<Email.Processor.EmailDataProcessor>(_dataAccessMock,
                _hermesHelperMock, _emailServiceMock, _workflowServiceMock, _messagingMock, _rabbitMqAdapter,
                _configurationMock, _wlConfigService);

            var rowDataFromDb = new List<DataPropertyObject>
            {
                new DataPropertyObject("booking_id", 12345678, typeof(int)),
                new DataPropertyObject("workflow_state_id", 852, typeof(int)),
                new DataPropertyObject("workflow_action_id", 2, typeof(int)),
                new DataPropertyObject("ss_id_fb", DBNull.Value, typeof(int)), //int32
                new DataPropertyObject("language_id", 1, typeof(int)),
                new DataPropertyObject("workflow_parameter_value",
                    "<Parameter><ModuleID>2</ModuleID><TemplateID>34</TemplateID><Priority>1</Priority><EmailFormat>1</EmailFormat></Parameter>", typeof(string)),
                new DataPropertyObject("dmc_code", "YCS", typeof(string)),
                new DataPropertyObject("dmc_id", 332, typeof(int)),
                new DataPropertyObject("dmc_type", 2, typeof(int)),
                new DataPropertyObject("booking_date_from", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("booking_date_until", "2012-04-08 00:00:00", typeof(DateTime)),
                new DataPropertyObject("other_special_needs", "Good Sea View", typeof(string)),
                new DataPropertyObject("is_multiple_bookings", "false", typeof(bool)),
                new DataPropertyObject("is_fraud_verify_sent", "false", typeof(bool)),
                new DataPropertyObject("itinerary_id", 293, typeof(int)),
                new DataPropertyObject("cid_list", 1234, typeof(int)),
                new DataPropertyObject("payment_model", 1, typeof(int)),
                new DataPropertyObject("cancellation_info",
                    string.Empty, typeof(string)),
                new DataPropertyObject("request_type", 14, typeof(int)),
                new DataPropertyObject("amendment_id", DBNull.Value, typeof(int)),
                new DataPropertyObject("is_skip_customer_cxl_confirm_email", "false", typeof(bool)),
                new DataPropertyObject("whitelabel_id", 1, typeof(int)),
                new DataPropertyObject("dmc_due_date", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("origin", "HK", typeof(string)),
                new DataPropertyObject("device_type_id", 0, typeof(string)),
                new DataPropertyObject("tracking_cookie_id", "457c86dd-3046-4759-a924-8c1c7b2cb0be", typeof(string)),
                new DataPropertyObject("pricefreeze_booking_id", 12345678, typeof(int)),
                new DataPropertyObject("is_test_booking", false, typeof(bool))
            };
            var dataRow = DataRowUtil.FromDataRowObject(rowDataFromDb);
            var bookingItem = new BookingItem(dataRow);

            _emailDetailInfoMock = new EmailDetailInfo
            {
                BookingId = 12345678,
                DmcType = 2,
                DmcId = 332,
                ContactMethodId = 1,
                WorkflowParameter = new WorkflowParameter
                {
                    ModuleId = 2,
                    TemplateId = 34,
                    Priority = 1,
                    EmailFormat = 1,
                },
                CusCoTemplateId = 1,
                LanguageId = 1,
                MailTemplateTo = Agoda.EBE.Framework.ConstantEnum.MailTemplateTo.Hotel
            };
            
            _dataAccessMock.GetBookingInfo(12345678).Returns(bookingItem);
            _dataAccessMock.IsNeedSendCustomerEmail(Arg.Any<int>()).Returns(false);
            _dataAccessMock.GetEmailDetailInfo(bookingItem).Returns(_emailDetailInfoMock);

            var resellBookingItem = new BookingResell
            {
                BookingId = 12345678,
                ResellBookingId = 456678391,
                ResellStatus = Provisioning.Enum.EBEBookingResellStatus.ConfirmedV2
            };
            _dataAccessMock.GetBookingResellByBookingId(12345678).Returns(resellBookingItem);
            _dataProcessor.When(_ => _.Validate(Arg.Any<IDefaultMessage>(), Arg.Any<IBookingItem>())).DoNotCallBase();
            _dataProcessor.Validate(Arg.Any<IDefaultMessage>(), Arg.Any<IBookingItem>()).Returns(true);
            
            _bookingContactMock = Substitute.For<IBookingContactFromContactsApi>();
            _bookingContactMock.PaxContact.Returns(new PaxContact { LoginTypes = new List<LoginType> { LoginType.Basic } });
            _dataAccessMock.GetBookingContact(Arg.Any<int>(), Arg.Any<int>(),Arg.Any<int?>(), Arg.Any<int>(),
                Arg.Any<Agoda.EBE.Framework.ConstantEnum.MailTemplateTo>(), Arg.Any<int?>()).Returns(_bookingContactMock);
           
        }

        [Test]
        public void Process_IsResellBooking_And_SendCancellationVoucherToHotel_ChangeState_With_ActionResultNotSent()
        {
            var message = new DefaultMessage
            {
                BookingId = 12345678,
                AdditionalData = null
            };
            _configurationMock.EnablePayHotelDMCForSendingHotelVoucher.Returns(new List<int> { 332, 29004 });
            _configurationMock.EmailHotelVoucherState.Returns(new List<int> { 201, 218, 251, 291, 296, 314, 401, 661, 752, 753, 811, 832, 834, 841, 852, 861, 872 });
            _configurationMock.EmailVerifyState.Returns(new List<int> { 111 });
            _configurationMock.EmailConfirmationState.Returns(new List<int> { 111 });
            _configurationMock.CancellationHotelVoucherState.Returns(new List<int> { });
            
            _emailServiceMock.When(_ => _.ProcessSendingEmail(Arg.Any<EmailDetailInfo>(), Arg.Any<IBookingContactFromContactsApi>(), Arg.Any<string>()))
                .DoNotCallBase();
            var emailGuid = new Guid("fd609ba4-cb35-4bff-a1a5-9891c7de9d88");
            _emailServiceMock.ProcessSendingEmail(Arg.Any<EmailDetailInfo>(), Arg.Any<IBookingContactFromContactsApi>(), Arg.Any<string>()).Returns(
                new ProcessSendingEmailResponse(emailGuid, Enum.EmailActionResult.Success, false, string.Empty));

            _dataProcessor.Process(message);
            _workflowServiceMock.Received(1).ChangeStateAndUpdateItinerary(_emailDetailInfoMock,
                Enum.EmailActionResult.NotSent, Arg.Any<String>(), emailGuid,
                false);
        }

       
    }
}