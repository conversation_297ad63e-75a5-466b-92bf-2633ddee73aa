using System;
using System.Collections.Generic;
using Agoda.Config.Consul.KeyValue.FeatureSwitch.Services;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.EBE.Agents.Email.DataAccess.Interface;
using Agoda.EBE.Agents.Email.Object;
using Agoda.EBE.Agents.Email.Object.Interface;
using Agoda.EBE.Agents.Email.Service;
using Agoda.EBE.Agents.Email.Service.Helpers.Interface;
using Agoda.EBE.Agents.Email.Service.Interface;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess.Interface;
using Agoda.EBE.Agents.Provisioning.Object;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.MQ.Interface;
using Agoda.EBE.Framework.MQ.MessageType;
using Agoda.EBE.Framework.TestUtils;
using Agoda.EBE.Framework.TestUtils.Objects;
using Agoda.WhiteLabelApi.Client.Models;
using NSubstitute;
using NUnit.Framework;

namespace Agoda.EBE.Agents.Email.UnitTest.Processor.EmailDataProcessor
{
    [TestFixture]
    [Category("Email_Agent")]
    public class SendCustomerVoucherTest
    {
        private IEBEExceptionMessage _exceptionMock;
        private IDatabaseAccess _dataAccessMock;
        private IHermesHelper _hermesHelperMock;
        private IPigeonHelper _pigeonHelperMock;
        private EmailService _emailServiceMock;
        private IWorkflowService _workflowServiceMock;
        private IMessaging _messagingMock;
        private IRabbitMQAdapter _rabbitMqAdapter;
        private IEmailConfiguration _configurationMock;
        private IEBEMeasurementMessage _measurementMessage;
        private IDeliveryApiService _deliveryApiServiceMock;
        private IWhiteLabelHelperService _wlhelperService;
        private IWhiteLabelConfigService _wlConfigService;
        private IFeatureSwitchService _featureSwitchService;
        private ICommunicationApiService _communicationApiServiceMock;
        private IEmailSendingEligibilityService _emailSendingEligibilityService;

        private IEmailDetailInfo _emailDetailInfoMock;

        private Exception _exceptionInEmailService;

        private Email.Processor.EmailDataProcessor _dataProcessor;

        [SetUp]
        public void SetUp()
        {
            _exceptionMock = Substitute.For<IEBEExceptionMessage>();
            _messagingMock = Substitute.For<IMessaging>();
            _messagingMock.ExceptionMessage.Returns(_exceptionMock);
            _measurementMessage = new MeasurementMessage("matric", "component");
            _messagingMock.MeasurementMessageFactory.CreateNewMeasurement().Returns(_measurementMessage);
            _deliveryApiServiceMock = Substitute.For<IDeliveryApiService>();
            _communicationApiServiceMock = Substitute.For<ICommunicationApiService>();
            _dataAccessMock = Substitute.For<IDatabaseAccess>();
            _hermesHelperMock = Substitute.For<IHermesHelper>();
            _pigeonHelperMock = Substitute.For<IPigeonHelper>();
            _wlhelperService = Substitute.For<IWhiteLabelHelperService>();
            _wlConfigService = Substitute.For<IWhiteLabelConfigService>();
            _featureSwitchService = Substitute.For<IFeatureSwitchService>();
            _emailSendingEligibilityService = Substitute.For<IEmailSendingEligibilityService>();
            _workflowServiceMock = Substitute.For<IWorkflowService>();
            _rabbitMqAdapter = Substitute.For<IRabbitMQAdapter>();
            _configurationMock = Substitute.For<IEmailConfiguration>();
            
            _emailServiceMock = Substitute.ForPartsOf<EmailService>(_configurationMock, _dataAccessMock,
                _messagingMock, _deliveryApiServiceMock, _communicationApiServiceMock, _pigeonHelperMock,_wlhelperService, _wlConfigService, _featureSwitchService, _emailSendingEligibilityService);

            _dataProcessor = Substitute.ForPartsOf<Email.Processor.EmailDataProcessor>(_dataAccessMock,
                _hermesHelperMock, _emailServiceMock, _workflowServiceMock, _messagingMock, _rabbitMqAdapter,
                _configurationMock, _wlConfigService);

            var rowDataFromDb = new List<DataPropertyObject>
            {
                new DataPropertyObject("booking_id", 12345678, typeof(int)),
                new DataPropertyObject("workflow_state_id", 111, typeof(int)),
                new DataPropertyObject("workflow_action_id", 22, typeof(int)),
                new DataPropertyObject("ss_id_fb", DBNull.Value, typeof(int)), //int32
                new DataPropertyObject("language_id", 1, typeof(int)),
                new DataPropertyObject("workflow_parameter_value",
                    "<Parameter><ModuleID>2</ModuleID><TemplateID>88</TemplateID><Priority>1</Priority><EmailFormat>2</EmailFormat><NotesTypeID>2</NotesTypeID></Parameter>", typeof(string)),
                new DataPropertyObject("dmc_code", "YCS", typeof(string)),
                new DataPropertyObject("dmc_id", 332, typeof(int)),
                new DataPropertyObject("dmc_type", 2, typeof(int)),
                new DataPropertyObject("booking_date_from", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("booking_date_until", "2012-04-08 00:00:00", typeof(DateTime)),
                new DataPropertyObject("other_special_needs", "Good Sea View", typeof(string)),
                new DataPropertyObject("is_multiple_bookings", "false", typeof(bool)),
                new DataPropertyObject("is_fraud_verify_sent", "false", typeof(bool)),
                new DataPropertyObject("itinerary_id", 293, typeof(int)),
                new DataPropertyObject("cid_list", 1234, typeof(int)),
                new DataPropertyObject("payment_model", 1, typeof(int)),
                new DataPropertyObject("cancellation_info",
                    string.Empty, typeof(string)),
                new DataPropertyObject("request_type", 14, typeof(int)),
                new DataPropertyObject("amendment_id", 123456, typeof(int)),
                new DataPropertyObject("is_skip_customer_cxl_confirm_email", "false", typeof(bool)),
                new DataPropertyObject("whitelabel_id", 1, typeof(int)),
                new DataPropertyObject("dmc_due_date", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("origin", "HK", typeof(string)),
                new DataPropertyObject("device_type_id", 0, typeof(string)),
                new DataPropertyObject("tracking_cookie_id", "457c86dd-3046-4759-a924-8c1c7b2cb0be", typeof(string)),
                new DataPropertyObject("pricefreeze_booking_id", 12345678, typeof(int)),
                new DataPropertyObject("is_test_booking", false, typeof(bool))
            };
            var dataRow = DataRowUtil.FromDataRowObject(rowDataFromDb);
            var bookingItem = new BookingItem(dataRow);

            _emailDetailInfoMock = new EmailDetailInfo
            {
                BookingId = 12345678,
                DmcType = 1,
                DmcId = (int)Agoda.EBE.Agents.Common.Enum.DmcId.YCS,
                ContactMethodId = 1,
                WorkflowParameter = new WorkflowParameter
                {
                    ModuleId = 1,
                    TemplateId = 1,
                    Priority = 1,
                    EmailFormat = 1,
                },
                CusCoTemplateId = 1,
                LanguageId = 1,
                MailTemplateTo = Agoda.EBE.Framework.ConstantEnum.MailTemplateTo.Customer
            };
            _dataAccessMock.GetBookingInfo(12345678).Returns(bookingItem);
            _dataAccessMock.IsNeedSendCustomerEmail(Arg.Any<int>()).Returns(false);
            _dataAccessMock.GetEmailDetailInfo(bookingItem).Returns(_emailDetailInfoMock);

            _dataProcessor.When(_ => _.Validate(Arg.Any<IDefaultMessage>(), Arg.Any<IBookingItem>())).DoNotCallBase();
            _dataProcessor.Validate(Arg.Any<IDefaultMessage>(), Arg.Any<IBookingItem>()).Returns(true);
            //_dataProcessor.When(_ => _.IsNeedSendProcessTask(Arg.Any<IBookingItem>())).DoNotCallBase();
            //_dataProcessor.IsNeedSendProcessTask(Arg.Any<IBookingItem>()).Returns(true);

            //_bookingContactMock = Substitute.For<IBookingContact>();
            //_bookingContactMock.PaxContact.Returns(new PaxContact {LoginTypes = new List<LoginType>{LoginType.Basic}});
            //_dataAccessMock.GetBookingContact(Arg.Any<int>(), Arg.Any<int?>(), Arg.Any<int>(),
            //Arg.Any<Agoda.EBE.Framework.ConstantEnum.MailTemplateTo>()).Returns(_bookingContactMock);
        }

        [Test]
        public void Process_IsConfirmationState_NotSendEmail_SkippBookingByKickToNextState()
        {
            var message = new DefaultMessage
            {
                BookingId = 12345678,
                AdditionalData = null
            };
            _configurationMock.EnablePayHotelDMCForSendingHotelVoucher.Returns(new List<int> { 332, 29004 });
            _configurationMock.EmailHotelVoucherState.Returns(new List<int> { 201, 218, 251, 291, 296, 314, 401, 661, 752, 753, 811, 832, 834, 841, 852, 861, 872 });
            _configurationMock.EmailVerifyState.Returns(new List<int> { 111 });
            _configurationMock.EmailConfirmationState.Returns(new List<int> { 111 });
            _configurationMock.CancellationHotelVoucherState.Returns(new List<int> { });
            //Make SendEmail Fail.
            _dataProcessor.Process(message);
            _workflowServiceMock.Received(1).ChangeStateWithWorkflowActionResult3(Arg.Is<int>((bid) => bid == 12345678), Arg.Is<String>((s) => s.Contains("booking is in confirmation state")));

        }

        [TestCase(Enum.WorkflowStateId.SendingAmendConfirmationToCustomer)]
        [TestCase(Enum.WorkflowStateId.SendingAmendConfirmationToCustomerBnpl)]
        public void ProcessAmendment_ForARIncorrectRate_ShouldNotSendCustomerVoucher(Enum.WorkflowStateId sendAmendEmailState)
        {
            var rowDataFromDb = new List<DataPropertyObject>
            {
                new DataPropertyObject("booking_id", 2345678, typeof(int)),
                new DataPropertyObject("workflow_state_id", (int)sendAmendEmailState, typeof(int)),
                new DataPropertyObject("workflow_action_id", 22, typeof(int)),
                new DataPropertyObject("ss_id_fb", DBNull.Value, typeof(int)), //int32
                new DataPropertyObject("language_id", 1, typeof(int)),
                new DataPropertyObject("workflow_parameter_value",
                    "<Parameter><ModuleID>2</ModuleID><TemplateID>88</TemplateID><Priority>1</Priority><EmailFormat>2</EmailFormat><NotesTypeID>2</NotesTypeID></Parameter>", typeof(string)),
                new DataPropertyObject("dmc_code", "YCS", typeof(string)),
                new DataPropertyObject("dmc_id", 332, typeof(int)),
                new DataPropertyObject("dmc_type", 2, typeof(int)),
                new DataPropertyObject("booking_date_from", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("booking_date_until", "2012-04-08 00:00:00", typeof(DateTime)),
                new DataPropertyObject("other_special_needs", "Good Sea View", typeof(string)),
                new DataPropertyObject("is_multiple_bookings", "false", typeof(bool)),
                new DataPropertyObject("is_fraud_verify_sent", "false", typeof(bool)),
                new DataPropertyObject("itinerary_id", 293, typeof(int)),
                new DataPropertyObject("cid_list", 1234, typeof(int)),
                new DataPropertyObject("payment_model", 1, typeof(int)),
                new DataPropertyObject("cancellation_info",
                    string.Empty, typeof(string)),
                new DataPropertyObject("request_type", 14, typeof(int)),
                new DataPropertyObject("amendment_id", 123456, typeof(int)),
                new DataPropertyObject("is_skip_customer_cxl_confirm_email", "false", typeof(bool)),
                new DataPropertyObject("whitelabel_id", 1, typeof(int)),
                new DataPropertyObject("dmc_due_date", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("origin", "HK", typeof(string)),
                new DataPropertyObject("device_type_id", 0, typeof(string)),
                new DataPropertyObject("tracking_cookie_id", "457c86dd-3046-4759-a924-8c1c7b2cb0be", typeof(string)),
                new DataPropertyObject("pricefreeze_booking_id", 12345678, typeof(int)),
                new DataPropertyObject("is_test_booking", false, typeof(bool))
            };
            var dataRow = DataRowUtil.FromDataRowObject(rowDataFromDb);
            var bookingItem = new BookingItem(dataRow);

            _dataAccessMock.GetBookingInfo(2345678).Returns(bookingItem);
            _configurationMock.EnablePayHotelDMCForSendingHotelVoucher.Returns(new List<int> { 332, 29004 });
            _configurationMock.EmailHotelVoucherState.Returns(new List<int> { 201, 218, 251, 291, 296, 314, 401, 661, 752, 753, 811, 832, 834, 841, 852, 861, 872 });
            _configurationMock.EmailVerifyState.Returns(new List<int> { 111 });
            _configurationMock.EmailConfirmationState.Returns(new List<int> { 111 });
            _configurationMock.CancellationHotelVoucherState.Returns(new List<int> { });


            var message = new DefaultMessage
            {
                BookingId = 2345678,
                AdditionalData = null
            };

            _dataProcessor.Process(message);
            _dataAccessMock.DidNotReceive().GetEmailDetailInfo(Arg.Any<BookingItem>());
            _workflowServiceMock.Received().ChangeStateWithWorkflowActionResult2(2345678, Arg.Any<string>());

        }

        [TestCase(Enum.WorkflowStateId.SendingCxlConfirmationToCustomer1)]
        [TestCase(Enum.WorkflowStateId.SendingCxlConfirmationToCustomer2)]
        public void ProcessCancellation_WithFlagIsSkipCustomerCxlConfirmEmail_ShouldNotSendCxlConfirmEmailToCustomer(Enum.WorkflowStateId sendAmendEmailState)
        {
            var rowDataFromDb = new List<DataPropertyObject>
            {
                new DataPropertyObject("booking_id", 2345678, typeof(int)),
                new DataPropertyObject("workflow_state_id", (int)sendAmendEmailState, typeof(int)),
                new DataPropertyObject("workflow_action_id", 22, typeof(int)),
                new DataPropertyObject("ss_id_fb", DBNull.Value, typeof(int)), //int32
                new DataPropertyObject("language_id", 1, typeof(int)),
                new DataPropertyObject("workflow_parameter_value",
                    "<Parameter><ModuleID>2</ModuleID><TemplateID>88</TemplateID><Priority>1</Priority><EmailFormat>2</EmailFormat><NotesTypeID>2</NotesTypeID></Parameter>", typeof(string)),
                new DataPropertyObject("dmc_code", "YCS", typeof(string)),
                new DataPropertyObject("dmc_id", 332, typeof(int)),
                new DataPropertyObject("dmc_type", 2, typeof(int)),
                new DataPropertyObject("booking_date_from", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("booking_date_until", "2012-04-08 00:00:00", typeof(DateTime)),
                new DataPropertyObject("other_special_needs", "Good Sea View", typeof(string)),
                new DataPropertyObject("is_multiple_bookings", "false", typeof(bool)),
                new DataPropertyObject("is_fraud_verify_sent", "false", typeof(bool)),
                new DataPropertyObject("itinerary_id", 293, typeof(int)),
                new DataPropertyObject("cid_list", 1234, typeof(int)),
                new DataPropertyObject("payment_model", 1, typeof(int)),
                new DataPropertyObject("cancellation_info",
                    string.Empty, typeof(string)),
                new DataPropertyObject("request_type", 14, typeof(int)),
                new DataPropertyObject("amendment_id", 123456, typeof(int)),
                new DataPropertyObject("is_skip_customer_cxl_confirm_email", "true", typeof(bool)),
                new DataPropertyObject("whitelabel_id", 1, typeof(int)),
                new DataPropertyObject("dmc_due_date", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("origin", "HK", typeof(string)),
                new DataPropertyObject("device_type_id", 0, typeof(string)),
                new DataPropertyObject("tracking_cookie_id", "457c86dd-3046-4759-a924-8c1c7b2cb0be", typeof(string)),
                new DataPropertyObject("pricefreeze_booking_id", 12345678, typeof(int)),
                new DataPropertyObject("is_test_booking", false, typeof(bool))
            };
            var dataRow = DataRowUtil.FromDataRowObject(rowDataFromDb);
            var bookingItem = new BookingItem(dataRow);

            _dataAccessMock.GetBookingInfo(2345678).Returns(bookingItem);
            _configurationMock.EnablePayHotelDMCForSendingHotelVoucher.Returns(new List<int> { 332, 29004 });
            _configurationMock.EmailHotelVoucherState.Returns(new List<int> { 201, 218, 251, 291, 296, 314, 401, 661, 752, 753, 811, 832, 834, 841, 852, 861, 872 });
            _configurationMock.EmailVerifyState.Returns(new List<int> { 111 });
            _configurationMock.EmailConfirmationState.Returns(new List<int> { 111 });
            _configurationMock.CancellationHotelVoucherState.Returns(new List<int> { });


            var message = new DefaultMessage
            {
                BookingId = 2345678,
                AdditionalData = null
            };

            _dataProcessor.Process(message);
            _dataAccessMock.DidNotReceive().GetEmailDetailInfo(Arg.Any<BookingItem>());
            _workflowServiceMock.Received().ChangeStateWithWorkflowActionResultSkipped(2345678, Arg.Any<string>());
        }
        
        [TestCase(Enum.WorkflowStateId.SendingCxlConfirmationToCustomer1)]
        [TestCase(Enum.WorkflowStateId.SendingCxlConfirmationToCustomer2)]
        public void ProcessCancellation_RurubuDomestic_ShouldNotSendCxlConfirmEmailToCustomer(Enum.WorkflowStateId sendAmendEmailState)
        {
            var rowDataFromDb = new List<DataPropertyObject>
            {
                new DataPropertyObject("booking_id", 2345678, typeof(int)),
                new DataPropertyObject("workflow_state_id", (int)sendAmendEmailState, typeof(int)),
                new DataPropertyObject("workflow_action_id", 22, typeof(int)),
                new DataPropertyObject("ss_id_fb", DBNull.Value, typeof(int)), //int32
                new DataPropertyObject("language_id", 1, typeof(int)),
                new DataPropertyObject("workflow_parameter_value",
                    "<Parameter><ModuleID>2</ModuleID><TemplateID>88</TemplateID><Priority>1</Priority><EmailFormat>2</EmailFormat><NotesTypeID>2</NotesTypeID></Parameter>", typeof(string)),
                new DataPropertyObject("dmc_code", "JTB", typeof(string)),
                new DataPropertyObject("dmc_id", 29014, typeof(int)),
                new DataPropertyObject("dmc_type", 2, typeof(int)),
                new DataPropertyObject("booking_date_from", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("booking_date_until", "2012-04-08 00:00:00", typeof(DateTime)),
                new DataPropertyObject("other_special_needs", "Good Sea View", typeof(string)),
                new DataPropertyObject("is_multiple_bookings", "false", typeof(bool)),
                new DataPropertyObject("is_fraud_verify_sent", "false", typeof(bool)),
                new DataPropertyObject("itinerary_id", 293, typeof(int)),
                new DataPropertyObject("cid_list", 1234, typeof(int)),
                new DataPropertyObject("payment_model", 1, typeof(int)),
                new DataPropertyObject("cancellation_info",
                    string.Empty, typeof(string)),
                new DataPropertyObject("request_type", 14, typeof(int)),
                new DataPropertyObject("amendment_id", 123456, typeof(int)),
                new DataPropertyObject("is_skip_customer_cxl_confirm_email", "false", typeof(bool)),
                new DataPropertyObject("whitelabel_id", 4, typeof(int)),
                new DataPropertyObject("dmc_due_date", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("origin", "HK", typeof(string)),
                new DataPropertyObject("device_type_id", 0, typeof(string)),
                new DataPropertyObject("tracking_cookie_id", "457c86dd-3046-4759-a924-8c1c7b2cb0be", typeof(string)),
                new DataPropertyObject("pricefreeze_booking_id", 12345678, typeof(int)),
                new DataPropertyObject("is_test_booking", false, typeof(bool))
            };
            var dataRow = DataRowUtil.FromDataRowObject(rowDataFromDb);
            var bookingItem = new BookingItem(dataRow);
            var config = Substitute.For<BookingWorkflow>();

            _dataAccessMock.GetBookingInfo(2345678).Returns(bookingItem);
            _configurationMock.EnablePayHotelDMCForSendingHotelVoucher.Returns(new List<int> { 332, 29004 });
            _configurationMock.EmailHotelVoucherState.Returns(new List<int> { 201, 218, 251, 291, 296, 314, 401, 661, 752, 753, 811, 832, 834, 841, 852, 861, 872 });
            _configurationMock.EmailVerifyState.Returns(new List<int> { 111 });
            _configurationMock.EmailConfirmationState.Returns(new List<int> { 111 });
            _configurationMock.CancellationHotelVoucherState.Returns(new List<int> { });
            _wlConfigService.IsRurubuDomestic(Arg.Any<int>(), Arg.Any<bool>(), Arg.Any<int>()).Returns(true);
            
            var message = new DefaultMessage
            {
                BookingId = 2345678,
                AdditionalData = null
            };

            _dataProcessor.Process(message);
            _dataAccessMock.DidNotReceive().GetEmailDetailInfo(Arg.Any<BookingItem>());
            _workflowServiceMock.Received().ChangeStateWithWorkflowActionResultSkipped(2345678, Arg.Any<string>());
        }
        
        [TestCase(Enum.WorkflowStateId.SendingCxlConfirmationToCustomer1)]
        [TestCase(Enum.WorkflowStateId.SendingCxlConfirmationToCustomer2)]
        public void ProcessCancellation_CxlAndRebook_ShouldNotSendCxlConfirmEmailToCustomer(Enum.WorkflowStateId sendCancelEmailState)
        {
            var rowDataFromDb = new List<DataPropertyObject>
            {
                new DataPropertyObject("booking_id", 2345678, typeof(int)),
                new DataPropertyObject("workflow_state_id", (int)sendCancelEmailState, typeof(int)),
                new DataPropertyObject("workflow_action_id", 22, typeof(int)),
                new DataPropertyObject("ss_id_fb", DBNull.Value, typeof(int)), //int32
                new DataPropertyObject("language_id", 1, typeof(int)),
                new DataPropertyObject("workflow_parameter_value",
                    "<Parameter><ModuleID>2</ModuleID><TemplateID>88</TemplateID><Priority>1</Priority><EmailFormat>2</EmailFormat><NotesTypeID>2</NotesTypeID></Parameter>", typeof(string)),
                new DataPropertyObject("dmc_code", "YCS", typeof(string)),
                new DataPropertyObject("dmc_id", 332, typeof(int)),
                new DataPropertyObject("dmc_type", 2, typeof(int)),
                new DataPropertyObject("booking_date_from", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("booking_date_until", "2012-04-08 00:00:00", typeof(DateTime)),
                new DataPropertyObject("other_special_needs", "Good Sea View", typeof(string)),
                new DataPropertyObject("is_multiple_bookings", "false", typeof(bool)),
                new DataPropertyObject("is_fraud_verify_sent", "false", typeof(bool)),
                new DataPropertyObject("itinerary_id", 293, typeof(int)),
                new DataPropertyObject("cid_list", 1234, typeof(int)),
                new DataPropertyObject("payment_model", 1, typeof(int)),
                new DataPropertyObject("cancellation_info",
                    string.Empty, typeof(string)),
                new DataPropertyObject("request_type", 14, typeof(int)),
                new DataPropertyObject("amendment_id", 123456, typeof(int)),
                new DataPropertyObject("is_skip_customer_cxl_confirm_email", "false", typeof(bool)),
                new DataPropertyObject("whitelabel_id", 1, typeof(int)),
                new DataPropertyObject("dmc_due_date", "2012-04-06 00:00:00", typeof(DateTime)),
                new DataPropertyObject("origin", "HK", typeof(string)),
                new DataPropertyObject("device_type_id", 0, typeof(string)),
                new DataPropertyObject("tracking_cookie_id", "457c86dd-3046-4759-a924-8c1c7b2cb0be", typeof(string)),
                new DataPropertyObject("pricefreeze_booking_id", 12345678, typeof(int)),
                new DataPropertyObject("is_test_booking", false, typeof(bool))
            };
            var dataRow = DataRowUtil.FromDataRowObject(rowDataFromDb);
            var bookingItem = new BookingItem(dataRow);

            _dataAccessMock.GetBookingInfo(2345678).Returns(bookingItem);
            
            
            var resellBookingItem = new BookingResell
            {
                BookingId = 2345678,
                ResellBookingId = 456678391,
                ResellStatus = Provisioning.Enum.EBEBookingResellStatus.ConfirmedV2
            };
            _dataAccessMock.GetBookingResell(Provisioning.Enum.EBEBookingResellType.CancelRebook, 2345678).Returns(resellBookingItem);
            _configurationMock.EnablePayHotelDMCForSendingHotelVoucher.Returns(new List<int> { 332, 29004 });
            _configurationMock.EmailHotelVoucherState.Returns(new List<int> { 201, 218, 251, 291, 296, 314, 401, 661, 752, 753, 811, 832, 834, 841, 852, 861, 872 });
            _configurationMock.EmailVerifyState.Returns(new List<int> { 111 });
            _configurationMock.EmailConfirmationState.Returns(new List<int> { 111 });
            _configurationMock.CancellationHotelVoucherState.Returns(new List<int> { });


            var message = new DefaultMessage
            {
                BookingId = 2345678,
                AdditionalData = null
            };

            _dataProcessor.Process(message);
            _dataAccessMock.DidNotReceive().GetEmailDetailInfo(Arg.Any<BookingItem>());
            _workflowServiceMock.Received().ChangeStateWithWorkflowActionResultSkipped(2345678, Arg.Any<string>());
        }
    }
}