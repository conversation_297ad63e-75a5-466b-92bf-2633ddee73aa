using System;
using System.Collections.Generic;
using Agoda.Adp.Messaging.Client;
using Agoda.Config.Consul.KeyValue.FeatureSwitch.Services;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.EBE.Agents.Payment.Configuration.FeatureSwitches;
using Agoda.EBE.Agents.Payment.Objects;
using Agoda.EBE.Agents.Payment.Processor.Common.ExternalLoyalty;
using Agoda.EBE.Agents.Payment.Service.Interface;
using Agoda.EBE.Framework;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.Objects;
using Agoda.ExternalLoyaltyApi.Client.Models;
using Agoda.WhiteLabelApi.Client.Models;
using NSubstitute;
using NUnit.Framework;
using Shouldly;
using MeasurementMessage = Agoda.EBE.Agents.Common.Messaging.MeasurementMessage;

namespace Agoda.EBE.Agents.Payment.UnitTest.ExternalLoyalty
{
    public class ExternalLoyaltyRefundBenefitsProcessorTest
    {
        private IMessaging _messagingMocked;
        private ExternalLoyaltyRefundBenefitsProcessor _externalLoyaltyRefundBenefitsProcessor;
        private IEBEExceptionMessage _exceptionMocked;
        private MeasurementMessage _statMocked;
        private ItineraryBookingItem _primaryItineraryBookingItem;
        private IExternalLoyaltyApiService _externalLoyaltyApiServiceMocked;
        private IWhiteLabelConfigService _whiteLabelConfigServiceMocked;
        private IWhiteLabelHelperService _whiteLabelHelperServiceMocked;
        private IFeatureSwitchService _featureSwitchServiceMocked;
        private CancellationInfo _cxlInfo;

        [SetUp]
        public void Setup()
        {
            _statMocked = Substitute.For<MeasurementMessage>("unit-test", "payment_agent");
            _exceptionMocked = Substitute.For<IEBEExceptionMessage>();
            _messagingMocked = Substitute.For<IMessaging>();
            _messagingMocked.ExceptionMessage.Returns(_exceptionMocked);
            _messagingMocked.MeasurementMessageFactory.CreateNewMeasurement()
                .Returns(_statMocked);
            _externalLoyaltyApiServiceMocked = Substitute.For<IExternalLoyaltyApiService>();
            _whiteLabelConfigServiceMocked = Substitute.For<IWhiteLabelConfigService>();
            _whiteLabelHelperServiceMocked = Substitute.For<IWhiteLabelHelperService>();
            _featureSwitchServiceMocked = Substitute.For<IFeatureSwitchService>();
            _externalLoyaltyRefundBenefitsProcessor = Substitute.ForPartsOf<ExternalLoyaltyRefundBenefitsProcessor>(
                _messagingMocked, _externalLoyaltyApiServiceMocked, _whiteLabelConfigServiceMocked, _whiteLabelHelperServiceMocked, _featureSwitchServiceMocked);

            _primaryItineraryBookingItem = new ItineraryBookingItem
            {
                BookingId = 56591925,
                SiteId = "-1",
                WhitelabelId = 51
            };
            var refundItems = new List<RefundItem>
            {
                new RefundItem
                {
                    PaymentType = ConstantEnum.PaymentType.GiftCardCompensation,
                    Amount = 934.5M, Currency = "THB", AmountUsd = 30.0M
                },
                new RefundItem
                {
                    PaymentType = ConstantEnum.PaymentType.CreditCard,
                    Amount = 1869M, Currency = "THB", AmountUsd = 60.0M //70M
                }
            };
            var externalLoyaltyRefund = new ExternalLoyaltyRefund
            {
                externalLoyaltyCancellationToken = "valid"
            };
            _cxlInfo = new CancellationInfo
            {
                RefundOption = ConstantEnum.RefundOption.OriginalPaymentMethod,
                Refunds = refundItems,
                ExternalLoyaltyRefund = externalLoyaltyRefund
            };

            // Mock
            _externalLoyaltyApiServiceMocked
                .RefundBenefits(Arg.Any<string>(), Arg.Any<int>(), Arg.Any<long>(), Arg.Any<long>(), Arg.Any<int>(),
                    Arg.Any<string>(), Arg.Any<decimal>(), Arg.Any<decimal>(), Arg.Any<bool?>())
                .Returns(new RefundBenefitsResponse("200", null, new CampaignInfo("CITI4THNIGHT", 25.0, null)));
            
            var eligibleForRedemptionCampaignIds = new List<int?>();
            var eligibleForRedemptionPromotionCodes = new List<string>() { "CITI4THNIGHT" };
            _whiteLabelConfigServiceMocked
                .GetFeatureConfigByKey<BenefitRedemption>(Arg.Any<string>(),
                    WhiteLabelConfigName.BenefitRedemption, Arg.Any<bool?>())
                .Returns(new BenefitRedemption(eligibleForRedemptionCampaignIds, eligibleForRedemptionPromotionCodes));
        }
        
                [Test]
        public void RefundBenefitsLoyalty_SkipExternalBenefitsRefund()
        {
            #region Initial Variables
            
            var loyaltyInfo = new LoyaltyInfo
            {
                DiscountAmountUsd = 25,
                PartnerClaimToken = "testPartnerClaimToken",
                Promocode = "TCITIQ423H",
                BookingAmountUsd = 100
            };
            var guid = new Guid("5234834A-9346-47CE-BD5A-F2B754D3383E");

            #endregion
            
            _whiteLabelConfigServiceMocked.IsFeatureEnabled(WhiteLabelFeatureName.BenefitRedemption, guid,
                    Arg.Any<bool>(), Arg.Any<int>(), Arg.Any<string>(), Arg.Any<int>())
                .Returns(false);
            
            _externalLoyaltyRefundBenefitsProcessor.Process(_primaryItineraryBookingItem,
                _cxlInfo, loyaltyInfo);

            _whiteLabelConfigServiceMocked.Received(0).GetFeatureConfigByKey<BenefitRedemption>(Arg.Any<string>(),
                WhiteLabelConfigName.BenefitRedemption, Arg.Any<bool?>(), Arg.Any<string>(), Arg.Any<int>());
            _externalLoyaltyApiServiceMocked.Received(0).RefundBenefits(Arg.Any<string>(), Arg.Any<int>(),
                Arg.Any<long>(), Arg.Any<long>(), Arg.Any<int>(),
                Arg.Any<string>(), Arg.Any<decimal>(), Arg.Any<decimal>(), Arg.Any<bool?>());
        }

        [Test]
        [Ignore("This test has been failing for quite some time. Ignore until External Loyalty team fixes. Reference: https://gitlab.agodadev.io/ebe-net-core/agents/-/merge_requests/1795")]
        public void RefundBenefitsLoyalty_RefundFullBalance_UsingBenefitRedemptionConfig()
        {
            #region Initial Variables

            var loyaltyInfo = new LoyaltyInfo
            {
                DiscountAmountUsd = 25,
                PartnerClaimToken = "testPartnerClaimToken",
                Promocode = "CITI4THNIGHT",
                BookingAmountUsd = 100
            };
            var guid = new Guid("5234834A-9346-47CE-BD5A-F2B754D3383E");

            #endregion
            
            _whiteLabelConfigServiceMocked.IsFeatureEnabled(WhiteLabelFeatureName.BenefitRedemption, guid,
                    Arg.Any<bool?>(), Arg.Any<int?>(), Arg.Any<string>(), Arg.Any<int>())
                .Returns(true);

            _externalLoyaltyRefundBenefitsProcessor.Process(_primaryItineraryBookingItem,
                _cxlInfo, loyaltyInfo);

            _whiteLabelConfigServiceMocked.Received(1).GetFeatureConfigByKey<BenefitRedemption>(Arg.Any<string>(),
                WhiteLabelConfigName.BenefitRedemption, Arg.Any<bool?>(), Arg.Any<string>(), Arg.Any<int>());
            _externalLoyaltyApiServiceMocked.Received(1).RefundBenefits(Arg.Any<string>(), Arg.Any<int>(),
                Arg.Any<long>(), Arg.Any<long>(), Arg.Any<int>(),
                Arg.Any<string>(), Arg.Any<decimal>(), Arg.Any<decimal>(), Arg.Any<bool?>());
            _exceptionMocked.Received(1).Send(Arg.Is<string>(s =>
                s.Contains("External refund benefit is using BenefitRedemption config") && s.Contains("56591925")
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());
        }
        
        [Test]
        [Ignore("This test has been failing for quite some time. Ignore until External Loyalty team fixes. Reference: https://gitlab.agodadev.io/ebe-net-core/agents/-/merge_requests/1795")]
        public void RefundBenefitsLoyalty_SkipExternalBenefits_UsingBenefitRedemptionConfig()
        {
            #region Initial Variables
            
            var loyaltyInfo = new LoyaltyInfo
            {
                DiscountAmountUsd = 25,
                PartnerClaimToken = "testPartnerClaimToken",
                Promocode = "TCITIQ423H",
                BookingAmountUsd = 100
            };
            var guid = new Guid("5234834A-9346-47CE-BD5A-F2B754D3383E");

            #endregion
            
            _whiteLabelConfigServiceMocked.IsFeatureEnabled(WhiteLabelFeatureName.BenefitRedemption, guid,
                    Arg.Any<bool?>(), Arg.Any<int?>(), Arg.Any<string>(), Arg.Any<int>())
                .Returns(true);

            _externalLoyaltyRefundBenefitsProcessor.Process(_primaryItineraryBookingItem,
                _cxlInfo, loyaltyInfo);

            _whiteLabelConfigServiceMocked.Received(1).GetFeatureConfigByKey<BenefitRedemption>(Arg.Any<string>(),
                WhiteLabelConfigName.BenefitRedemption, Arg.Any<bool?>(), Arg.Any<string>(), Arg.Any<int>());
            _externalLoyaltyApiServiceMocked.Received(0).RefundBenefits(Arg.Any<string>(), Arg.Any<int>(),
                Arg.Any<long>(), Arg.Any<long>(), Arg.Any<int>(),
                Arg.Any<string>(), Arg.Any<decimal>(), Arg.Any<decimal>(), Arg.Any<bool?>());
            _exceptionMocked.Received(1).Send(Arg.Is<string>(s =>
                s.Contains("External refund benefit is using BenefitRedemption config") && s.Contains("56591925")
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());
        }

        [Test]
        public void Process_ShouldNotSkip_WhenRefundBenefitMigrationFeatureSwitchIsDisabled()
        {
            var loyaltyInfo = new LoyaltyInfo
            {
                DiscountAmountUsd = 25,
                PartnerClaimToken = "testPartnerClaimToken",
                Promocode = "CITI4THNIGHT",
                BookingAmountUsd = 100
            };
            
            var guid = new Guid("5234834A-9346-47CE-BD5A-F2B754D3383E");
            var whiteLabelToken = guid.ToString();
            
            _featureSwitchServiceMocked.IsOn<FeatureSwitch_Payment_RefundBenefitMigration>().Returns(false);
            
            _whiteLabelHelperServiceMocked.ResolveWhiteLabelToken(_primaryItineraryBookingItem.WhitelabelId).Returns(whiteLabelToken);
            
            _whiteLabelConfigServiceMocked.IsFeatureEnabled(WhiteLabelFeatureName.BenefitRedemption, guid,
                    Arg.Any<bool?>(), Arg.Any<int?>())
                .Returns(true);

            _externalLoyaltyRefundBenefitsProcessor.Process(_primaryItineraryBookingItem, _cxlInfo, loyaltyInfo);
            
            _exceptionMocked.DidNotReceive().Send(Arg.Is<string>(s =>
                s.Contains("External(CITI) refund benefit WL Config for") && 
                s.Contains(_primaryItineraryBookingItem.BookingId.ToString())
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());

            _exceptionMocked.DidNotReceive().Send(Arg.Is<string>(s =>
                s.Contains("CITI Refund Benefit Migration is skipped by WL Promo Config")
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());
            
            _exceptionMocked.Received().Send(Arg.Is<string>(s =>
                s.Contains("External refund benefit is using BenefitRedemption config")
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());
            
            _exceptionMocked.Received().Send(Arg.Is<string>(s =>
                s.Contains($"External refund benefit is successful. Booking ID: {_primaryItineraryBookingItem.BookingId}")
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());
        }

        [Test]
        public void Process_ShouldNotSkip_WhenRefundBenefitMigrationFeatureSwitchIsEnabled_AndCITIRefundBenefitMigrationIsDisabled()
        {
            var loyaltyInfo = new LoyaltyInfo
            {
                DiscountAmountUsd = 25,
                PartnerClaimToken = "testPartnerClaimToken",
                Promocode = "CITI4THNIGHT",
                BookingAmountUsd = 100
            };
            
            var guid = new Guid("5234834A-9346-47CE-BD5A-F2B754D3383E");
            var whiteLabelToken = guid.ToString();
            
            _featureSwitchServiceMocked.IsOn<FeatureSwitch_Payment_RefundBenefitMigration>().Returns(true);
            
            _whiteLabelHelperServiceMocked.ResolveWhiteLabelToken(_primaryItineraryBookingItem.WhitelabelId).Returns(whiteLabelToken);
            
            _whiteLabelConfigServiceMocked.IsFeatureEnabled(WhiteLabelFeatureName.BenefitRefundMigration, guid,
                    Arg.Any<bool?>(), Arg.Any<int?>())
                .Returns(false);
            
            _whiteLabelConfigServiceMocked.IsFeatureEnabled(WhiteLabelFeatureName.BenefitRedemption, guid,
                    Arg.Any<bool?>(), Arg.Any<int?>())
                .Returns(true);
            
            _whiteLabelConfigServiceMocked.IsFeatureEnabled(WhiteLabelFeatureName.BenefitRefundMigration, guid,
                    Arg.Any<bool?>(), Arg.Any<int?>())
                .Returns(false);

            _externalLoyaltyRefundBenefitsProcessor.Process(_primaryItineraryBookingItem, _cxlInfo, loyaltyInfo);
            
            _exceptionMocked.Received().Send(Arg.Is<string>(s =>
                s.Contains("External(CITI) refund benefit WL Config for") && 
                s.Contains(_primaryItineraryBookingItem.BookingId.ToString()) &&
                s.Contains("isRefundBenefitMigrationEnable: True") &&
                s.Contains("isBenefitRefundFeatureEnabled: False")
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());

            _exceptionMocked.DidNotReceive().Send(Arg.Is<string>(s =>
                s.Contains("CITI Refund Benefit Migration is skipped by WL Promo Config")
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());
            
            _exceptionMocked.Received().Send(Arg.Is<string>(s =>
                s.Contains("External refund benefit is using BenefitRedemption config")
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());
            
            _exceptionMocked.Received().Send(Arg.Is<string>(s =>
                s.Contains($"External refund benefit is successful. Booking ID: {_primaryItineraryBookingItem.BookingId}")
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());
        }

        [Test]
        public void Process_ShouldSkip_WhenRefundBenefitMigrationFeatureSwitchIsEnabled_AndCITIRefundBenefitMigrationIsEnabled()
        {
            var loyaltyInfo = new LoyaltyInfo
            {
                DiscountAmountUsd = 25,
                PartnerClaimToken = "testPartnerClaimToken",
                Promocode = "CITI4THNIGHT",
                BookingAmountUsd = 100
            };
            
            var guid = new Guid("5234834A-9346-47CE-BD5A-F2B754D3383E");
            var whiteLabelToken = guid.ToString();
            
            _featureSwitchServiceMocked.IsOn<FeatureSwitch_Payment_RefundBenefitMigration>().Returns(true);
            
            _whiteLabelHelperServiceMocked.ResolveWhiteLabelToken(_primaryItineraryBookingItem.WhitelabelId).Returns(whiteLabelToken);
            
            _whiteLabelConfigServiceMocked.IsFeatureEnabled(WhiteLabelFeatureName.BenefitRefundMigration, guid,
                    Arg.Any<bool?>(), Arg.Any<int?>())
                .Returns(true);

            _externalLoyaltyRefundBenefitsProcessor.Process(_primaryItineraryBookingItem, _cxlInfo, loyaltyInfo);
           
            _exceptionMocked.Received().Send(Arg.Is<string>(s =>
                s.Contains("External(CITI) refund benefit WL Config for") && 
                s.Contains(_primaryItineraryBookingItem.BookingId.ToString()) &&
                s.Contains("isRefundBenefitMigrationEnable: True") &&
                s.Contains("isBenefitRefundFeatureEnabled: True")
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());
                
            _exceptionMocked.Received().Send(Arg.Is<string>(s =>
                s.Contains("External refund benefit is skipped due to the migration. Booking ID") && 
                s.Contains(_primaryItineraryBookingItem.BookingId.ToString())
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());
            
            _exceptionMocked.DidNotReceive().Send(Arg.Is<string>(s =>
                s.Contains("External refund benefit is skipped by WL Promo Config(Non Citi promocode). Booking ID") &&
                s.Contains(_primaryItineraryBookingItem.BookingId.ToString())
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());
            
            _externalLoyaltyApiServiceMocked.DidNotReceive().RefundBenefits(Arg.Any<string>(), Arg.Any<int>(),
                Arg.Any<long>(), Arg.Any<long>(), Arg.Any<int>(),
                Arg.Any<string>(), Arg.Any<decimal>(), Arg.Any<decimal>(), Arg.Any<bool?>());
            
            _exceptionMocked.DidNotReceive().Send(Arg.Is<string>(s =>
                s.Contains("ELAPI Refund benefit response")
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());
            
            _exceptionMocked.DidNotReceive().Send(Arg.Is<string>(s =>
                s.Contains("External refund benefit is successful. Booking ID") &&
                s.Contains(_primaryItineraryBookingItem.BookingId.ToString())
            ), Arg.Any<Exception>(), Arg.Any<LogLevel>(), Arg.Any<Dictionary<string, string>>());
        }
    }
}
