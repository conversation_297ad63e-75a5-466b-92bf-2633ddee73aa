using System;
using System.Collections.Generic;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Payment.Interface;
using Agoda.EBE.Agents.Payment.Objects;
using Agoda.EBE.Agents.Payment.Processor.Common;
using Agoda.EBE.Framework;
using Agoda.EBE.Framework.Objects;
using NSubstitute;
using NUnit.Framework;
using Shouldly;
using PaymentItem = Agoda.EBE.Agents.Payment.Objects.PaymentItem;
using ChargeItem = Agoda.EBE.Framework.Objects.ChargeItem;

namespace Agoda.EBE.Agents.Payment.UnitTest.Processor.Common
{
    [TestFixture]
    [Category("Payment_Agent")]
    public class CalculatorProcessorTest
    {
        private IMessaging _messagingMock;
        private IPaymentValidatorHelper _paymentValidatorMock;
        private IPaymentDataAccess _dataAccessMock;

        private CalculatorProcessor _processor;

        [SetUp]
        public void Setup()
        {
            _messagingMock = Substitute.For<IMessaging>();
            _paymentValidatorMock = Substitute.For<IPaymentValidatorHelper>();
            _dataAccessMock = Substitute.For<IPaymentDataAccess>();
            _dataAccessMock
                .GetCurrencyDetail(Arg.Any<string>())
                .Returns(new CurrencyDetail { NoDecimal = 2 });

            _processor = new CalculatorProcessor(
                _messagingMock,
                _paymentValidatorMock,
                _dataAccessMock
            );
        }

        public static IEnumerable<object[]> ManipulatePaymentAmountTestCases()
        {
            yield return new object[]
            {
                new PaymentItem(),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRateInternational: MockUtils.ThbUsdUpliftExchangeRate
                ),
                100m,
                "THB",
                false,
                MockUtils.CreateMockPaymentItem(
                    paymentAmount: 100,
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.ThbUsdUpliftExchangeRate,
                    localAmount: 3692,
                    upliftAmount: 176,
                    localCurrency: "THB"
                )
            };
            yield return new object[]
            {
                new PaymentItem(),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRateInternational: MockUtils.JpyUsdUpliftExchangeRate
                ),
                10m,
                "JPY",
                false,
                MockUtils.CreateMockPaymentItem(
                    paymentAmount: 10,
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.JpyUsdUpliftExchangeRate,
                    localAmount: 1552,
                    upliftAmount: 73.9m,
                    localCurrency: "JPY"
                )
            };
        }

        public static IEnumerable<object[]> ManipulateFloatingExchangeRatePaymentAmountV1TestCases()
        {
            yield return new object[]
            {
                new PaymentItem(),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate
                ),
                100m,
                "THB",
                ExchangeRateOption.FloatingUpliftFX,
                new List<ChargeItem>(),
                new List<PaymentItem>(),
                ConstantEnum.TransactionType.PreAuthorize,
                false,
                MockUtils.CreateMockPaymentItem(
                    paymentAmount: 100,
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.ThbUsdUpliftExchangeRate,
                    localAmount: 3692,
                    upliftAmount: 176,
                    localCurrency: "THB"
                )
            };
            yield return new object[]
            {
                new PaymentItem(),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate
                ),
                10m,
                "JPY",
                ExchangeRateOption.FloatingUpliftFX,
                new List<ChargeItem>(),
                new List<PaymentItem>(),
                ConstantEnum.TransactionType.PreAuthorize,
                false,
                MockUtils.CreateMockPaymentItem(
                    paymentAmount: 10,
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.JpyUsdUpliftExchangeRate,
                    localAmount: 1552,
                    upliftAmount: 73.9m,
                    localCurrency: "JPY"
                )
            };
            yield return new object[]
            {
                new PaymentItem(),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate
                ),
                100m,
                "THB",
                ExchangeRateOption.FloatingUpliftFXV2,
                new List<ChargeItem>(),
                new List<PaymentItem>(),
                ConstantEnum.TransactionType.PreAuthorize,
                false,
                MockUtils.CreateMockPaymentItem(
                    paymentAmount: 100,
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.ThbUsdUpliftExchangeRate,
                    localAmount: 3692,
                    upliftAmount: 176,
                    localCurrency: "THB"
                )
            };
            yield return new object[]
            {
                new PaymentItem(),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate
                ),
                10m,
                "JPY",
                ExchangeRateOption.FloatingUpliftFXV2,
                new List<ChargeItem>(),
                new List<PaymentItem>(),
                ConstantEnum.TransactionType.PreAuthorize,
                false,
                MockUtils.CreateMockPaymentItem(
                    paymentAmount: 10,
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.JpyUsdUpliftExchangeRate,
                    localAmount: 1552,
                    upliftAmount: 73.9m,
                    localCurrency: "JPY"
                )
            };
            yield return new object[]
            {
                new PaymentItem(),
                MockUtils.CreateMockExchangeRateItem(MockUtils.ThbUsdSiteExchangeRate),
                100m,
                "THB",
                ExchangeRateOption.FloatingSiteFX,
                new List<ChargeItem>(),
                new List<PaymentItem>(),
                ConstantEnum.TransactionType.PreAuthorize,
                false,
                MockUtils.CreateMockPaymentItem(
                    paymentAmount: 100,
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.ThbUsdSiteExchangeRate,
                    localAmount: 3516,
                    localCurrency: "THB"
                )
            };
        }

        public static IEnumerable<object[]> ManipulateFloatingExchangeRatePaymentAmountV2TestCases()
        {
            yield return new object[]
            {
                MockUtils.CreateMockPaymentItem(exchangeRateLocalToPayment: MockUtils.ThbUsdUpliftExchangeRate),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate,
                    floatingUpliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate
                ),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate,
                    floatingUpliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate
                ),
                100m,
                "THB",
                ExchangeRateOption.FloatingUpliftFX,
                new List<ChargeItem>() { MockUtils.CreateMockChargeItem(100m, 3516m) },
                new List<PaymentItem>(),
                ConstantEnum.TransactionType.PreAuthorize,
                MockUtils.CreateMockPaymentItem(
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.ThbUsdUpliftExchangeRate, localAmount: 3692,
                    upliftAmount: 176,
                    newUpliftAmount: 3692m
                )
            };
            yield return new object[]
            {
                MockUtils.CreateMockPaymentItem(exchangeRateLocalToPayment: MockUtils.JpyUsdUpliftExchangeRate),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate,
                    floatingUpliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate
                ),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate,
                    floatingUpliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate
                ),
                10m,
                "JPY",
                ExchangeRateOption.FloatingUpliftFX,
                new List<ChargeItem>() { MockUtils.CreateMockChargeItem(10m, 1478.1m) },
                new List<PaymentItem>(),
                ConstantEnum.TransactionType.PreAuthorize,
                MockUtils.CreateMockPaymentItem(
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.JpyUsdUpliftExchangeRate,
                    localAmount: 1552,
                    upliftAmount: 73.9m,
                    newUpliftAmount: 1552m
                )
            };
            yield return new object[]
            {
                MockUtils.CreateMockPaymentItem(exchangeRateLocalToPayment: MockUtils.ThbUsdUpliftExchangeRate),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate,
                    floatingUpliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate
                ),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate,
                    floatingUpliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate
                ),
                100m,
                "THB",
                ExchangeRateOption.FloatingUpliftFXV2,
                new List<ChargeItem>() { MockUtils.CreateMockChargeItem(100m, 3516m) },
                new List<PaymentItem>(),
                ConstantEnum.TransactionType.PreAuthorize,
                MockUtils.CreateMockPaymentItem(siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.ThbUsdUpliftExchangeRate, localAmount: 3692,
                    upliftAmount: 176, newUpliftAmount: 3692m
                )
            };
            yield return new object[]
            {
                MockUtils.CreateMockPaymentItem(exchangeRateLocalToPayment: MockUtils.JpyUsdUpliftExchangeRate),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate,
                    floatingUpliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate
                ),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate,
                    floatingUpliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate
                ),
                10m,
                "JPY",
                ExchangeRateOption.FloatingUpliftFXV2,
                new List<ChargeItem>() { MockUtils.CreateMockChargeItem(10m, 1478.1m) },
                new List<PaymentItem>(),
                ConstantEnum.TransactionType.PreAuthorize,
                MockUtils.CreateMockPaymentItem(
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.JpyUsdUpliftExchangeRate,
                    localAmount: 1552,
                    upliftAmount: 73.9m,
                    newUpliftAmount: 1552m
                )
            };
            yield return new object[]
            {
                MockUtils.CreateMockPaymentItem(exchangeRateLocalToPayment: MockUtils.ThbUsdSiteExchangeRate),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    floatingSiteExchangeRate: MockUtils.ThbUsdSiteExchangeRate
                ),
                MockUtils.CreateMockExchangeRateItem(MockUtils.ThbUsdSiteExchangeRate, MockUtils.ThbUsdSiteExchangeRate,
                    floatingUpliftExchangeRate: MockUtils.ThbUsdSiteExchangeRate),
                100m,
                "THB",
                ExchangeRateOption.FloatingSiteFX,
                new List<ChargeItem>() { MockUtils.CreateMockChargeItem(100m, 3516m) },
                new List<PaymentItem>(),
                ConstantEnum.TransactionType.PreAuthorize,
                MockUtils.CreateMockPaymentItem(siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.ThbUsdSiteExchangeRate, localAmount: 3516,
                    newUpliftAmount: 3516m
                )
            };
        }

        public static IEnumerable<object[]> ManipulateFloatingExchangeRatePaymentAmountV3TestCases()
        {
            yield return new object[]
            {
                MockUtils.CreateMockPaymentItem(exchangeRateLocalToPayment: MockUtils.ThbUsdUpliftExchangeRate),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate,
                    floatingUpliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate
                ),
                100m,
                "THB",
                ExchangeRateOption.FloatingUpliftFX,
                false,
                MockUtils.CreateMockPaymentItem(siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.ThbUsdUpliftExchangeRate, localAmount: 3692,
                    upliftAmount: 176, paymentAmount: 100
                )
            };
            yield return new object[]
            {
                MockUtils.CreateMockPaymentItem(exchangeRateLocalToPayment: MockUtils.JpyUsdUpliftExchangeRate),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate,
                    floatingUpliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate
                ),
                10m,
                "JPY",
                ExchangeRateOption.FloatingUpliftFX,
                false,
                MockUtils.CreateMockPaymentItem(siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.JpyUsdUpliftExchangeRate, localAmount: 1552,
                    upliftAmount: 73.9m, paymentAmount: 10
                )
            };
            yield return new object[]
            {
                MockUtils.CreateMockPaymentItem(exchangeRateLocalToPayment: MockUtils.ThbUsdUpliftExchangeRate),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate,
                    floatingUpliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate
                ),
                100m,
                "THB",
                ExchangeRateOption.FloatingUpliftFXV2,
                false,
                MockUtils.CreateMockPaymentItem(siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.ThbUsdUpliftExchangeRate, localAmount: 3692,
                    upliftAmount: 176, paymentAmount: 100
                )
            };
            yield return new object[]
            {
                MockUtils.CreateMockPaymentItem(exchangeRateLocalToPayment: MockUtils.JpyUsdUpliftExchangeRate),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate,
                    floatingUpliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate
                ),
                10m,
                "JPY",
                ExchangeRateOption.FloatingUpliftFXV2,
                false,
                MockUtils.CreateMockPaymentItem(siteExchangeRate: MockUtils.JpyUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.JpyUsdUpliftExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.JpyUsdUpliftExchangeRate, localAmount: 1552,
                    upliftAmount: 73.9m, paymentAmount: 10
                )
            };
            yield return new object[]
            {
                MockUtils.CreateMockPaymentItem(exchangeRateLocalToPayment: MockUtils.ThbUsdSiteExchangeRate),
                MockUtils.CreateMockExchangeRateItem(
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    floatingSiteExchangeRate: MockUtils.ThbUsdSiteExchangeRate
                ),
                100m,
                "THB",
                ExchangeRateOption.FloatingSiteFX,
                false,
                MockUtils.CreateMockPaymentItem(
                    siteExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    upliftExchangeRate: MockUtils.ThbUsdSiteExchangeRate,
                    exchangeRateLocalToPayment: MockUtils.ThbUsdSiteExchangeRate,
                    localAmount: 3516,
                    paymentAmount: 100
                )
            };
        }

        public static IEnumerable<object[]> CalculateHotelLocalCurrencyFluctuationTestCases()
        {
            yield return new object[]
            {
                new List<ChargeItem>() { MockUtils.CreateMockChargeItem(100m, 3516m) },
                MockUtils.CreateMockExchangeRateItem(MockUtils.ThbUsdSiteExchangeRate),
                1m
            };
            yield return new object[]
            {
                new List<ChargeItem>() { MockUtils.CreateMockChargeItem(10m, 1478.1m) },
                MockUtils.CreateMockExchangeRateItem(MockUtils.JpyUsdSiteExchangeRate),
                1m
            };
        }

        public static IEnumerable<object[]> CalculateExchangeRateTestCases()
        {
            yield return new object[]
            {
                3516m,
                100m,
                4,
                MidpointRounding.AwayFromZero,
                35.16m
            };
            yield return new object[]
            {
                1552m,
                10m,
                4,
                MidpointRounding.AwayFromZero,
                155.20m
            };
        }

        [Test]
        [TestCase(100, 35.16, "THB", false, 3516)]
        [TestCase(10, 147.81, "JPY", true, 1478.1)]
        public void CalculateLocalAmount(decimal usdAmount, decimal exchangeRate, string localCurrency,
            bool isSupportTruncateMode, decimal expected)
        {
            _processor.CalculateLocalAmount(usdAmount, exchangeRate, localCurrency, isSupportTruncateMode)
                .ShouldBe(expected);
        }

        [Test]
        [TestCase(100, 36.92, 35.16, "THB", 176)]
        [TestCase(10, 155.20, 147.81, "JPY", 73.9)]
        public void CalculateUpliftAmount(decimal usdAmount, decimal exchangeRate, decimal siteExchangeRate,
            string localCurrency, decimal expected)
        {
            _processor.CalculateUpliftAmount(usdAmount, exchangeRate, siteExchangeRate, localCurrency)
                .ShouldBe(expected);
        }

        public static IEnumerable<object[]> CalculateUpliftAmountWithPaymentItemTestCases()
        {
            // PBOC-522: Invoice payment type with zero uplift amount should return 0
            yield return new object[]
            {
                new PaymentItem
                {
                    PaymentTypeId = (int)PaymentType.Invoice,
                    UpliftAmount = decimal.Zero,
                    PaymentAmount = 100m,
                    ExchangeRateLocalToPayment = 36.92m,
                    SiteExchangeRate = 35.16m,
                    LocalCurrency = "THB"
                },
                0m
            };

            // Invoice payment type with non-zero uplift amount should calculate normally
            yield return new object[]
            {
                new PaymentItem
                {
                    PaymentTypeId = (int)PaymentType.Invoice,
                    UpliftAmount = 50m,
                    PaymentAmount = 100m,
                    ExchangeRateLocalToPayment = 36.92m,
                    SiteExchangeRate = 35.16m,
                    LocalCurrency = "THB"
                },
                176m
            };

            // Invoice payment type with negative uplift amount should calculate normally
            yield return new object[]
            {
                new PaymentItem
                {
                    PaymentTypeId = (int)PaymentType.Invoice,
                    UpliftAmount = -10m,
                    PaymentAmount = 100m,
                    ExchangeRateLocalToPayment = 36.92m,
                    SiteExchangeRate = 35.16m,
                    LocalCurrency = "THB"
                },
                176m
            };

            // Invoice payment type with very small non-zero uplift amount should calculate normally
            yield return new object[]
            {
                new PaymentItem
                {
                    PaymentTypeId = (int)PaymentType.Invoice,
                    UpliftAmount = 0.00001m,
                    PaymentAmount = 100m,
                    ExchangeRateLocalToPayment = 36.92m,
                    SiteExchangeRate = 35.16m,
                    LocalCurrency = "THB"
                },
                176m
            };

            // Non-Invoice payment type with zero uplift amount should calculate normally
            yield return new object[]
            {
                new PaymentItem
                {
                    PaymentTypeId = (int)PaymentType.CreditCard,
                    UpliftAmount = decimal.Zero,
                    PaymentAmount = 100m,
                    ExchangeRateLocalToPayment = 36.92m,
                    SiteExchangeRate = 35.16m,
                    LocalCurrency = "THB"
                },
                176m
            };

            // Non-Invoice payment type with non-zero uplift amount should calculate normally
            yield return new object[]
            {
                new PaymentItem
                {
                    PaymentTypeId = (int)PaymentType.CreditCard,
                    UpliftAmount = 50m,
                    PaymentAmount = 100m,
                    ExchangeRateLocalToPayment = 36.92m,
                    SiteExchangeRate = 35.16m,
                    LocalCurrency = "THB"
                },
                176m
            };

            // Company Account payment type with zero uplift amount should calculate normally
            yield return new object[]
            {
                new PaymentItem
                {
                    PaymentTypeId = (int)PaymentType.CompanyAccount,
                    UpliftAmount = decimal.Zero,
                    PaymentAmount = 100m,
                    ExchangeRateLocalToPayment = 36.92m,
                    SiteExchangeRate = 35.16m,
                    LocalCurrency = "THB"
                },
                176m
            };

            // Bank Transfer payment type with zero uplift amount should calculate normally
            yield return new object[]
            {
                new PaymentItem
                {
                    PaymentTypeId = (int)PaymentType.BankTransfer,
                    UpliftAmount = decimal.Zero,
                    PaymentAmount = 100m,
                    ExchangeRateLocalToPayment = 36.92m,
                    SiteExchangeRate = 35.16m,
                    LocalCurrency = "THB"
                },
                176m
            };

            // Test with JPY currency - Invoice with zero uplift
            yield return new object[]
            {
                new PaymentItem
                {
                    PaymentTypeId = (int)PaymentType.Invoice,
                    UpliftAmount = decimal.Zero,
                    PaymentAmount = 10m,
                    ExchangeRateLocalToPayment = 155.20m,
                    SiteExchangeRate = 147.81m,
                    LocalCurrency = "JPY"
                },
                0m
            };

            // Test with JPY currency - Invoice with non-zero uplift
            yield return new object[]
            {
                new PaymentItem
                {
                    PaymentTypeId = (int)PaymentType.Invoice,
                    UpliftAmount = 25m,
                    PaymentAmount = 10m,
                    ExchangeRateLocalToPayment = 155.20m,
                    SiteExchangeRate = 147.81m,
                    LocalCurrency = "JPY"
                },
                73.9m
            };
        }

        [Test]
        [TestCaseSource(nameof(CalculateUpliftAmountWithPaymentItemTestCases))]
        public void CalculateUpliftAmountWithPaymentItem(PaymentItem paymentItem, decimal expected)
        {
            _processor.CalculateUpliftAmount(paymentItem).ShouldBe(expected);
        }

        [Test]
        [TestCase(100 * 35.16, "THB", 3516)]
        [TestCase(10 * 147.81, "JPY", 1478.1)]
        [TestCase(666.66 / 100.00, "CNY", 6.67)]
        [TestCase(999.99 / 100.00, "GBP", 10)]
        [TestCase(999.99 / 100.00, "AUD", 9)]
        public void ProcessCurrency(decimal number, string currency, decimal expected)
        {
            _dataAccessMock.GetCurrencyDetail("AUD").Returns(new CurrencyDetail { NoDecimal = 0 });
            _processor.ProcessCurrency(number, currency).ShouldBe(expected);
        }

        [Test]
        [TestCase(100 * 35.16, "THB", 3516)]
        [TestCase(10 * 147.81, "JPY", 1478.1)]
        [TestCase(666.66 / 100.00, "CNY", 6.67)]
        [TestCase(999.99 / 100.00, "GBP", 10)]
        [TestCase(999.99 / 100.00, "AUD", 10)]
        [TestCase(345.58*31.8682, "TWD", 11013)]
        public void RoundCurrency(decimal number, string currency, decimal expected)
        {
            _dataAccessMock.GetCurrencyDetail("AUD").Returns(new CurrencyDetail { NoDecimal = 0 });
            _dataAccessMock.GetCurrencyDetail("TWD").Returns(new CurrencyDetail { NoDecimal = 0 });
            var result = _processor.RoundCurrency(number, currency);
            result.ShouldBe(expected);
        }

        [Test]
        [TestCase("THB", 3)]
        [TestCase("JPY", 5)]
        [TestCase("GBP", 2)]
        public void GetCurrencyDecimalDigitsPrecision(string currencyKey, int expected)
        {
            _dataAccessMock.GetCurrencyDetail("THB").Returns(new CurrencyDetail { NoDecimal = 3 });
            _dataAccessMock.GetCurrencyDetail("JPY").Returns(new CurrencyDetail { NoDecimal = 5 });
            _processor.GetCurrencyDecimalDigitsPrecision(currencyKey).ShouldBe(expected);
        }

        [Test]
        [TestCase("THB", 3)]
        [TestCase("JPY", 5)]
        [TestCase("GBP", 2)]
        public void GetNumberOfDecimalDigits(string currencyKey, int expected)
        {
            _dataAccessMock.GetCurrencyDetail("THB").Returns(new CurrencyDetail { NoDecimal = 3 });
            _dataAccessMock.GetCurrencyDetail("JPY").Returns(new CurrencyDetail { NoDecimal = 5 });
            _processor.GetNumberOfDecimalDigits(currencyKey).ShouldBe(expected);
        }

        [Test]
        [TestCaseSource(nameof(CalculateHotelLocalCurrencyFluctuationTestCases))]
        public void CalculateHotelLocalCurrencyFluctuation(IEnumerable<ChargeItem> chargeItems,
            ExchangeRateItem hotelExchangeRateItem, decimal expected)
        {
            _processor.CalculateHotelLocalCurrencyFluctuation(chargeItems, hotelExchangeRateItem).ShouldBe(expected);
        }

        [Test]
        [TestCaseSource(nameof(CalculateExchangeRateTestCases))]
        public void CalculateExchangeRate(decimal localAmount, decimal usdAmount, int digits,
            MidpointRounding midpointRounding, decimal expected)
        {
            _processor.CalculateExchangeRate(localAmount, usdAmount, digits, midpointRounding).ShouldBe(expected);
        }

        [Test]
        [TestCaseSource(nameof(ManipulatePaymentAmountTestCases))]
        public void ManipulatePaymentAmount(
            PaymentItem paymentItem,
            ExchangeRateItem exchangeRateItem,
            decimal balance,
            string localCurrency,
            bool isSupportTruncateMode,
            PaymentItem expected)
        {
            PaymentItem actual = _processor.ManipulatePaymentAmount(exchangeRateItem, balance, localCurrency,
                paymentItem, isSupportTruncateMode);

            actual.PaymentAmount.ShouldBe(expected.PaymentAmount);
            actual.LocalCurrency.ShouldBe(expected.LocalCurrency);
            actual.LocalAmount.ShouldBe(expected.LocalAmount);
            actual.UpliftAmount.ShouldBe(expected.UpliftAmount);
            actual.RateQuoteId.ShouldBe(expected.RateQuoteId);
            actual.UpliftExchangeRate.ShouldBe(expected.UpliftExchangeRate);
            actual.SiteExchangeRate.ShouldBe(expected.SiteExchangeRate);
            actual.DestinationCurrency.ShouldBe(expected.DestinationCurrency);
            actual.DestinationExchangeRate.ShouldBe(expected.DestinationExchangeRate);
            actual.ExchangeRateLocalToPayment.ShouldBe(expected.ExchangeRateLocalToPayment);
        }

        [Test]
        [TestCaseSource(nameof(ManipulateFloatingExchangeRatePaymentAmountV1TestCases))]
        public void ManipulateFloatingExchangeRatePaymentAmountV1(
            PaymentItem paymentItem,
            ExchangeRateItem exchangeRateItem,
            decimal balance,
            string localCurrency,
            ExchangeRateOption exchangeRateOption,
            List<ChargeItem> chgList,
            List<PaymentItem> paymentList,
            ConstantEnum.TransactionType transactionType,
            bool isSupportTruncateMode,
            PaymentItem expected)
        {
            PaymentItem actual = _processor.ManipulateFloatingExchangeRatePaymentAmount(paymentItem, exchangeRateItem,
                balance,
                localCurrency, exchangeRateOption, chgList, paymentList, transactionType, isSupportTruncateMode);

            actual.PaymentAmount.ShouldBe(expected.PaymentAmount);
            actual.SiteExchangeRate.ShouldBe(expected.SiteExchangeRate);
            actual.UpliftExchangeRate.ShouldBe(expected.UpliftExchangeRate);
            actual.ExchangeRateLocalToPayment.ShouldBe(expected.ExchangeRateLocalToPayment);
            actual.LocalAmount.ShouldBe(expected.LocalAmount);
            actual.UpliftAmount.ShouldBe(expected.UpliftAmount);
        }

        [Test]
        [TestCaseSource(nameof(ManipulateFloatingExchangeRatePaymentAmountV2TestCases))]
        public void ManipulateFloatingExchangeRatePaymentAmountV2(
            PaymentItem paymentItem,
            ExchangeRateItem exchangeRateItem,
            ExchangeRateItem hotelExchangeRateItem,
            decimal balance,
            string localCurrency,
            ExchangeRateOption exchangeRateOption,
            List<ChargeItem> chgList,
            List<PaymentItem> paymentList,
            ConstantEnum.TransactionType transactionType,
            PaymentItem expected)
        {
            PaymentItem actual = _processor.ManipulateFloatingExchangeRatePaymentAmount(paymentItem, exchangeRateItem,
                exchangeRateOption, chgList, paymentList, hotelExchangeRateItem, transactionType, balance,
                localCurrency);

            actual.SiteExchangeRate.ShouldBe(expected.SiteExchangeRate);
            actual.UpliftExchangeRate.ShouldBe(expected.UpliftExchangeRate);
            actual.ExchangeRateLocalToPayment.ShouldBe(expected.ExchangeRateLocalToPayment);
            actual.LocalAmount.ShouldBe(expected.LocalAmount);
            actual.UpliftAmount.ShouldBe(expected.UpliftAmount);
            actual.NewUpliftAmount.ShouldBe(expected.NewUpliftAmount);
            actual.RateQuoteId.ShouldBe(expected.RateQuoteId);
            actual.DestinationExchangeRate.ShouldBe(expected.DestinationExchangeRate);
            actual.DestinationCurrency.ShouldBe(expected.DestinationCurrency);
        }

        [Test]
        [TestCaseSource(nameof(ManipulateFloatingExchangeRatePaymentAmountV3TestCases))]
        public void ManipulateFloatingExchangeRatePaymentAmountV3(
            PaymentItem paymentItem,
            ExchangeRateItem exchangeRateItem,
            decimal balance,
            string localCurrency,
            ExchangeRateOption exchangeRateOption,
            bool isSupportTruncateMode,
            PaymentItem expected)
        {
            PaymentItem actual = _processor.ManipulateFloatingExchangeRatePaymentAmount(paymentItem, exchangeRateItem,
                balance, localCurrency, exchangeRateOption, isSupportTruncateMode);

            actual.PaymentAmount.ShouldBe(expected.PaymentAmount);
            actual.LocalAmount.ShouldBe(expected.LocalAmount);
            actual.UpliftAmount.ShouldBe(expected.UpliftAmount);
            actual.ExchangeRateLocalToPayment.ShouldBe(expected.ExchangeRateLocalToPayment);
            actual.SiteExchangeRate.ShouldBe(expected.SiteExchangeRate);
            actual.UpliftExchangeRate.ShouldBe(expected.UpliftExchangeRate);
            actual.RateQuoteId.ShouldBe(expected.RateQuoteId);
            actual.DestinationExchangeRate.ShouldBe(expected.DestinationExchangeRate);
            actual.DestinationCurrency.ShouldBe(expected.DestinationCurrency);
        }
    }
}