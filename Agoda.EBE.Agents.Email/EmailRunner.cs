using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common;
using Agoda.EBE.Agents.Common.Base;
using Agoda.EBE.Agents.Common.Configuration.Interface;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Email.DataAccess;
using Agoda.EBE.Agents.Email.DataAccess.Interface;
using Agoda.EBE.Agents.Email.Object;
using Agoda.EBE.Agents.Email.Object.Interface;
using Agoda.EBE.Agents.Email.Object.Messaging;
using Agoda.EBE.Agents.Email.Processor;
using Agoda.EBE.Agents.Email.Processor.Interface;
using Agoda.EBE.Agents.Email.Service;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess.Interface;
using Agoda.EBE.Framework;
using Agoda.EBE.Framework.Interface;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.MQ;
using Agoda.EBE.Framework.MQ.Interface;
using Agoda.EBE.Workflow.Client;
using Autofac;
using System;
using System.Net;
using System.Net.Http;
using System.Runtime.Caching;
using System.Threading;
using Agoda.EBE.Framework.Customer;
using Agoda.EBE.Framework.Customer.Interface;
using Agoda.EBE.Framework.DAL.Interface;
using Agoda.EBE.Framework.DAL;
using Agoda.EBE.Framework.Services.PIIClient;
using Agoda.Enigma.Client;
using Agoda.EBE.Agents.Email.Service.Helpers;
using Agoda.EBE.Agents.Email.Service.Helpers.Interface;
using Agoda.EBE.Agents.Email.SendEmail;
using Agoda.EBE.Framework.Services.PIIClient.EnigmaConfig;
using Agoda.EBE.Agents.Common.Configuration;
using Agoda.BookingApi.GenClient;
using Agoda.EBE.Agents.Common.ServiceDiscovery;
using System.Linq;
using Agoda.Config.Consul;
using Agoda.Config.Consul.Abstractions;
using Agoda.Config.Consul.Common.Configuration;
using Agoda.Config.Consul.Common.Models;
using Agoda.Config.Consul.KeyValue.FeatureSwitch.Services;
using Agoda.Cusco.CommunicationApi.Client;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.EBE.Agents.Common.Util.Data;
using Agoda.EBE.Agents.Common.Util.Data.Interface;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess.Helpers;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess.Helpers.Interface;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess.Interface;
using Agoda.EBE.Agents.Email.Object.Configuration.FeatureSwitches;
using Agoda.EBE.Agents.Email.Service.Interface;
using Agoda.WhiteLabelApi.Client;
using Autofac.Features.AttributeFilters;
using Microsoft.Rest;
using IWhiteLabelExperimentManager = Agoda.WhiteLabelApi.Client.Services.IExperimentManager;

namespace Agoda.EBE.Agents.Email
{
    public class EmailRunner : RunnerBase
    {
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly ContainerBuilder builder;
        private readonly IEmailConfiguration _emailConfiguration;

        public EmailRunner(string agentName, IEmailConfiguration consoleConfig, CancellationTokenSource cancelToken, ContainerBuilder builder)
            : base(agentName, (IConsoleConfiguration)consoleConfig, cancelToken)
        {
            _emailConfiguration = consoleConfig;
            _cancellationTokenSource = cancelToken;
            this.builder = builder;
        }

        protected override void GetConfig()
        {
        }

        public override void Start()
        {
            Console.WriteLine($"Starting {AgentName} agent");

            try
            {
                var container = GetContainer();

                InitWhiteLabelClientWithRetry(container, _emailConfiguration.WhiteLabelClientConfiguration);

                using (var root = container.BeginLifetimeScope())
                {
                    var messaging = root.Resolve<IMessaging>();
                    var configuration = root.Resolve<IEmailConfiguration>();

                    void RunQueueProcessIfActive(bool isActive, IQueueProcessor processor, string agentName)
                    {
                        if (isActive)
                        {
                            Console.WriteLine($"{agentName} is running");
                            messaging.ExceptionMessage.Send($"{agentName} Agent Started!", null, LogLevel.INFO);
                            if (!CancelToken.Token.IsCancellationRequested)
                            {
                                try
                                {
                                    processor.Process();
                                }
                                catch (Exception ex)
                                {
                                    messaging.ExceptionMessage.Send($"Unable to process {agentName}", ex, LogLevel.FATAL);
                                    throw;
                                }
                            }
                        }
                        else
                        {
                            messaging.ExceptionMessage.Send($"{agentName} does not run, configuration is set to false", null, LogLevel.INFO);
                        }
                    }

                    RunQueueProcessIfActive(configuration.IsActive, container.Resolve<IEmailQueueProcessor>(), AgentName);
                    RunQueueProcessIfActive(configuration.IsResendEmailActive,
                        container.Resolve<IEmailResendQueueProcessor>(),
                        $"{AgentName}.ResendEmail");
                    RunQueueProcessIfActive(configuration.IsSendEmailQueueActive,
                        container.Resolve<ISendEmailQueueProcessor>(),
                        $"{AgentName}.SendEmail.Federated");
                    RunQueueProcessIfActive(configuration.IsEmailAlertActive,
                        container.Resolve<IEmailAlertQueueProcessor>(),
                        $"{AgentName}.Alert");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error - EmailRunner {ex}");
                throw;
            }
        }

        public override void Stop()
        {
            if (Global.Instance.QueueAdapter != null)
            {
                Global.Instance.QueueAdapter.Dispose();
            }
        }

        private IContainer GetContainer()
        {
            var memoryCache = MemoryCache.Default;

            ConfigureConsul(_emailConfiguration, builder);
            builder.RegisterInstance(_emailConfiguration).As<IEmailConfiguration>();
            builder.RegisterInstance(_cancellationTokenSource).As<CancellationTokenSource>();

            builder.RegisterGeneric(typeof(OwnedWrapper<>)).As(typeof(IOwned<>)).ExternallyOwned();

            builder.Register(c => new MeasurementMessageFactory(c.Resolve<IEmailConfiguration>().MetricName,
                c.Resolve<IEmailConfiguration>().ComponentName)).As<IMeasurementMessageFactory>();
            builder.Register(c => new EmailAgentException(c.Resolve<IEmailConfiguration>().StoreFrontID))
                .As<IEBEExceptionMessage>();
            builder.RegisterType<Messaging>().As<IMessaging>();
            builder.RegisterType<CustomerApiService>().As<ICustomerApiService>();

            builder.Register(c => new EmailContentDal(c.Resolve<IEmailConfiguration>(), c.Resolve<IMessaging>(),
                memoryCache, c.Resolve<IPIIService>())).As<IEmailContentDal>();

            builder.RegisterType<DataAccess.DatabaseAccess>().As<IDatabaseAccess>();
            builder.Register(c =>
                    new DatabaseProvider(c.Resolve<IEmailConfiguration>().ApplicationEbeConnectionString))
                .As<IDatabaseProvider>();
            builder.RegisterType<HermesService>().As<IHermesService>();
            builder.RegisterType<HermesHelper>().As<IHermesHelper>();
            builder.RegisterType<PigeonService>().As<IPigeonService>();
            builder.RegisterType<PigeonHelper>().As<IPigeonHelper>();
            builder.RegisterType<ContactsApiService>().As<IContactsApiService>();
            builder.RegisterType<ContactsApiHelper>().As<IContactsApiHelper>();
            builder.RegisterType<WhiteLabelHelperService>().As<IWhiteLabelHelperService>();
            builder.RegisterType<DataAccess.ExternalServicesAccess.EmailSendingEligibilityService>().As<IEmailSendingEligibilityService>();
            builder.RegisterType<EmailService>().As<IEmailService>();
            builder.Register(c => new WorkflowServiceClient(serviceUrl: c.Resolve<IEmailConfiguration>().WorkflowWebserviceUrls, overrideHostHeader: true))
                .As<IWorkflowServiceClient>();
            builder.RegisterType<WorkflowService>().As<IWorkflowService>();
            builder.Register(c =>
            {
                var config = c.Resolve<IEmailConfiguration>();
                return new CustomerAPIClient(config.CustomerApiServiceUrl, config.RewardsApiKey, config.ComponentName, overrideHostHeader: true);
            }).As<ICustomerAPIClient>();
            builder.RegisterType<EmailDataProcessor>().As<IEmailDataProcessor>();
            builder.RegisterType<EmailQueueProcessor>().As<IEmailQueueProcessor>();
            builder.RegisterType<EmailResendDataProcessor>().As<IEmailResendDataProcessor>();
            builder.RegisterType<EmailResendQueueProcessor>().As<IEmailResendQueueProcessor>();
            builder.RegisterType<EmailAlertDataProcessor>().As<IEmailAlertDataProcessor>();
            builder.RegisterType<EmailAlertQueueProcessor>().As<IEmailAlertQueueProcessor>();
            builder.RegisterType<SendEmailProcessor>().As<ISendEmailProcessor>();
            builder.RegisterType<SendEmailQueueProcessor>().As<ISendEmailQueueProcessor>();
            builder.Register(c => new RabbitMQAdapter(c.Resolve<IEmailConfiguration>().QueueConnectionString))
                .As<IRabbitMQAdapter>();
            builder.Register(c => new RabbitMQAdapter(new RabbitMQAdapterConfiguration(
                    c.Resolve<IEmailConfiguration>().QueueConnectionString,
                    requeueOnAgentFailure: false)))
                .Named<IRabbitMQAdapter>(Constants.RMQAdaptorWithNoRequeueUponFailure);
            builder.Register(c => new DeliveryClientApi(c.Resolve<IEmailConfiguration>().DeliveryApiServiceUrl,
                c.Resolve<IEmailConfiguration>().DeliveryApiTimeout,
                c.Resolve<IEmailConfiguration>().DeliveryApiRetryCount,
                c.Resolve<IEmailConfiguration>().DeliveryApiServerMaxWeight)).As<IDeliveryClientApi>();
            builder.Register(CreateCommunicationApi).As<ICommunicationAPI>().SingleInstance();
            builder.RegisterType<DeliveryApiService>().As<IDeliveryApiService>();
            builder.RegisterType<CommunicationApiService>().As<ICommunicationApiService>();
            builder.Register(CreateEnigma).As<IEnigma>();
            builder.Register(c =>
            {
                var config = c.Resolve<IEmailConfiguration>();
                return new CustomerHelper(
                    c.Resolve<IEBEDalService>(),
                    config.CustomerApiServiceUrl[0],
                    config.ExternalServiceTimeoutSeconds,
                    c.Resolve<Agoda.EBE.Framework.Services.PIIClient.IPIIService>(),
                    config.RewardsApiKey,
                    config.ComponentName, overrideHostHeader: true);
            }).As<ICustomerHelper>();
            builder.Register(c =>
            {
                return new EBEDalService(c.Resolve<IEmailConfiguration>().ApplicationEbeConnectionString);
            }).As<IEBEDalService>();
            builder.Register(c =>
            {
                var config = c.Resolve<IEmailConfiguration>();
                var enigma = c.Resolve<IEnigma>();
                return new PIIService(enigma, config.ComponentName);
            }).As<IPIIService>();

            RegisterSecureLinkGenerationService(builder, _emailConfiguration);
            RegisterWhiteLabelConfigService(builder, _emailConfiguration.WhiteLabelClientConfiguration);

            return builder.Build();
        }

        private void RegisterSecureLinkGenerationService(ContainerBuilder builder, IEmailConfiguration configuration)
        {
            builder.RegisterInstance(configuration.BookingApiConfiguration);
            RegisterBookingApi(builder, configuration.BookingApiConfiguration);
            builder.RegisterType<BookingApiService>().As<ISecureLinkGenerationService>().WithAttributeFiltering();
        }

        private void RegisterBookingApi(ContainerBuilder builder, BookingApiConfiguration configuration)
        {
            RegisterBookingApiLoadBalancingConfig(builder, configuration);
            builder.Register(c =>
            {
                var httpHandler = new HttpClientHandler
                {
                    ServerCertificateCustomValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true
                };
                var httpClient = new HttpClient(httpHandler);
                var bookingApiLoadBalancingConfig = c.Resolve<BookingApiLoadBalancingConfig>();
                return new BookingAPI(httpClient, bookingApiLoadBalancingConfig);
            }).Keyed<IBookingAPI>("bookingApiClient").SingleInstance();
        }

        private void RegisterBookingApiLoadBalancingConfig(ContainerBuilder builder, BookingApiConfiguration bookingApiConfiguration)
        {
            builder.Register(c =>
            {
                if (bookingApiConfiguration.IsConnectBapiViaConsul)
                {
                    var bookingApiConsulService = c.Resolve<BookingApiConsulService>();
                    string[] urls = bookingApiConsulService.GetEndpoints().ToArray();
                    return new BookingApiLoadBalancingConfig(urls, bookingApiConsulService);
                }
                else
                {
                    return new BookingApiLoadBalancingConfig(bookingApiConfiguration.Urls, null);
                }
            });
        }

        private void RegisterWhiteLabelConfigService(ContainerBuilder builder, WhiteLabelClientConfiguration whiteLabelClientConfiguration)
        {
            var serverSettings = whiteLabelClientConfiguration.ServerSettings.ToList();

            var whiteLabelApiConfig = new ApiBaseConfig
            {
                name = "WhiteLabelApiClient",
                deserializationSettings = null,
                serializationSettings = null,
                retryCount = 1,
                timeout = TimeSpan.FromMilliseconds(whiteLabelClientConfiguration.TimeoutMilliseconds),
                manualCalcUris = false,
                overrideHostHeader = true,
                settings = serverSettings
            };

            var whiteLabelApi = new WhiteLabelAPIImpl(whiteLabelApiConfig);
            var whiteLabelClient = new WhiteLabelClient(
                whiteLabelApi,
                "ebe",
                "ebe_email_agent"
            );

            builder.RegisterInstance(whiteLabelClient).As<IWhiteLabelClient>();

            builder.RegisterType<WhiteLabelExperimentManager>().As<IWhiteLabelExperimentManager>();
            builder.RegisterType<WhiteLabelConfigService>().As<IWhiteLabelConfigService>();
        }


        IEnigma CreateEnigma(IComponentContext context)
        {
            var config = context.Resolve<IEmailConfiguration>();
            HttpClientHandler handler = new HttpClientHandler
            {
                AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
                ClientCertificateOptions = ClientCertificateOption.Manual,
                ServerCertificateCustomValidationCallback =
                    (httpRequestMessage, cert, cetChain, policyErrors) => true,
                UseDefaultCredentials = true,
            };
            var httpClient = new HttpClient(handler);
            httpClient.DefaultRequestHeaders.Add("X-PII-API-Key", config.EnigmaApiKey);
            httpClient.DefaultRequestHeaders.Add("X-Agoda-ClientMachine", "EBE-Net-Core Email Agent");
            var enigmaApiConfig = new EnigmaApiConfig(config.EnigmaServiceUrls);
            return new Enigma.Client.Enigma(httpClient, enigmaApiConfig);
        }

        ICommunicationAPI CreateCommunicationApi(IComponentContext context)
        {
            var config = context.Resolve<IEmailConfiguration>();
            HttpClientHandler handler = new HttpClientHandler
            {
                AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
                ClientCertificateOptions = ClientCertificateOption.Manual,
                ServerCertificateCustomValidationCallback = (_, _, _, _) => true
            };
            var httpClient = new HttpClient(handler);
            var communicationApi = new CommunicationAPI(new TokenCredentials("t"), httpClient, false)
            {
                BaseUri = new Uri(config.CommunicationApiServiceUrl)
            };
            return communicationApi;
        }

        private void ConfigureConsul(IEmailConfiguration configuration, ContainerBuilder builder)
        {
            var consulSetting = configuration.ConsulSetting;
            ConfigurationConfigurator.RegisterConfigurationSettings()
                .FromAssemblies(typeof(BookingApiConsulService).Assembly)
                .WithCapabilities(Capability.ServiceDiscovery, Capability.FeatureSwitch)
                .WithAppSettingsConfiguration(new AppSettingsConfiguration()
                {
                    RegisterAsSingleton = configSetting => builder.RegisterInstance(configSetting)
                        .As<IConfigurationSetting>()
                        .SingleInstance(),
                    AppName = "ebe_netcore_agents",
                    GroupName = "ebe_netcore_agents"
                })
                .WithConsulConfiguration(new ConsulConfiguration()
                {
                    AgentUrl = consulSetting.AgentUrl,
                    ServerUrl = consulSetting.ServerUrl,
                    DataCenter = consulSetting.DataCenter,
                    PollingInterval = TimeSpan.FromSeconds(consulSetting.PollingIntervalInSeconds),
                    LocalFileCachePath = consulSetting.LocalFileCachePath,
                    DontUseConsul = consulSetting.DontUseConsul
                })
                .WithFeatureSwitchConfiguration(new FeatureSwitchConfiguration()
                {
                    RegisterAsSingleton = featureSwitchMgr => builder.RegisterInstance(featureSwitchMgr)
                        .As<IFeatureSwitchService>()
                        .SingleInstance()
                })
                .WithServiceDiscoveryConfiguration(new ServiceDiscoveryConfiguration()
                {
                    RegisterAsSingleton = configSetting => builder.RegisterInstance(configSetting)
                        .AsSelf()
                        .SingleInstance()
                })
                .DoYourThing();
        }
    }
}
