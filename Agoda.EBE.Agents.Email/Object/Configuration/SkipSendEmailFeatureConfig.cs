using System.Collections.Generic;

namespace Agoda.EBE.Agents.Email.Object.Configuration
{
    public class SkipSendEmailFeatureConfig
    {
        public const string FEATURE_NAME = "SkipSendEmail";
        
        public List<SkipSendEmailTemplate>? AllowedTemplates { get; set; } = new List<SkipSendEmailTemplate>();
    }

    public class SkipSendEmailTemplate
    {
        public string? TemplateName { get; set; }
        public string? ExperimentName { get; set; }
        public bool? ShouldSkip { get; set; }
    }
} 