using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Email.DataAccess.Interface;
using Agoda.EBE.Agents.Email.Object.Interface;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using Agoda.Config.Consul.KeyValue.FeatureSwitch.Services;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.EBE.Agents.Email.Object;
using Agoda.EBE.Agents.Email.Object.Configuration;
using Agoda.EBE.Agents.Email.Object.Configuration.FeatureSwitches;
using Agoda.EBE.Agents.Email.Object.Exceptions;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.MQ.MessageType;
using Agoda.Rewards.Api.Definition;
using ConstantEnum = Agoda.EBE.Framework.ConstantEnum;
using EmailActionResult = Agoda.EBE.Agents.Email.Enum.EmailActionResult;
using CommonEnum = Agoda.EBE.Agents.Common.Enum;
using Agoda.EBE.Agents.Email.SendEmail.Object;
using Agoda.EBE.Agents.Email.Service.Helpers.Interface;
using Agoda.EBE.Agents.Email.Service.Interface;
using Agoda.EBE.Agents.Email.Util;
using Agoda.EBE.Framework.EmailContent.Objects.Interface;
using Agoda.EBE.Agents.Email.Service.Utils;

namespace Agoda.EBE.Agents.Email.Service
{
    public class EmailService : IEmailService
    {
        private readonly IEmailConfiguration _configuration;
        private readonly IDatabaseAccess _dataAccess;
        private readonly IMessaging _messaging;
        private readonly IDeliveryApiService _deliveryApiService;
        private readonly ICommunicationApiService _communicationApiService;
        private readonly IPigeonHelper _pigeonHelper;
        private readonly IWhiteLabelHelperService _wlHelperService;
        private readonly IWhiteLabelConfigService _whiteLabelConfigService;
        private readonly IFeatureSwitchService _featureSwitchService;
        private readonly IEmailSendingEligibilityService _emailSendingEligibilityService;

        private const string CHARGE_OPTION = "ChargeOption";
        private const string CHARGE_OPTION_NAME = "ChargeOptionName";
        private const string PAYMENT_MODEL = "PaymentModel";
        private const string PAYMENT_MODEL_NAME = "PaymentModelName";
        private const string DMC = "DMC";
        private const string DMC_CODE = "DMCCode";
        private const string DMC_NAME = "DMCName";
        private const string PAYMENT_CATEGORY = "PaymentCategory";
        private const string PAYMENT_CATEGORY_NAME = "PaymentCategoryName";
        private const string PAYMENT_METHOD = "PaymentMethod";
        private const string PAYMENT_METHOD_NAME = "PaymentMethodName";
        private const string GATEWAY = "Gateway";
        private const string GATEWAY_NAME = "GatewayName";

        private const string CEG_MANUAL = "CEGManual";
        private const string TEMPLATE = "Template";
        private const string STOREFRONT_ID = "StorefrontId";
        private const string AFFILIATE_PAYMENT_METHOD = "AffiliatePaymentMethod";
        private const string BOOKING_TYPE = "BookingType";
        private const string AFFILIATE_MODEL = "AffiliateModel";
        private const string AVAILABILITY_TYPE = "AvailabilityType";

        private const bool DEFAULT_SHOULD_FALLBACK_AFTER_PIGEON_FAILED = true;

        public EmailService(
            IEmailConfiguration emailConfiguration,
            IDatabaseAccess dataAccess,
            IMessaging messaging,
            IDeliveryApiService deliveryApiService,
            ICommunicationApiService communicationApiService,
            IPigeonHelper pigeonHelper,
            IWhiteLabelHelperService wlHelperService,
            IWhiteLabelConfigService whiteLabelConfigService,
            IFeatureSwitchService featureSwitchService,
            IEmailSendingEligibilityService emailSendingEligibilityService
        )
        {
            _configuration = emailConfiguration;
            _dataAccess = dataAccess;
            _messaging = messaging;
            _deliveryApiService = deliveryApiService;
            _communicationApiService = communicationApiService;
            _pigeonHelper = pigeonHelper;
            _wlHelperService = wlHelperService;
            _whiteLabelConfigService = whiteLabelConfigService;
            _featureSwitchService = featureSwitchService;
            _emailSendingEligibilityService = emailSendingEligibilityService;
        }

        public virtual ProcessSendingEmailResponse ProcessSendingEmail(
            int bookingId,
            int? dmcType,
            int dmcId,
            int moduleId,
            int templateId,
            int cuscoTemplateId,
            int priority,
            int emailFormat,
            int languageId,
            ConstantEnum.MailTemplateTo mailTemplateTo,
            IBookingContactFromContactsApi bookingContact,
            NotificationChannel notificationChannel = null,
            string name = null,
            string address = null,
            Dictionary<string, string> specialTags = null,
            int workflowStateId = 0)
        {
            var processSendingEmailStat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            processSendingEmailStat.BeginTrack();
            try
            {
                var emailGuid = Guid.Empty;
                var emailRemark = string.Empty;
                var emailSentBy = Enum.EmailSentBy.Default.ToString();
                var loginType = bookingContact.PaxContact.LoginTypes;
                var actionResult = EmailActionResult.TechnicalError;
                var notAllowedCuscoTemplateIdsForEmailLogin = _configuration.NotAllowedCuscoTemplateIdsForEmailLogin;
                var isCusCoEmail =
                    isCuscoFlagEnabled(cuscoTemplateId, loginType, notAllowedCuscoTemplateIdsForEmailLogin);

                switch (mailTemplateTo)
                {
                    case ConstantEnum.MailTemplateTo.Custom:
                        if (!string.IsNullOrEmpty(notificationChannel?.Value))
                        {
                            var emailResponse = GetContentAndSendEmail(bookingId,
                                notificationChannel?.Value,
                                bookingContact.PaxContact.Phone,
                                bookingContact.PaxContact.Fax,
                                languageId, moduleId, templateId, cuscoTemplateId, priority, emailFormat,
                                isCusCoEmail, loginType, bookingContact.HotelContact.Id,
                                name, address, specialTags,
                                notificationChannel?.Value,
                                bookingContact.MemberId,
                                workflowStateId);
                            emailGuid = emailResponse.RequestId;
                            emailRemark = emailResponse.Remark;
                            emailSentBy = emailResponse.SentBy;

                            if (emailGuid != Guid.Empty || EmailUtil.IsSkippedSendingTemplate(templateId))
                            {
                                actionResult = EmailActionResult.Success;
                            }
                        }
                        else
                        {
                            actionResult = EmailActionResult.NoEmail;
                            emailRemark = "No email or phone number found for Custom email.";
                        }

                        break;
                    case ConstantEnum.MailTemplateTo.Customer:
                        if (!string.IsNullOrEmpty(bookingContact.PaxContact.Email))
                        {
                            var emailResponse = GetContentAndSendEmail
                            (
                                bookingId,
                                bookingContact.PaxContact.Email,
                                bookingContact.PaxContact.Phone,
                                bookingContact.PaxContact.Fax,
                                languageId, moduleId, templateId, cuscoTemplateId, priority, emailFormat,
                                isCusCoEmail, loginType, bookingContact.HotelContact.Id, name,
                                address, specialTags,
                                notificationChannel?.Value, bookingContact.MemberId,
                                workflowStateId
                            );

                            emailGuid = emailResponse.RequestId;
                            emailRemark = emailResponse.Remark;
                            emailSentBy = emailResponse.SentBy;

                            if (emailGuid != Guid.Empty || EmailUtil.IsSkippedSendingTemplate(templateId))
                            {
                                actionResult = EmailActionResult.Success;
                            }
                        }
                        else
                        {
                            actionResult = EmailActionResult.NoEmail;
                            emailRemark = "No email or phone number found for Customer email.";
                        }

                        break;
                    case ConstantEnum.MailTemplateTo.DMC:
                    case ConstantEnum.MailTemplateTo.Hotel:
                        var isSendHotelInfo = dmcType == 2 || dmcId == (int)CommonEnum.DmcId.YCS;
                        var dmcEmail = GetDmcEmail(bookingContact, notificationChannel, isSendHotelInfo);

                        var dmcLanguage = isSendHotelInfo
                            ? bookingContact.HotelContact.LanguageId
                            : 1;

                        if (dmcId == (int)CommonEnum.DmcId.Bcom)
                            // For all Bcom booking (especially BMP case - MerchantCommission),
                            // we won't send any email to Booking.com and Hotels
                        {
                            actionResult = EmailActionResult.NoEmail;
                            emailRemark = "Sending email to DMC is disabled for BCOM booking";
                        }
                        else
                        {
                            var emailResponse = GetContentAndSendEmail
                            (
                                bookingId, dmcEmail, "", "", dmcLanguage,
                                moduleId, templateId, cuscoTemplateId, priority, emailFormat, isCusCoEmail, loginType,
                                bookingContact.HotelContact.Id, name, address, specialTags, notificationChannel?.Value,
                                workflowStateId: workflowStateId
                            );
                            emailGuid = emailResponse.RequestId;
                            emailRemark = emailResponse.Remark;
                            emailSentBy = emailResponse.SentBy;
                            if (emailGuid != Guid.Empty || EmailUtil.IsSkippedSendingTemplate(templateId))
                            {
                                actionResult = EmailActionResult.Success;
                            }

                            if (emailGuid == Guid.Empty && emailResponse.IsSkippedPigeonResponse)
                            {
                                actionResult = EmailActionResult.NoEmail;
                            }
                        }

                        break;
                }

                /*
                 * isCuscoEmail - traditional cusco with cusco_template_id
                 * Pigeon - also send via Cusco behind the scene
                 */
                var isSentViaCusCo = isCusCoEmail || emailSentBy == Enum.EmailSentBy.Pigeon.ToString();

                processSendingEmailStat.Tags["is_success"] =
                    actionResult == EmailActionResult.Success ? true.ToString() : false.ToString();
                processSendingEmailStat.Tags["is_cusco_email"] = isSentViaCusCo.ToString();
                processSendingEmailStat.Tags["dmc_id"] = dmcId.ToString();
                processSendingEmailStat.Tags["dmc_type"] = dmcType.GetValueOrDefault(0).ToString();
                processSendingEmailStat.Tags["email_format"] = emailFormat.ToString();
                processSendingEmailStat.Tags["language_id"] = languageId.ToString();
                processSendingEmailStat.Tags["module_id"] = moduleId.ToString();
                processSendingEmailStat.Tags["template_id"] = templateId.ToString();
                processSendingEmailStat.Tags["mail_template_to"] = mailTemplateTo.ToString();
                processSendingEmailStat.Tags["cusco_template_id"] = cuscoTemplateId.ToString();
                processSendingEmailStat.Tags["valid_login_type"] =
                    loginType.Contains(LoginType.PhoneNumber) ? true.ToString() : false.ToString();
                processSendingEmailStat.Tags["not_allowed_cusco_template_id"] =
                    notAllowedCuscoTemplateIdsForEmailLogin.Contains(cuscoTemplateId)
                        ? true.ToString()
                        : false.ToString();
                processSendingEmailStat.Tags["email_sent_by"] = emailSentBy;
                processSendingEmailStat.Tags["action_result"] = actionResult.ToString();
                processSendingEmailStat.EndTrack(Enum.Measurement.ProcessSendingEmail);
                return new ProcessSendingEmailResponse(emailGuid, actionResult, isSentViaCusCo, emailRemark);
            }
            catch (Exception ex)
            {
                processSendingEmailStat.EndTrack(Enum.Measurement.ProcessSendingEmail, false);
                throw;
            }
        }

        public bool isCuscoFlagEnabled(int cuscoTemplateId, List<LoginType> loginType,
            List<int> notAllowedCuscoTemplateIdsForEmailLogin)
        {
            return cuscoTemplateId > 0 && _configuration.IsEnableUseCusCo &&
                   (loginType.Contains(LoginType.PhoneNumber) ||
                    !notAllowedCuscoTemplateIdsForEmailLogin.Contains(cuscoTemplateId));
        }

        public virtual ProcessSendingEmailResponse ProcessSendingEmail(IEmailDetailInfo emailDetailInfo,
            IBookingContactFromContactsApi bookingContact, string hermesRemark)
        {
            try
            {
                return ProcessSendingEmail(emailDetailInfo.BookingId,
                    emailDetailInfo.DmcType,
                    emailDetailInfo.DmcId,
                    emailDetailInfo.WorkflowParameter.ModuleId,
                    emailDetailInfo.WorkflowParameter.TemplateId,
                    emailDetailInfo.CusCoTemplateId,
                    emailDetailInfo.WorkflowParameter.Priority,
                    emailDetailInfo.WorkflowParameter.EmailFormat,
                    emailDetailInfo.LanguageId,
                    emailDetailInfo.MailTemplateTo,
                    bookingContact,
                    workflowStateId: emailDetailInfo.WorkflowStateId);
            }
            catch (Exception ex)
            {
                throw new SendEmailFailException(ex, hermesRemark);
            }
        }

        public ProcessSendingEmailResponse ProcessSendingEmail(ResendEmailMessage message,
            IBookingItem bookingInfo, int contactMethodId, ConstantEnum.MailTemplateTo mailTemplateTo,
            int cuscoTemplateId,
            string name, string address)
        {
            var bookingContact = _dataAccess.GetBookingContact(
                bookingId: message.BookingId,
                whitelabelId: _wlHelperService.ResolveWhiteLabelId(bookingInfo.Id, bookingInfo.WhitelabelId),
                dmcType: bookingInfo.DmcType,
                contactMethodId: contactMethodId,
                mailTemplateTo: mailTemplateTo,
                mailTemplateId: message.TemplateId
            );

            return ProcessSendingEmail(
                message.BookingId,
                bookingInfo.DmcType,
                bookingInfo.DmcId,
                message.ModuleId,
                message.TemplateId,
                cuscoTemplateId,
                message.Priority,
                message.EmailFormat,
                bookingInfo.LanguageId,
                mailTemplateTo,
                bookingContact);
        }

        public ProcessSendingEmailResponse ProcessSendingEmail(ResendEmailMessage message,
            IBookingItem bookingInfo,
            int contactMethodId,
            ConstantEnum.MailTemplateTo mailTemplateTo,
            int cuscoTemplateId,
            NotificationChannel notificationChannel,
            string name,
            string address,
            Dictionary<string, string> specialTags,
            int? templateLanguageId = null)
        {
            var bookingContact = _dataAccess.GetBookingContact(
                bookingId: message.BookingId,
                whitelabelId: _wlHelperService.ResolveWhiteLabelId(bookingInfo.Id, bookingInfo.WhitelabelId),
                dmcType: bookingInfo.DmcType,
                contactMethodId: contactMethodId,
                mailTemplateTo: mailTemplateTo,
                mailTemplateId: message.TemplateId
            );

            return ProcessSendingEmail(
                message.BookingId,
                bookingInfo.DmcType,
                bookingInfo.DmcId,
                message.ModuleId,
                message.TemplateId,
                cuscoTemplateId,
                message.Priority,
                message.EmailFormat,
                templateLanguageId ?? bookingInfo.LanguageId,
                mailTemplateTo,
                bookingContact,
                notificationChannel,
                name,
                address,
                specialTags);
        }

        public ProcessSendingEmailResponse ProcessSendingEmailAlert(int bookingId, string mailTo, int languageId,
            int sendModuleId,
            int templateId, int sendPriority, int sendEmailFormat, int cuscoTemplateId)
        {
            var processSendingEmailStat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            processSendingEmailStat.BeginTrack();

            try
            {
                var isCusCoEmail = isCuscoFlagEnabled(cuscoTemplateId, new List<LoginType>(), new List<int>());
                var emailResponse = GetContentAndSendEmail
                (
                    bookingId, mailTo, string.Empty, string.Empty,
                    languageId, sendModuleId, templateId, cuscoTemplateId,
                    sendPriority, sendEmailFormat, isCusCoEmail,
                    null, 0, null,
                    null, null, null
                );
                var emailGuid = emailResponse.RequestId;
                var emailRemark = emailResponse.Remark;
                EmailActionResult actionResult;
                if (emailGuid == Guid.Empty)
                {
                    actionResult = EmailActionResult.TechnicalError;
                    processSendingEmailStat.EndTrack(Enum.Measurement.ProcessSendingEmailAlert, false);
                }
                else
                {
                    actionResult = EmailActionResult.Success;
                    processSendingEmailStat.EndTrack(Enum.Measurement.ProcessSendingEmailAlert);
                }

                return new ProcessSendingEmailResponse(emailGuid, actionResult, isCusCoEmail, emailRemark);
            }
            catch (Exception ex)
            {
                processSendingEmailStat.EndTrack(Enum.Measurement.ProcessSendingEmailAlert, false);
                throw;
            }
        }

        private EmailResponse SendEmail(int bookingId,
            string mailTo,
            string smsTo,
            string faxTo,
            int languageId,
            int sendModuleId,
            int templateId,
            int sendPriority,
            int sendEmailFormat,
            int cuscoTemplateId,
            Dictionary<string, string> cuscoTemplatePlaceholders,
            bool isCusCoEmail,
            List<LoginType> loginType,
            string wlToken,
            IBookingItem bookingItem,
            string forceRecipient,
            string name,
            string address,
            int memberId = 0,
            int workflowStateId = 0)
        {
            var sendEmailMeasurement = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            sendEmailMeasurement.BeginTrack();

            var sendEmailTagBuilder = new TagBuilder().AddBookingTag(bookingId);
            sendEmailTagBuilder.Add(new StringTag("TemplateId", templateId.ToString()));

            _messaging.ExceptionMessage.Send(
                $"[EmailService] SendEmail Start TemplateId: {templateId}, CuscoTemplateId: {cuscoTemplateId}, BookingId: {bookingId}",
                null,
                LogLevel.INFO,
                sendEmailTagBuilder.Build());


            if (EmailUtil.IsSkippedSendingTemplate(templateId))
            {
                _messaging.ExceptionMessage.Send(
                    $"[EmailService] SendEmail Skip TemplateId: {templateId}, CuscoTemplateId: {cuscoTemplateId}, BookingId: {bookingId}",
                    null,
                    LogLevel.INFO,
                    sendEmailTagBuilder.Build());
                return new EmailResponse(EmailUtil.GetDefaultRequestIdForSkippedEmail(),
                    $"Skipped for TemplateId: {templateId}", Enum.EmailSentBy.Default.ToString());
            }

            /*
             * Check if email should be skipped based on SkipSendEmail feature configuration
             */
            if (_emailSendingEligibilityService.ShouldSkipSendEmail(bookingItem, wlToken, bookingId, templateId))
            {
                var skipTagBuilder = new TagBuilder().AddBookingTag(bookingId);
                skipTagBuilder.Add(new StringTag("TemplateId", templateId.ToString()));
                var skipTags = skipTagBuilder.Build();
                
                _messaging.ExceptionMessage.Send(
                    $"[SkipSendEmail] Email sending skipped for TemplateId: {templateId}, BookingId: {bookingId}",
                    null,
                    LogLevel.INFO,
                    skipTags);
                    
                return new EmailResponse(EmailUtil.GetDefaultRequestIdForSkippedEmail(), "Email sending skipped by SkipSendEmail feature",
                    Enum.EmailSentBy.Default.ToString());
            }

            /*
             * Send via Pigeon
             * - We need to ensure with success & CusCoRequestId when it is sent via Pigeon
             * - Otherwise we fallback to send with traditional ways (CusCo or EmailService)
             */
            var pigeonResponse = _pigeonHelper.Send(templateId, memberId, languageId, bookingItem, forceRecipient, name,
                address, wlToken, workflowStateId);
            if (pigeonResponse != null)
            {
                var pigeonTagBuilder = new TagBuilder().AddBookingTag(bookingId);
                pigeonTagBuilder.Add(new StringTag("SentVia", Enum.Measurement.SendEmailViaPigeon.ToString()));
                pigeonTagBuilder.Add(new StringTag("TemplateId", templateId.ToString()));
                pigeonTagBuilder.Add(new StringTag("CuscoTemplateId", cuscoTemplateId.ToString()));
                var pigeonTags = pigeonTagBuilder.Build();
                if (pigeonResponse.Success && pigeonResponse.IsSentByPigeon)
                {
                    _messaging.ExceptionMessage.Send(
                        $"[Pigeon] SendEmail Success CuscoTemplateId: {cuscoTemplateId}, BookingId: {bookingId}", null,
                        LogLevel.INFO, pigeonTags);
                    sendEmailMeasurement.EndTrack(Enum.Measurement.SendEmailViaPigeon);
                    return new EmailResponse(new Guid(pigeonResponse.CuscoRequestId), "Sent by Pigeon",
                        Enum.EmailSentBy.Pigeon.ToString());
                }

                // SkipGate for Pigeon case by intention
                if (pigeonResponse.IsSkipped)
                {
                    var pigeonStatusCode =
                        (Enum.PigeonStatusCode)System.Enum.ToObject(typeof(Enum.PigeonStatusCode),
                            pigeonResponse.StatusCode);
                    _messaging.ExceptionMessage.Send(
                        $"[Pigeon] SendEmail {pigeonStatusCode.ToString()} TemplateId: {cuscoTemplateId}, BookingId: {bookingId}",
                        null, LogLevel.INFO, pigeonTags);
                    sendEmailMeasurement.EndTrack(Enum.Measurement.SendEmailViaPigeon);
                    return new EmailResponse(Guid.Empty, pigeonStatusCode.ToString(),
                        Enum.EmailSentBy.Pigeon.ToString(), true);
                }

                // TODO Remove when migrate Pigeon to async flow
                // Pigeon failed to build, and we don't want to fall back to Cusco.
                if (pigeonResponse.StatusCode == (int)Enum.PigeonStatusCode.FailInternalError && _configuration.IsEnabledFailedInPigeon)
                {
                    _messaging.ExceptionMessage.Send($"[Pigeon] SendEmail Failed No FallBack TemplateId: {templateId}, CuscoTemplateId: {cuscoTemplateId}, BookingId: {bookingId}", null, LogLevel.WARN, pigeonTags);
                    sendEmailMeasurement.EndTrack(Enum.Measurement.SendEmailViaPigeon);
                    return new EmailResponse(EmailUtil.GetDefaultRequestIdForFailedPigeonEmail(), "Pigeon Failed to build", Enum.EmailSentBy.Pigeon.ToString(), true);
                }

                // If not match it will go down to Cusco & WinEmail (No expectation)
                _messaging.ExceptionMessage.Send(
                    $"[Pigeon] SendEmail skip with CuscoTemplateId: {cuscoTemplateId}, BookingId: {bookingId}, PigeonResponse: {pigeonResponse}",
                    null, LogLevel.INFO, pigeonTags);
            }

            /*
             * Collect shouldFallbackAfterPigeonFailed and default value is true
             */
            bool shouldFallbackAfterPigeonFailed = GetShouldFallbackAfterPigeonFailed(bookingItem, wlToken, bookingId, templateId);

            /*
             * Send via either Cusco or EmailService (agoda.gitlab.com/ebe/email-systems)
             * - This logic is decided to send email either Cusco or EmailService
             * - It checked that if your template contained cuscoTemplateId or not and process respectively
             * - If shouldFallbackAfterPigeonFailed is false, it will not send via Cusco
             */
            if (isCusCoEmail && bookingItem?.WhitelabelId != null && shouldFallbackAfterPigeonFailed)
            {
                var cuscoTagBuilder = new TagBuilder().AddBookingTag(bookingId);
                cuscoTagBuilder.Add(new StringTag("SentVia", Enum.Measurement.CusCoSend.ToString()));
                cuscoTagBuilder.Add(new StringTag("TemplateId", templateId.ToString()));
                cuscoTagBuilder.Add(new StringTag("CuscoTemplateId", cuscoTemplateId.ToString()));
                var cuscoTags = cuscoTagBuilder.Build();
                _messaging.ExceptionMessage.Send(
                    $"[Cusco] SendEmail with TemplateId: {templateId}, CuscoTemplateId: {cuscoTemplateId}, BookingId: {bookingId}",
                    null, LogLevel.INFO, cuscoTags);
                return SendEmailViaCusco(bookingId, mailTo, smsTo, cuscoTemplateId, cuscoTemplatePlaceholders,
                    loginType, wlToken, bookingItem, memberId);
            }
            else
            {
                // This supposed to be deprecated so we make throw exception and logging to capture malicious emails that are not migrated.
                var winEmailTagBuilder = new TagBuilder().AddBookingTag(bookingId);
                winEmailTagBuilder.Add(new StringTag("SentVia", Enum.Measurement.SendEmail.ToString()));
                winEmailTagBuilder.Add(new StringTag("TemplateId", templateId.ToString()));
                var winEmailTags = winEmailTagBuilder.Build();
                if (bookingItem?.WhitelabelId != null &&
                    CommonEnum.IsUsBankGroup((CommonEnum.WhitelabelId)bookingItem.WhitelabelId))
                {
                    _messaging.ExceptionMessage.Send(
                        $"SendEmail for USBANK group WL bookings with TemplateId: {templateId}, BookingId: {bookingId}.",
                        null, LogLevel.ERROR, winEmailTags);
                }
                else if (bookingItem?.WhitelabelId != null &&
                         _whiteLabelConfigService.IsRurubuWl(bookingItem.WhitelabelId.Value, bookingItem.IsTestBooking))
                {
                    _messaging.ExceptionMessage.Send(
                        $"SendEmail for JTB group WL bookings with TemplateId: {templateId}, BookingId: {bookingId}.",
                        null, LogLevel.ERROR, winEmailTags);
                }
                else
                {
                    _messaging.ExceptionMessage.Send(
                        $"[WinEmail] SendEmail with TemplateId: {templateId}, BookingId: {bookingId} - It should be deprecated!!",
                        null, LogLevel.ERROR, winEmailTags);
                }

                throw new SendViaDeprecatedWinEmailException(templateId, bookingId);
            }
        }

        private EmailResponse SendEmailViaCusco(
            int bookingId,
            string mailTo,
            string smsTo,
            int cuscoTemplateId,
            Dictionary<string, string> cuscoTemplatePlaceholders,
            List<LoginType> loginType,
            string wlToken,
            IBookingItem bookingItem,
            int memberId = 0)
        {
            var tagBuilder = new TagBuilder().AddBookingTag(bookingId);
            tagBuilder.Add(new StringTag("SentVia", Enum.Measurement.CusCoSend.ToString()));
            var tags = tagBuilder.Build();
            try
            {
                if (EmailUtil.IsSkippedSendingEmailViaCusco(cuscoTemplateId))
                {
                    _messaging.ExceptionMessage.Send(
                        $"[SendEmailViaCusco] Skip CuscoTemplateId: {cuscoTemplateId}, BookingId: {bookingId}",
                        null,
                        LogLevel.INFO,
                        tags);
                    return new EmailResponse(
                        EmailUtil.GetDefaultRequestIdForSkippedEmail(),
                        $"Skipped for CuscoTemplateId: {cuscoTemplateId}",
                        Enum.EmailSentBy.Cusco.ToString());
                }

                if (_configuration.IsCommunicationApiEnabled)
                {
                    var tagBuilderCommunicationApi = new TagBuilder().AddBookingTag(bookingId);
                    tagBuilderCommunicationApi.Add(new StringTag("SentVia", Enum.Measurement.CusCoSendCommunication.ToString()));
                    tags = tagBuilderCommunicationApi.Build();
                    var communicationApiResponse = _communicationApiService.Send(
                        cuscoTemplatePlaceholders,
                        mailTo,
                        cuscoTemplateId,
                        wlToken,
                        bookingItem,
                        bookingId,
                        memberId
                    );
                    _messaging.ExceptionMessage.Send(
                        $"SendEmail via CommunicationAPI Success CuscoTemplateId: {cuscoTemplateId}, BookingId: {bookingId}",
                        null,
                        LogLevel.INFO,
                        tags);
                    return communicationApiResponse;
                }

                _messaging.ExceptionMessage.Send(
                    $"[SendEmailViaCusco] fall back to DeliverAPI on CuscoTemplateId: {cuscoTemplateId}, BookingId: {bookingId}",
                    null,
                    LogLevel.WARN,
                    tags);

                var deliveryApiResponse = _deliveryApiService.Send(
                    cuscoTemplatePlaceholders, mailTo, smsTo, cuscoTemplateId, loginType, wlToken, bookingItem, memberId);
                _messaging.ExceptionMessage.Send(
                    $"SendEmail via DeliveryAPI Success CuscoTemplateId: {cuscoTemplateId}, BookingId: {bookingId}",
                    null,
                    LogLevel.INFO,
                    tags);
                return deliveryApiResponse;
            }
            catch (Exception ex)
            {
                _messaging.ExceptionMessage.Send(
                    $"SendEmail Failed CuscoTemplateId: {cuscoTemplateId}, BookingId: {bookingId}",
                    ex,
                    LogLevel.FATAL,
                    tags);
                throw;
            }
        }

        private EmailResponse GetContentAndSendEmail(
            int bookingId,
            string mailTo,
            string smsTo,
            string faxTo,
            int languageId,
            int sendModuleId,
            int templateId,
            int cuscoTemplateId,
            int sendPriority,
            int sendEmailFormat,
            bool isCusCoEmail,
            List<LoginType> loginType,
            int hotelId,
            string name,
            string address,
            Dictionary<string, string> specialTags,
            string forceRecipient,
            int memberId = 0,
            int workflowStateId = 0)
        {
            var sendEmailMeasurement = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            sendEmailMeasurement.BeginTrack();
            try
            {
                var bookingItem = _dataAccess.GetBookingInfo(bookingId);

                var cuscoPlaceHolder = GetCuscoPlaceHolderData(bookingId, languageId, memberId, hotelId, bookingItem,
                    name, address);

                string wlToken = null;
                if (IsAgentEmail(cuscoTemplateId))
                {
                    wlToken = _whiteLabelConfigService.GetWhiteLabelKey((int)CommonEnum.WhitelabelId.Agoda);
                }
                else if (bookingItem?.WhitelabelId != null)
                {
                    wlToken = _whiteLabelConfigService.GetWhiteLabelKey(bookingItem.WhitelabelId.Value);
                }

                var mailResponse = SendEmail(bookingId, mailTo, smsTo, faxTo, languageId, sendModuleId, templateId,
                    sendPriority, sendEmailFormat, cuscoTemplateId, cuscoPlaceHolder,
                    isCusCoEmail, loginType, wlToken, bookingItem, forceRecipient, name, address, memberId,
                    workflowStateId);

                sendEmailMeasurement.EndTrack(Enum.Measurement.GetContentAndSendEmailProcess);

                if (IsMeasurementEmailTemplate(templateId))
                {
                    var bookingMeasurementList = _dataAccess.GetBookingMeasurementByBookingId(bookingId);
                    if (bookingMeasurementList.Any())
                    {
                        MeasureBooking(bookingMeasurementList, templateId);
                    }
                }

                return mailResponse;
            }
            catch (Exception ex)
            {
                _messaging.ExceptionMessage.Send(
                    $"SendEmail Failed. BookingId: {bookingId}," +
                    $"TemplateId: {templateId}", ex, LogLevel.FATAL,
                    new TagBuilder().AddBookingTag(bookingId).Build());
                sendEmailMeasurement.EndTrack(Enum.Measurement.GetContentAndSendEmailProcess, false);
                throw;
            }
        }

        private bool IsMeasurementEmailTemplate(int templateId)
        {
            return _configuration.MeasurementEmailTemplateIds.Contains(templateId);
        }

        private void MeasureBooking(List<IBookingMeasurement> bookingMeasurementList, int templateId)
        {
            var bookingMeasurement = bookingMeasurementList[0];

            if (bookingMeasurementList.Count > 1)
            {
                var creditCardMeasurement = bookingMeasurementList.Find(x => x.PaymentMethod != 0);

                if (creditCardMeasurement != null)
                {
                    bookingMeasurement = creditCardMeasurement;
                }
            }

            var diffInSeconds = (DateTime.Now - bookingMeasurementList[0].BookingCreatedDate).TotalSeconds;
            var seconds = Convert.ToInt32(diffInSeconds);

            TrackCreateBookingTimeValue(bookingMeasurement, seconds, templateId);
        }

        private void TrackCreateBookingTimeValue(IBookingMeasurement bookingMeasurement, int seconds, int templateId)
        {
            var tracker = _messaging.MeasurementMessageFactory.CreateNewMeasurement();

            tracker.Tags[CHARGE_OPTION] = bookingMeasurement.ChargeOption.ToString();
            tracker.Tags[CHARGE_OPTION_NAME] = bookingMeasurement.ChargeOptionName;

            tracker.Tags[PAYMENT_MODEL] = bookingMeasurement.PaymentModel.ToString();
            tracker.Tags[PAYMENT_MODEL_NAME] = bookingMeasurement.PaymentModelName;

            tracker.Tags[DMC] = bookingMeasurement.DMC.ToString();
            tracker.Tags[DMC_CODE] = bookingMeasurement.DMCCode;
            tracker.Tags[DMC_NAME] = bookingMeasurement.DMCName;

            tracker.Tags[PAYMENT_CATEGORY] = bookingMeasurement.PaymentCategory.ToString();
            tracker.Tags[PAYMENT_CATEGORY_NAME] = bookingMeasurement.PaymentCategoryName;

            tracker.Tags[PAYMENT_METHOD] = bookingMeasurement.PaymentMethod.ToString();
            tracker.Tags[PAYMENT_METHOD_NAME] = bookingMeasurement.PaymentMethodName;

            tracker.Tags[GATEWAY] = bookingMeasurement.Gateway.ToString();
            tracker.Tags[GATEWAY_NAME] = bookingMeasurement.GatewayName;

            tracker.Tags[CEG_MANUAL] = bookingMeasurement.CEGManual > 0 ? "1" : "0";
            tracker.Tags[TEMPLATE] = templateId.ToString();
            tracker.Tags[STOREFRONT_ID] = bookingMeasurement.StorefrontId.ToString();
            tracker.Tags[AFFILIATE_PAYMENT_METHOD] = bookingMeasurement.AffiliatePaymentMethod.ToString();
            tracker.Tags[BOOKING_TYPE] = bookingMeasurement.BookingType.ToString();
            tracker.Tags[AFFILIATE_MODEL] = bookingMeasurement.AffiliateModel.ToString();
            tracker.Tags[AVAILABILITY_TYPE] = bookingMeasurement.AvailabilityType.ToString();

            tracker.TrackValue((int)Enum.Measurement.CreateBookingTime, Enum.Measurement.CreateBookingTime.ToString(),
                seconds);
        }

        public Dictionary<string, string> GetCuscoPlaceHolderData(int bookingId, int languageId, int memberId,
            int hotelId, IBookingItem bookingItem, string name, string address)
        {
            var placeholders = new Dictionary<string, string>
            {
                { "bookingId", bookingId.ToString() },
                { "languageId", languageId.ToString() },
                { "memberId", memberId.ToString() },
                { "hotelId", hotelId.ToString() }
            };
            if (bookingItem?.WhitelabelId != null)
            {
                placeholders.Add("whiteLabelId", bookingItem.WhitelabelId.ToString());
            }

            if (name != null)
            {
                placeholders.Add("cname", name);
            }

            if (address != null)
            {
                placeholders.Add("caddr", address);
            }

            if (bookingItem?.DmcDueDate != null)
            {
                // For ACR email template https://llama.agoda.local/#/template/6474?view=true
                placeholders.Add("resellDeadline", bookingItem.DmcDueDate?.ToString("yyyy-MM-dd"));
            }

            return placeholders;
        }

        private string GetDmcEmail(IBookingContactFromContactsApi bookingContact,
            NotificationChannel notificationChannel, bool isSendHotelInfo)
        {
            if (notificationChannel != null
                && notificationChannel.NotificationType == NotificationType.Email)
            {
                return notificationChannel.Value;
            }

            var toReturn = isSendHotelInfo
                ? bookingContact.HotelContact.Email
                : bookingContact.SupplierContact.Email;

            return toReturn;
        }

        private bool IsAgentEmail(int cuscoTemplateId)
        {
            return Enum.AgentEmail.Contains(cuscoTemplateId);
        }

        private PigeonEmailTemplate GetPigeonEmailTemplateConfig(string wlToken, bool? isTestBooking, int bookingId, int templateId)
        {
            try
            {
                var templateName = GetPigeonTemplateName(templateId, bookingId);
                if (string.IsNullOrEmpty(templateName))
                {
                    return null;
                }

                if (!IsPigeonFeatureEnabled(wlToken, isTestBooking, bookingId))
                {
                    return null;
                }

                var pigeonEmailListConfig = _whiteLabelConfigService.GetFeatureConfigByKey<PigeonEmailListFeatureConfig>(
                    wlToken, PigeonEmailListFeatureConfig.FEATURE_NAME, isTestBooking);

                return FindMatchingPigeonTemplate(pigeonEmailListConfig, templateName, templateId, bookingId);
            }
            catch (Exception ex)
            {
                _messaging.ExceptionMessage.Send(
                $"[PigeonEmailList] Failed to retrieve configuration for TemplateId: {templateId}, BookingId: {bookingId}",
                ex,
                LogLevel.WARN,
                new TagBuilder().AddBookingTag(bookingId).Build());
                return null;
            }
        }

        private string GetPigeonTemplateName(int templateId, int bookingId)
        {
            var (templateName, _) = PigeonTemplateData.GetTemplateNameAndType(templateId);
            
            if (string.IsNullOrEmpty(templateName))
            {
                _messaging.ExceptionMessage.Send(
                    $"[PigeonEmailList] TemplateId {templateId} not found in PigeonTemplateData, BookingId: {bookingId}",
                    null,
                    LogLevel.INFO,
                    new TagBuilder().AddBookingTag(bookingId).Build());
                return null;
            }

            return templateName;
        }

        private bool IsPigeonFeatureEnabled(string wlToken, bool? isTestBooking, int bookingId)
        {
            if (!string.IsNullOrEmpty(wlToken) && Guid.TryParse(wlToken, out var whiteLabelKey))
            {
                var isFeatureEnabled = _whiteLabelConfigService.IsFeatureEnabled(
                    PigeonEmailListFeatureConfig.FEATURE_NAME, 
                    whiteLabelKey, 
                    isTestBooking, 
                    null);
                
                if (!isFeatureEnabled)
                {
                    _messaging.ExceptionMessage.Send(
                        $"[PigeonEmailList] Feature '{PigeonEmailListFeatureConfig.FEATURE_NAME}' is not enabled for BookingId: {bookingId}",
                        null,
                        LogLevel.INFO,
                        new TagBuilder().AddBookingTag(bookingId).Build());
                    return false;
                }

                return true;
            }
            else
            {
                _messaging.ExceptionMessage.Send(
                    $"[PigeonEmailList] Invalid or empty wlToken: {wlToken}, BookingId: {bookingId}",
                    null,
                    LogLevel.INFO,
                    new TagBuilder().AddBookingTag(bookingId).Build());
                return false;
            }
        }

        private PigeonEmailTemplate FindMatchingPigeonTemplate(PigeonEmailListFeatureConfig pigeonEmailListConfig, string templateName, int templateId, int bookingId)
        {
            var matchingTemplates = pigeonEmailListConfig?.AllowedTemplates?
                .Where(t => string.Equals(t.TemplateName, templateName, StringComparison.OrdinalIgnoreCase))
                .ToList() ?? new List<PigeonEmailTemplate>();
            
            var firstMatchingTemplate = matchingTemplates.FirstOrDefault();
            
            _messaging.ExceptionMessage.Send(
                $"[PigeonEmailList] Template config for '{templateName}' (TemplateId: {templateId}), BookingId: {bookingId}, " +
                $"MatchingCount: {matchingTemplates.Count}, " +
                $"FirstConfig: {(firstMatchingTemplate != null ? $"TemplateName='{firstMatchingTemplate.TemplateName}', ExperimentName='{firstMatchingTemplate.ExperimentName}', ShouldFallback={firstMatchingTemplate.ShouldFallback}" : "null")}",
                null,
                LogLevel.INFO,
                new TagBuilder().AddBookingTag(bookingId).Build());
            
            return firstMatchingTemplate;
        }

        private bool GetShouldFallbackAfterPigeonFailed(IBookingItem bookingItem, string wlToken, int bookingId, int templateId)
        {
            if (bookingItem?.WhitelabelId == null || string.IsNullOrEmpty(wlToken))
            {
                return DEFAULT_SHOULD_FALLBACK_AFTER_PIGEON_FAILED;
            }

            var templateConfig = GetPigeonEmailTemplateConfig(wlToken, bookingItem.IsTestBooking, bookingId, templateId);
            return templateConfig?.ShouldFallback ?? DEFAULT_SHOULD_FALLBACK_AFTER_PIGEON_FAILED;
        }
    }
}