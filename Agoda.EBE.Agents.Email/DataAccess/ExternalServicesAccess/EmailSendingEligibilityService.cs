using System;
using System.Collections.Generic;
using System.Linq;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess.Interface;
using Agoda.EBE.Agents.Email.Object.Configuration;
using Agoda.EBE.Agents.Email.Object.Configuration.FeatureSwitches;
using Agoda.EBE.Agents.Email.Object.Interface;
using Agoda.EBE.Agents.Email.Service.Utils;
using Agoda.EBE.Framework.Messaging;

namespace Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess
{
    /// <summary>
    /// Service responsible for determining email sending eligibility based on whitelabel configuration
    /// Handles SkipSendEmail feature configuration and template matching logic
    /// </summary>
    public class EmailSendingEligibilityService : IEmailSendingEligibilityService
    {
        private readonly IWhiteLabelConfigService _whiteLabelConfigService;
        private readonly IMessaging _messaging;

        public EmailSendingEligibilityService(
            IWhiteLabelConfigService whiteLabelConfigService,
            IMessaging messaging)
        {
            _whiteLabelConfigService = whiteLabelConfigService ?? throw new ArgumentNullException(nameof(whiteLabelConfigService));
            _messaging = messaging ?? throw new ArgumentNullException(nameof(messaging));
        }

        public bool ShouldSkipSendEmail(IBookingItem? bookingItem, string wlToken, int bookingId, int templateId)
        {
            if (bookingItem?.WhitelabelId == null || string.IsNullOrEmpty(wlToken))
            {
                return false; // Default is false - don't skip
            }

            // Check if template name is valid
            var templateName = GetPigeonTemplateName(templateId, bookingId);
            if (string.IsNullOrEmpty(templateName) || !IsSkipSendEmailFeatureEnabled(wlToken, bookingItem.IsTestBooking, bookingId))
            {
                return false; // Default is false - don't skip
            }

            // Check if feature is enabled
            var skipSendEmailConfig = _whiteLabelConfigService.GetFeatureConfigByKey<SkipSendEmailFeatureConfig?>(
                wlToken, SkipSendEmailFeatureConfig.FEATURE_NAME, bookingItem.IsTestBooking);
            if (skipSendEmailConfig == null) return false;

            // Check if template is configured to skip
            var templateConfig = FindMatchingSkipSendEmailTemplate(skipSendEmailConfig, templateName, templateId, bookingId);
            return templateConfig?.ShouldSkip ?? false;
        }

        private bool IsSkipSendEmailFeatureEnabled(string wlToken, bool? isTestBooking, int bookingId)
        {
            if (!Guid.TryParse(wlToken, out var whiteLabelKey))
            {
                _messaging.ExceptionMessage.Send(
                    $"[SkipSendEmail] Invalid wlToken, BookingId: {bookingId}",
                    null,
                    LogLevel.INFO,
                    new TagBuilder().AddBookingTag(bookingId).Build());
                return false;
            }

            var isFeatureEnabled = _whiteLabelConfigService.IsFeatureEnabled(
                SkipSendEmailFeatureConfig.FEATURE_NAME,
                whiteLabelKey,
                isTestBooking,
                null);

            if (!isFeatureEnabled)
            {
                _messaging.ExceptionMessage.Send(
                    $"[SkipSendEmail] Feature '{SkipSendEmailFeatureConfig.FEATURE_NAME}' is not enabled for BookingId: {bookingId}",
                    null,
                    LogLevel.INFO,
                    new TagBuilder().AddBookingTag(bookingId).Build());
                return false;
            }

            return true;
        }

        private SkipSendEmailTemplate? FindMatchingSkipSendEmailTemplate(SkipSendEmailFeatureConfig skipSendEmailConfig, string templateName, int templateId, int bookingId)
        {
            var firstMatchingTemplate = skipSendEmailConfig.AllowedTemplates?
                .FirstOrDefault(t => string.Equals(t.TemplateName, templateName, StringComparison.OrdinalIgnoreCase));
            
            _messaging.ExceptionMessage.Send(
                $"[SkipSendEmail] Template config for '{templateName}' (TemplateId: {templateId}), BookingId: {bookingId}, " +
                $"Found: {(firstMatchingTemplate != null ? $"TemplateName='{firstMatchingTemplate.TemplateName}', ExperimentName='{firstMatchingTemplate.ExperimentName}', ShouldSkip={firstMatchingTemplate.ShouldSkip}" : "null")}",
                null,
                LogLevel.INFO,
                new TagBuilder().AddBookingTag(bookingId).Build());
            
            return firstMatchingTemplate;
        }

        private string? GetPigeonTemplateName(int templateId, int bookingId)
        {
            var (templateName, _) = PigeonTemplateData.GetTemplateNameAndType(templateId);
                
                if (string.IsNullOrEmpty(templateName))
                {
                    _messaging.ExceptionMessage.Send(
                        $"[SkipSendEmail] TemplateId {templateId} not found in PigeonTemplateData, BookingId: {bookingId}",
                        null,
                        LogLevel.INFO,
                        new TagBuilder().AddBookingTag(bookingId).Build());
                    return null;
                }

                return templateName;
        }
    }
} 