using Agoda.EBE.Agents.Email.Object;
using Agoda.EBE.Agents.Email.Object.Interface;
using Com.Agoda.Abs.Protobuf;

namespace Agoda.EBE.Agents.Email.Service.Helpers.Interface
{
    public interface IPigeonHelper
    {
        PigeonResponse Send(int templateId, int memberId, int languageId, IBookingItem bookingItem, string forceRecipient, string name, string address, string wlToken, int workflowStateId = 0);
    }
}