using System.Data;
using Agoda.EBE.Framework.Objects.Email;
using Agoda.Enigma.Client.Models;

namespace Agoda.EBE.Agents.Email.DataAccess.Interface
{
    public interface IEmailContentDal
    {
        TemplateParamsList GetTemplateParamsList(int templateId, int languageId);
        string GetCoBrandingMessage(string cidList, int languageId);
        DataSet GetPaymentMethodByID(int paymentMethodId);
        string GetCreditCardTypeStr(int cardType);
        DataRow[] GetGuestPaxInfo(int bookingId);
        string GetLanguageName(int languageId);
        DataSet GetBookingHistory(int bookingId);
        string GetCancelDate(int bookingId);
        DataSet GetBookingSummary(int bookingId);
        DataSet GetBookingPayments(int bookingId, int languageId);
        BookingDetail GetBookingDetail(int bookingId, int whitelabelId);
    }
}