﻿using System;
using System.Collections.Generic;
using Agoda.EBE.Agents.Email.Object.Interface;
using Agoda.EBE.Agents.Provisioning.Object;
using Agoda.EBE.Framework;
using Agoda.EBE.Framework.EmailContent.Objects.Interface;
using Agoda.EBE.Framework.Objects.Email;

namespace Agoda.EBE.Agents.Email.DataAccess.Interface
{
    public interface IDatabaseAccess
    {     
        IBookingContactFromContactsApi GetBookingContact(int bookingId, int whitelabelId, int? mailTemplateId);
        IBookingContactFromContactsApi GetBookingContact(int bookingId, int whitelabelId, int? dmcType, int contactMethodId = 1,
            ConstantEnum.MailTemplateTo mailTemplateTo = 0, int? mailTemplateId = null);
        List<int> GetBookingListInItinerary(int itineraryId);
        bool IsNeedSendCustomerEmail(int affiliateSiteId);
        bool UpdateItinerary(int itineraryId);
        List<IEmailAlertConfiguration> GetEmailAlertConfigurationList();
        bool InsertBookingEventData(int bookingId, int eventId, string eventData, DateTime eventDate);
        List<IBookingEventDetailItem> GetBookingEventDetailList(int bookingId, int eventId);
        bool IsBookingExist(int bookingId);
        IBookingItem GetBookingInfo(int bookingId);
        IEmailDetailInfo GetEmailDetailInfo(IBookingItem bookingInfo);
        int GetContactMethodId(ConstantEnum.MailTemplateTo mailTemplateTo, int? dmcType, int dmcId);
        List<IBookingMeasurement> GetBookingMeasurementByBookingId(int bookingId);
        int GetBookingPaymentCategory(int bookingId);
        string GetBookingGreetingMessage(int bookingId);
        IPropertyMessageConfiguration GetPropertyMessageConfiguration(int hotelId);
        List<IBookingEventDetailItem> GetBookingEventDetailListByEventId(int eventId);
        int GetBookingTrackerHistoryId(int bookingId);
        ITrackerSettingItem GetBookingVocEmailInfo(int bookingId, int historyId);
        EmailTemplate GetEmailTemplate(int templateId, int languageId = 1);
        IBookingEventDetailItem GetEventDetailByBookingId(int bookingId);
        List<IBookingEmailAlertConfig> GetEmailAlertConfig(int workFlowStateId, int eventId);
        int GetWorkflowActionResultFromBookingHistory(int bookingId, int workflowStateId);
        BookingResell GetBookingResell(Provisioning.Enum.EBEBookingResellType resellTypeId, int bookingId);
        BookingResell GetBookingResellByBookingId(int bookingId);

    }
}