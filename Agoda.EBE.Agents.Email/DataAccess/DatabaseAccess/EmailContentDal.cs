using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Agents.Common.Util.Data;
using Agoda.EBE.Agents.Email.DataAccess.Interface;
using Agoda.EBE.Agents.Email.Object.Interface;
using Agoda.EBE.Framework;
using Agoda.EBE.Framework.Data;
using Agoda.EBE.Framework.Objects.Email;
using System;
using System.Collections.Generic;
using System.Data;
using System.Runtime.Caching;
using Agoda.EBE.Framework.Services.PIIClient;
using Agoda.Enigma.Client.Models;

namespace Agoda.EBE.Agents.Email.DataAccess
{
    public class EmailContentDal : IEmailContentDal
    {
        private readonly MemoryCache _cache;
        private IEmailConfiguration _configuration;
        private readonly IMessaging _messaging;
        private readonly IPIIService _piiService;

        public EmailContentDal(
            IEmailConfiguration emailConfiguration,
            IMessaging messaging, MemoryCache cache, IPIIService piiService)
        {
            _cache = cache;
            _configuration = emailConfiguration;
            _messaging = messaging;
            _piiService = piiService;
        }

        public virtual TemplateParamsList GetTemplateParamsList(int templateId, int languageId)
        {
            var getTemplateParamListStat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            getTemplateParamListStat.BeginTrack();
            
            var cacheKey = $"GetTemplateParamsList(templateId:{templateId},languageId:{languageId})";
            var data = (TemplateParamsList)_cache.Get(cacheKey);
            if (data == null)
            {
                var parameterInfoes = new List<ParameterInfo>
                {
                    new ParameterInfo("@template_id", templateId, SqlDbType.Int),
                    new ParameterInfo("@language_id", languageId, SqlDbType.Int)
                };
                try
                {
                    var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString,
                        "dbo.email_templates_select_v1", parameterInfoes);
                    if (!ds.HasRowInTable(0))
                    {
                        throw new Exception("Execute email_templates_select_v1 return no row in GetTemplateList");
                    }

                    data = new TemplateParamsList(templateId, languageId, ds.Tables[0].Rows[0]);
                    _cache.Add(cacheKey, data, new CacheItemPolicy { AbsoluteExpiration = DateTime.Now.AddMinutes(30) });
                }
                catch (Exception ex)
                {
                    _messaging.LogExceptionWithParameterInfo("GetTemplateParamsList fail", ex, parameterInfoes);
                    throw;
                }
            }
            getTemplateParamListStat.EndTrack(Enum.Measurement.GetTemplateParamList);
            return data;
        }
        
        public virtual string GetCoBrandingMessage(string cidList, int languageId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@get_language", languageId, SqlDbType.Int),
                new ParameterInfo("@object_id", cidList, SqlDbType.Int),
                new ParameterInfo("@Is_affiliate_site_id", 1, SqlDbType.Bit)
            };
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString,
                    "dbo.email_agent_branding_message_select_is_cid_v1", parameterInfos);
                return ds.HasRowInTable(0) ? ds.Tables[0].GetValueOrDefault("message") : string.Empty;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetCoBrandingMessage fail", ex, parameterInfos);
                throw;
            }
        }
        public virtual DataSet GetPaymentMethodByID(int paymentMethodId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@payment_method_id", paymentMethodId, SqlDbType.Int)
            };
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString,
                    "dbo.ebe_afm_payment_methods_select_by_id", parameterInfos);
                return ds;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetPaymentMethodByID fail", ex, parameterInfos);
                throw;
            }
        }
        public string GetCreditCardTypeStr(int cardType)
        {
            var ds = GetPaymentMethodByID(cardType);
            return ds.HasRowInTable(0) ? ds.GetValueOrDefault(0, 0, "payment_method_name") : string.Empty;
        }
        public virtual DataRow[] GetGuestPaxInfo(int bookingId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@booking_id", bookingId, SqlDbType.Int)
            };
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString,
                    "dbo.ebe_booking_pax_select_by_booking_v2", parameterInfos);
                return ds.Tables[0].Select("room_no=1 and guest_no=1", string.Empty);
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetGuestPaxInfo fail", ex, parameterInfos);
                throw;
            }
        }
        public virtual string GetLanguageName(int languageId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@language_id", languageId, SqlDbType.Int)
            };
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString,
                    "dbo.afm_language_select_v1", parameterInfos);
                return ds.HasRowInTable(0) ? ds.Tables[0].Rows[0]["language_name"].ToString() : "English";
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetLanguageName fail", ex, parameterInfos);
                return "English";
            }
        }
        public virtual DataSet GetBookingHistory(int bookingId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@booking_id", bookingId, SqlDbType.Int)
            };
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString,
                    "dbo.ebe_booking_history_select", parameterInfos);
                return ds;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingHistory fail", ex, parameterInfos);
                throw;
            }
        }
        public string GetCancelDate(int bookingId)
        {
            try
            {
                using (var dsHistory = GetBookingHistory(bookingId))
                {
                    // ReSharper disable once InvertIf
                    if (dsHistory.HasRowInTable(0))
                    {
                        var drCancelDate = dsHistory.Tables[0].Select("workflow_action_results_id in (798,799)",
                            "booking_history_id desc");
                        if (drCancelDate.Length > 0)
                        {
                            return ((DateTime)drCancelDate[0]["rec_created_when"]).ToString("MMMM d, yyyy");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetCancelDate fail", ex);
            }
            return string.Empty;
        }
        public virtual DataSet GetBookingSummary(int bookingId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@booking_id", bookingId, SqlDbType.Int)
            };
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString,
                    "dbo.gp_ebe_get_amount_totals_per_booking", parameterInfos);
                return ds;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingSummary fail", ex, parameterInfos);
                throw;
            }
        }
        public virtual DataSet GetBookingPayments(int bookingId, int languageId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@booking_id", bookingId, SqlDbType.Int),
                new ParameterInfo("@get_language", languageId, SqlDbType.Int)
            };
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString,
                    "dbo.ebe_booking_payments_select_by_booking", parameterInfos);
                return ds;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo($"GetBookingPayments fail: {ex.Message}", ex, parameterInfos);
                throw;
            }
        }

        public virtual BookingDetail GetBookingDetail(int bookingId, int whitelabelId)
        {
            return _piiService.GetBookingDetail(bookingId, null, whitelabelId);
        }
    }
}
