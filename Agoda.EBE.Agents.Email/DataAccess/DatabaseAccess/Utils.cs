﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Agoda.EBE.Agents.Email.Object;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Object;

namespace Agoda.EBE.Agents.Email.DataAccess
{
    public static class Utils
    {
        // ReSharper disable once MemberCanBePrivate.Global
        internal static IEnumerable<T> MapTableRowIEnumerable<T>(this DataSet ds, int tableIdx, Func<DataRow, T> rowFn)
        {
            return ds.Tables[tableIdx].Rows
                .Cast<DataRow>()
                .Select(rowFn);
        }

        internal static List<T> MapTableRow<T>(this DataSet ds, int tableIdx, Func<DataRow, T> rowFn)
        {
            return MapTableRowIEnumerable(ds, tableIdx, rowFn).ToList();
        }
    }
}