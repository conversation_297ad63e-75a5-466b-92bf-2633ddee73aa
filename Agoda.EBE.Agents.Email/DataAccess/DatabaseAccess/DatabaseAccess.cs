using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Agents.Common.Util.Data;
using Agoda.EBE.Agents.Email.DataAccess.Interface;
using Agoda.EBE.Agents.Email.Object;
using Agoda.EBE.Agents.Email.Object.Interface;
using Agoda.EBE.Framework;
using Agoda.EBE.Framework.Data;
using Agoda.EBE.Framework.Objects.Email;
using Agoda.EBE.Framework.Partner.Objects;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.EBE.Agents.Common.Util;
using Agoda.EBE.Agents.Common.Util.Data.Interface;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess.Helpers.Interface;
using Agoda.EBE.Agents.Email.DataAccess.ExternalServicesAccess.Utils;
using Agoda.EBE.Agents.Email.Object.Exceptions;
using Agoda.EBE.Agents.Provisioning.Object;
using Agoda.EBE.Framework.Customer.Interface;
using Agoda.EBE.Framework.EmailContent.Objects;
using Agoda.EBE.Framework.EmailContent.Objects.Interface;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.Services.PIIClient;

namespace Agoda.EBE.Agents.Email.DataAccess
{
    public class DatabaseAccess : IDatabaseAccess
    {
        private IEmailConfiguration _configuration;
        private readonly IMessaging _messaging;
        private readonly ICustomerHelper _customerHelper;
        private readonly IContactsApiHelper _contactsApiHelper;
        private readonly IPIIService _piiService;
        private readonly IDatabaseProvider _databaseProvider;
        private readonly IWhiteLabelHelperService _wlHelperService;

        public DatabaseAccess(IMessaging messaging, 
            IEmailConfiguration emailConfiguration,
            ICustomerHelper customerHelper, IContactsApiHelper contactsApiHelper,
            IPIIService piiService, IDatabaseProvider databaseProvider, IWhiteLabelHelperService wlHelperService)
        {
            _configuration = emailConfiguration;
            _messaging = messaging;
            _customerHelper = customerHelper;
            _contactsApiHelper = contactsApiHelper;
            _piiService = piiService;
            _databaseProvider = databaseProvider;
            _wlHelperService = wlHelperService;
        }
        
        bool isToDmcOrHotel(ConstantEnum.MailTemplateTo templateTo)
        {
            return templateTo == ConstantEnum.MailTemplateTo.DMC ||
                   templateTo == ConstantEnum.MailTemplateTo.Hotel;
        }

        public IBookingContactFromContactsApi GetBookingContact(int bookingId, int whitelabelId,
            int? mailTemplateId = null)
        {
            // this case is used for customer + hotel name
            var parameterInfos = new List<ParameterInfo> { new ParameterInfo("@booking_id", bookingId) };
            return GetBookingContact(bookingId, whitelabelId, parameterInfos, mailTemplateId);
        }
        
        public IBookingContactFromContactsApi GetBookingContact(int bookingId, int whitelabelId, int? dmcType, int contactMethodId = 1,
            ConstantEnum.MailTemplateTo mailTemplateTo = ConstantEnum.MailTemplateTo.Customer, int? mailTemplateId = null)
        {

            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@booking_id", bookingId, SqlDbType.Int)
            };
            
            if (isToDmcOrHotel(mailTemplateTo))
            {
                parameterInfos.Add(new ParameterInfo(dmcType == 2 
                    ? "@hotel_contact_method_id" 
                    : "@dmc_contact_method_id", 
                    contactMethodId, SqlDbType.Int));
            }

            return GetBookingContact(bookingId, whitelabelId, parameterInfos, mailTemplateId, mailTemplateTo);
        }

        private IBookingContactFromContactsApi GetBookingContact(
            int bookingId,
            int whitelabelId,
            List<ParameterInfo> parameterInfos,
            int? mailTemplateId = null,
            ConstantEnum.MailTemplateTo mailTemplateTo = ConstantEnum.MailTemplateTo.Customer)
        {
            var getBookingContactStat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            getBookingContactStat.BeginTrack();
            var contactsApiMigrationTagBuilder = new TagBuilder().AddBookingTag(bookingId);
            contactsApiMigrationTagBuilder.Add(new StringTag("MailTemplateId", mailTemplateId.ToString()));

            try
            {
                if (_configuration.IsContactsApiEnabled)
                {
                    var newBookingContactFromContactsApi = GetBookingContactFromContactsApi(bookingId, whitelabelId,
                        parameterInfos, mailTemplateId, mailTemplateTo);
                    if (newBookingContactFromContactsApi != null) return newBookingContactFromContactsApi; 
                }

                _messaging.ExceptionMessage.Send("[GetBookingContact] fall back to gp_ebe_contacts_get_v8 on templateId: " + mailTemplateId + " bookingId: " + bookingId, null, LogLevel.WARN, contactsApiMigrationTagBuilder.Build());
                var ds = _databaseProvider.ExecuteDataSet("dbo.gp_ebe_contacts_get_v8", parameterInfos);

                if (!ds.HasRowInTable(0)) return null;

                var row = ds.Tables[0].Rows[0];
                var memberId = int.Parse(row["member_id"].ToString() ?? string.Empty);
                
                var paxContact = GetCustomerContact(bookingId, whitelabelId, memberId, parameterInfos);
                // Fetch PII Data from PII/Enigma service instead
                var bookingDetail = _piiService.GetBookingDetail(
                    bookingId: bookingId,
                    memberId: null,
                    whiteLabelId: whitelabelId
                );
                var mainGuest = bookingDetail.Guests?.ToList().Find(guest => guest.GuestNo == 1);
                var hotelEmail = "";
                var bookingContact = new BookingContactFromContactsApi(
                    row,
                    paxContact,
                    hotelEmail,
                    mainGuest?.FirstName ?? string.Empty,
                    mainGuest?.LastName ?? string.Empty
                );
                getBookingContactStat.EndTrack(Enum.Measurement.GetBookingContact);
                return bookingContact;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingContact fail", ex, parameterInfos);
                getBookingContactStat.EndTrack(Enum.Measurement.GetBookingContact, false);
                throw;
            }
        }

        // will replace GetBookingContact when int exp
        private IBookingContactFromContactsApi GetBookingContactFromContactsApi(int bookingId, int whitelabelId, List<ParameterInfo> parameterInfos,
            int? mailTemplateId = null, ConstantEnum.MailTemplateTo mailTemplateTo = ConstantEnum.MailTemplateTo.Customer)
        {

            var templateIsRequireToSupport = isToDmcOrHotel(mailTemplateTo);
            var templateIsSupported = ContactsApiUseCase.IsSupportedMailTemplateId(mailTemplateId);

            var contactsApiMigrationTagBuilder = new TagBuilder().AddBookingTag(bookingId);
            contactsApiMigrationTagBuilder.Add(new StringTag("MailTemplateId", mailTemplateId.ToString()));
            _messaging.ExceptionMessage.Send("[GetBookingContact] moved to gp_ebe_contacts_get_v8 on templateId: " + mailTemplateId + " bookingId: " + bookingId, null, LogLevel.WARN, contactsApiMigrationTagBuilder.Build());
            
            // case 1 hotel/dmc email is not needed -> gp_ebe_contacts_get_v8
            // case 2 getting for dmc/hotel and contactsAPI support and succeed -> gp_ebe_contacts_get_v8 + continue
            // case 3 getting for dmc/hotel and contactsAPI fail -> gp_ebe_contacts_get_v8 + throw error and retry in queue
            // case 4 getting for dmc/hotel and contactsAPI not supported mailTemplateId -> fall back to gp_ebe_contacts_get_v4, very unlikely case.
            try
            {
                var hotelEmail = "";
                if (templateIsRequireToSupport)
                {
                    if (templateIsSupported)
                    {
                        // Fetch Hotel Email from Contacts API service instead
                        hotelEmail = _contactsApiHelper.GetBookingContact((int)mailTemplateId!, bookingId)
                            .GetToEmailAddress(bookingId);
                        _messaging.ExceptionMessage.Send(
                            "[GetBookingContact] moved to gp_ebe_contacts_get_v8 successfully on templateId: " +
                            mailTemplateId + " bookingId: " + bookingId, null, LogLevel.WARN,
                            contactsApiMigrationTagBuilder.Build());
                    }
                    else
                    {
                        _messaging.ExceptionMessage.Send(
                            "[GetBookingContact] moved to gp_ebe_contacts_get_v8 but not supported on templateId: " +
                            mailTemplateId + " bookingId: " + bookingId, null, LogLevel.WARN,
                            contactsApiMigrationTagBuilder.Build());
                        return null;
                    }
                }

                var ds = _databaseProvider.ExecuteDataSet("dbo.gp_ebe_contacts_get_v8", parameterInfos);
                if (!ds.HasRowInTable(0)) return null;

                var row = ds.Tables[0].Rows[0];
                var memberId = int.Parse(row["member_id"].ToString() ?? string.Empty);
                var paxContact = GetCustomerContact(bookingId, whitelabelId, memberId, parameterInfos);
                var bookingDetail = _piiService.GetBookingDetail(
                    bookingId: bookingId,
                    memberId: null,
                    whiteLabelId: whitelabelId
                );
                var mainGuest = bookingDetail.Guests?.ToList().Find(guest => guest.GuestNo == 1);
                return new BookingContactFromContactsApi(
                    row,
                    paxContact,
                    hotelEmail,
                    mainGuest?.FirstName ?? string.Empty,
                    mainGuest?.LastName ?? string.Empty
                );
            }
            catch (Exception e)
            {
                _messaging.ExceptionMessage.Send("[GetBookingContact] moved to gp_ebe_contacts_get_v8 got error on templateId: " + mailTemplateId + " bookingId: " + bookingId + " error: " + e.Message, e, LogLevel.WARN, contactsApiMigrationTagBuilder.Build());
                throw;
            }
        }

        private Contact GetCustomerContact(int bookingId, int whitelabelId, int memberId,
            List<ParameterInfo> parameterInfos)
        {
            var getCustContactStat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            var contact = new Contact();

            try
            {
                getCustContactStat.BeginTrack();
                
                if (memberId < 0) throw new DeletedMemberException(memberId, bookingId);

                var response = _customerHelper.GetContactInfoWithBookingOverride(bookingId, memberId, whitelabelId);
                if (response != null)
                {
                    contact.Phone = response.Phone ?? string.Empty;
                    contact.Email = response.Email;
                    contact.Fax = string.Empty; // We never have a case where we need to send fax to customer so just default it to empty.
                    contact.LoginTypes = response.LoginTypes; // If it is null, we should get null reference exceptions here
                }
                else
                {
                    throw new InvalidOperationException(
                        $"{nameof(_customerHelper.GetContactInfoWithBookingOverride)} returns null");
                }

                getCustContactStat.EndTrack(Enum.Measurement.GetBookingCustomerContact);
                return contact;
            }
            catch (NullReferenceException ex)
            {
                var invalidLoginTypeException = new InvalidLoginTypesException(ex, memberId, bookingId);
                _messaging.LogExceptionWithParameterInfo(invalidLoginTypeException.Message, invalidLoginTypeException, parameterInfos, LogLevel.WARN);
                throw invalidLoginTypeException;
            }
            catch (Exception ex)
            {
                getCustContactStat.EndTrack(Enum.Measurement.GetBookingCustomerContact, false);
                _messaging.ExceptionMessage.Send($"Couldn't fetch customer contact info with override local table for memberID: {memberId}, bookingId: {bookingId}", ex, LogLevel.FATAL,
                    new TagBuilder().AddBookingTag(bookingId).Build());
                throw;
            }
        }

        public List<int> GetBookingListInItinerary(int itineraryId)
        {
            var parameterInfos = new List<ParameterInfo> {new ParameterInfo("@itinerary_id", itineraryId)};
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString,
                    "dbo.ebe_get_bookingid_by_itineraryid_v1", parameterInfos.ToArray());

                if (!ds.HasRowInTable(0))
                {
                    return new List<int>();
                }

                return ds.MapTableRow(0,
                    row => row["booking_id"] == DBNull.Value ? 0 : int.Parse(row["booking_id"].ToString()));
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingListInItinerary fail", ex, parameterInfos);
                throw;
            }
        }

        private List<PartnerInfo> GetPartnerInfoList()
        {
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString,
                    "dbo.ebe_partner_configuration_select_v2", Array.Empty<ParameterInfo>());

                if (ds.HasRowInTable(0))
                {
                    return ds.Tables[0].Rows.Cast<DataRow>().Select(row => new PartnerInfo(row)).ToList();
                }

                _messaging.ExceptionMessage.Send(
                    "GetPartnerInfoList: \"ebe_partner_configuration_select_v2\" return no row", null,
                    LogLevel.WARN);
                return new List<PartnerInfo>();
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingListInItinerary fail", ex, Array.Empty<ParameterInfo>());
                throw;
            }
        }
        
        public bool IsNeedSendCustomerEmail(int affiliateSiteId)
        {
            //Todo: cache later
            var partnerInfoList = GetPartnerInfoList();
            return !partnerInfoList.Any(x => x.SiteIdList.Contains(affiliateSiteId)) 
                   || partnerInfoList.First(x => x.SiteIdList.Contains(affiliateSiteId)).IsSendCustomerEmail;
        }

        public bool UpdateItinerary(int itineraryId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@itinerary_id", itineraryId),
                new ParameterInfo("@user_id", _configuration.UserGuid)
            };

            try
            {
                // ReSharper disable once StringLiteralTypo
                var obj = DbUtils.ExecuteScalar(_configuration.ApplicationEbeConnectionString, "dbo.ebe_itinerary_fraudverify_status_update_v2", parameterInfos);
                return (int) obj > 0;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("UpdateItinerary fail", ex, parameterInfos);
                throw;
            }
        }

        public List<IEmailAlertConfiguration> GetEmailAlertConfigurationList()
        {
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString, "dbo.ebe_email_alert_configuration_select_v2", new ParameterInfo[0].ToList());
                return !ds.HasRowInTable(0)
                    ? new List<IEmailAlertConfiguration>()
                    : ds.MapTableRow<IEmailAlertConfiguration>(0, row => new EmailAlertConfiguration(row));
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetEmailAlertConfigurationList data", ex);
                throw;
            }
        }

        public bool InsertBookingEventData(int bookingId, int eventId, string eventData, DateTime eventDate)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@booking_id", bookingId),
                new ParameterInfo("@event_id", eventId),
                new ParameterInfo("@event_data", eventData),
                new ParameterInfo("@event_date", eventDate),
                new ParameterInfo("@user_id", _configuration.UserGuid)
            };
            try
            {
                var obj = DbUtils.ExecuteScalar(_configuration.ApplicationEbeConnectionString, "ebe_booking_event_details_insert_v3", parameterInfos);
                return (int) obj > 0;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("InsertBookingEventData fail", ex, parameterInfos);
                throw;
            }
        }

        public List<IBookingEventDetailItem> GetBookingEventDetailList(int bookingId, int eventId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@booking_id", bookingId),
                new ParameterInfo("@event_id", eventId)
            };
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString, "dbo.ebe_booking_event_details_select_by_booking_id_event_id_v2",
                    parameterInfos);
                return !ds.HasRowInTable(0)
                    ? new List<IBookingEventDetailItem>()
                    : ds.MapTableRow<IBookingEventDetailItem>(0, row => new BookingEventDetailItem(row));
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingEventDetailList fail", ex, parameterInfos);
                throw;
            }
        }
        
        private DataSet GetBookingInfoDs(int bookingId)
        {
            var parameterInfos = new List<ParameterInfo> {new ParameterInfo("@booking_id", bookingId)};
            return DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString,
                "dbo.ebe_booking_select_by_booking_id_v18", parameterInfos, 1);
        }

        public virtual bool IsBookingExist(int bookingId)
        {
            var isBookingExistStat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            isBookingExistStat.BeginTrack();
            try
            {
                var ds = GetBookingInfoDs(bookingId);
                isBookingExistStat.EndTrack(Enum.Measurement.GetIsBookingExist);
                return ds.HasRowInTable(0);
            }
            catch (Exception ex)
            {
                _messaging.ExceptionMessage.Send($"Get IsBookingExist fail {ex.Message}", ex, LogLevel.FATAL,
                    new TagBuilder().AddBookingTag(bookingId).Build());
                isBookingExistStat.EndTrack(Enum.Measurement.GetIsBookingExist, false);
                throw;
            }
        }
        
        public virtual IBookingItem GetBookingInfo(int bookingId)
        {
            var getBookingInfoStat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            getBookingInfoStat.BeginTrack();
            try
            {
                var ds = GetBookingInfoDs(bookingId);
                if (!ds.HasRowInTable(0))
                {
                    throw new Exception($"GetBookingInfo bookingId: {bookingId} has no row");
                }
                getBookingInfoStat.EndTrack(Enum.Measurement.GetBookingInfo);
                return new BookingItem(ds.Tables[0].Rows[0]);
            }
            catch (Exception ex)
            {
                _messaging.ExceptionMessage.Send($"GetBookingInfoList fail {ex.Message}", ex, LogLevel.FATAL);
                getBookingInfoStat.EndTrack(Enum.Measurement.GetBookingInfo, false);
                throw;
            }
        }
        
        public virtual BookingResell GetBookingResell(Provisioning.Enum.EBEBookingResellType resellTypeId, int bookingId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@resell_type_id", resellTypeId),
                new ParameterInfo("@booking_id", bookingId),
            };
            try
            {
                var ds = _databaseProvider.ExecuteDataSet("dbo.email_agent_get_ebe_booking_resell_v2", parameterInfos);
                if (!ds.HasRowInTable(0)) return null;
                var records = ds.Tables[0].Rows;
                if (records.Count <= 0) return null;
                var row = records[0];
                return new BookingResell
                {
                    BookingId = row.ParseDataRow<int>("booking_id"),
                    ResellBookingId = row.ParseDataRow<int?>("resell_booking_id"),
                    ResellStatus = (Provisioning.Enum.EBEBookingResellStatus) Convert.ToInt32(row["resell_status_id"]),
                };
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingResell fail", ex, parameterInfos);
                throw;
            }
        }
        public IEmailDetailInfo GetEmailDetailInfo(IBookingItem bookingInfo)
        {
            var getEmailDetailInfoStat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();

            try
            {
                getEmailDetailInfoStat.BeginTrack();

                var emailDetailInfo = new EmailDetailInfo(bookingInfo);
                var emailTemplate = GetEmailTemplate(emailDetailInfo.WorkflowParameter.TemplateId);

                emailDetailInfo.MailTemplateTo = emailTemplate.To;
                emailDetailInfo.ContactMethodId = GetContactMethodId(emailTemplate.To, emailDetailInfo.DmcType, bookingInfo.DmcId);
                emailDetailInfo.CusCoTemplateId = emailTemplate.CusCoTemplateId;
                emailDetailInfo.WhitelabelId =
                    _wlHelperService.ResolveWhiteLabelId(bookingInfo.Id, bookingInfo.WhitelabelId);
                
                getEmailDetailInfoStat.EndTrack(Enum.Measurement.GetEmailDetailInfoStat);

                return emailDetailInfo;
            }
            catch (Exception ex)
            {
                _messaging.ExceptionMessage.Send($"DataAccess.GetEmailDetailInfo fail. BookingID: {bookingInfo.Id}", ex,
                        LogLevel.FATAL);
                getEmailDetailInfoStat.EndTrack(Enum.Measurement.GetEmailDetailInfoStat, false);
                throw;
            }
        }

        public int GetContactMethodId(ConstantEnum.MailTemplateTo mailTemplateTo, int? dmcType, int dmcId)
        {
            switch(mailTemplateTo)
            {
                case ConstantEnum.MailTemplateTo.DMC:
                case ConstantEnum.MailTemplateTo.Hotel:
                    return GetContactMethodIdForDmcHotel(dmcId, dmcType);
                case ConstantEnum.MailTemplateTo.Customer:
                    return _configuration.CustomerContactMethodId;
                default:
                    return 0;
            }
        }

        private int GetContactMethodIdForDmcHotel(int dmcId, int? dmcType)
        {
            if (dmcId == (int) Provisioning.Enum.Dmc.Offline)
            {
                return _configuration.OfflineDeliveryContactMethodId;
            }
            return dmcType == 2
                ? _configuration.HotelContactMethodId
                : _configuration.DmcContactMethodId;
        }

        public List<IBookingMeasurement> GetBookingMeasurementByBookingId(int bookingId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@booking_id", bookingId),
                new ParameterInfo("@workflow_state_id", _configuration.MeasurementWorkflowStateIds),
                new ParameterInfo("@email_workflow_state_id",
                    _configuration.SentEmailWorkflowStateIds)
            };
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString, "dbo.ebe_booking_measurement_v1", parameterInfos);
                return !ds.HasRowInTable(0)
                    ? new List<IBookingMeasurement>()
                    : ds.MapTableRow<IBookingMeasurement>(0, row => new BookingMeasurement(row));
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingMeasurementByBookingId fail", ex, parameterInfos);
                throw;
            }
        }

        public int GetBookingTrackerHistoryId(int bookingId)
        {
            var parameterInfos = new List<ParameterInfo> {new ParameterInfo("@booking_id", bookingId)};
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString, "dbo.ebe_party_tracker_select_by_booking_id_v1", parameterInfos);
                return ds.HasRowInTable(0) ? int.Parse(ds.Tables[0].Rows[0]["history_id"].ToString()) : 0;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingTrackerHistoryId fail", ex, parameterInfos);
                throw;
            }
        }

        public int GetBookingPaymentCategory(int bookingId)
        {
            var parameterInfos = new List<ParameterInfo> {new ParameterInfo("@booking_id", bookingId)};
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString, "dbo.ebe_booking_payment_category_select_v1", parameterInfos);
                return ds.HasRowInTable(0) ? int.Parse(ds.Tables[0].Rows[0]["payment_category"].ToString()) : 0;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingPaymentCategory fail", ex, parameterInfos);
                throw;
            }
        }

        public string GetBookingGreetingMessage(int bookingId)
        {
            var parameterInfos = new List<ParameterInfo> {new ParameterInfo("@booking_id", bookingId)};

            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString, "dbo.ebe_booking_hotel_select_greeting_message_by_booking_id_v1",
                    parameterInfos);
                if (ds.HasRowInTable(0))
                {
                    if (! Convert.IsDBNull(ds.Tables[0].Rows[0]["greeting_message"]))
                    {
                        return Compression.DecompressToString((byte[]) ds.Tables[0].Rows[0]["greeting_message"]);
                    }
                    _messaging.LogExceptionWithParameterInfo($"greeting_message is null BookingId:{bookingId}", null, parameterInfos, LogLevel.WARN);
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingGreetingMessage fail", ex, parameterInfos, LogLevel.WARN);
                throw;
            }
        }

        public IPropertyMessageConfiguration GetPropertyMessageConfiguration(int hotelId)
        {
            var parameterInfos = new List<ParameterInfo> {new ParameterInfo("@property_id", hotelId)};

            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString, "dbo.property_message_api_configuration_select_v1", parameterInfos);
                return ds.HasRowInTable(0)
                    ? new PropertyMessageConfiguration(hotelId, ds.Tables[0].Rows[0])
                    : new PropertyMessageConfiguration();
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetPropertyMessageConfiguration fail", ex, parameterInfos);
                throw;
            }
        }

        public List<IBookingEventDetailItem> GetBookingEventDetailListByEventId(int eventId)
        {
            var parameterInfos = new List<ParameterInfo> {new ParameterInfo("@event_id", eventId)};
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString, "dbo.ebe_booking_event_details_select_by_event_id_event_date_v1",
                    parameterInfos);
                return !ds.HasRowInTable(0)
                    ? new List<IBookingEventDetailItem>()
                    : ds.MapTableRow<IBookingEventDetailItem>(0, row => new BookingEventDetailItem(row));
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingEventDetailListByEventId fail", ex, parameterInfos);
                throw;
            }
        }

        public ITrackerSettingItem GetBookingVocEmailInfo(int bookingId, int historyId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@booking_id", bookingId),
                new ParameterInfo("@history_id", historyId)
            };

            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString, "dbo.ebe_party_tracker_select_by_booking_id_tracker_history_id_v1",
                    parameterInfos);
                return ds.HasRowInTable(0) ? new TrackerSettingItem(ds.Tables[0].Rows[0]) : null;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingVocEmailInfo fail", ex, parameterInfos);
                throw;
            }
        }

        public virtual EmailTemplate GetEmailTemplate(int templateId, int languageId = 1)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@get_language", languageId, SqlDbType.Int),
                new ParameterInfo("@mail_template_id", templateId, SqlDbType.Int)
            };

            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString, "dbo.email_agent_select_email_template_info_by_template_id_v1", parameterInfos);
                return ds.HasRowInTable(0) ? new EmailTemplate(ds.Tables[0].Rows[0]) : null; 
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetEmailTemplate fail", ex, parameterInfos);
                throw;
            }
        }
        
        public IBookingEventDetailItem GetEventDetailByBookingId(int bookingId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@booking_id", bookingId, SqlDbType.Int),
            };

            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString, "dbo.ebe_email_agent_get_booking_email_alert_detail_v2", parameterInfos);
                return ds.HasRowInTable(0) ? new BookingEventDetailItem(ds.Tables[0].Rows[0]) : null; 
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetEventDetailByBookingId fail", ex, parameterInfos);
                throw;
            }
        }

        public List<IBookingEmailAlertConfig> GetEmailAlertConfig(int workFlowStateId, int eventId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@workflow_state_id", workFlowStateId, SqlDbType.Int),
                new ParameterInfo("@event_id", eventId, SqlDbType.Int)
            };

            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString, "dbo.ebe_email_agent_get_alert_event_config_v2", parameterInfos);
                List<IBookingEmailAlertConfig> results = Enumerable.Empty<IBookingEmailAlertConfig>().ToList();
                if (ds.HasRowInTable(0))
                {
                    var records = ds.Tables[0].Rows;
                    //https://stackoverflow.com/questions/46675355/why-cannot-use-linq-on-datarowcollection-if-its-base-class-derives-from-ienumera
                    var rows = records.Cast<DataRow>();
                    results = rows.Select(row => new BookingEmailAlertConfig(row)).ToList<IBookingEmailAlertConfig>();
                }
                return results;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingEmailAlertConfig fail", ex, parameterInfos);
                throw;
            }
        }
        public int GetWorkflowActionResultFromBookingHistory(int bookingId, int workflowStateId)
        {
            var parameterInfos = new List<ParameterInfo>
            {
                new ParameterInfo("@booking_id", bookingId, SqlDbType.Int),
                new ParameterInfo("@workflow_state_id", workflowStateId, SqlDbType.Int)
            };

            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString, "dbo.ebe_email_agent_get_workflow_action_results_v2", parameterInfos);
                return ds.HasRowInTable(0) ? int.Parse(ds.Tables[0].Rows[0]["workflow_action_results_id"].ToString()) : 0;
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetWorkflowActionResultId fail", ex, parameterInfos);
                throw;
            }
        }

        public BookingResell GetBookingResellByBookingId(int bookingId)
        {
            var parameterInfos = new List<ParameterInfo> {new ParameterInfo("@booking_id", bookingId)};
            try
            {
                var ds = DbUtils.ExecuteDataSet(_configuration.ApplicationEbeConnectionString,
                    "dbo.email_agent_ebe_booking_resell_select_by_booking_id_v1", parameterInfos);
                if (!ds.HasRowInTable(0)) return null;
                var records = ds.Tables[0].Rows;
                if (records.Count <= 0) return null;
                var row = records[0];
                return new BookingResell
                {
                    BookingId = row.ParseDataRow<int>("booking_id"),
                    ResellBookingId = row.ParseDataRow<int?>("resell_booking_id"),
                    ResellStatus = (Provisioning.Enum.EBEBookingResellStatus) Convert.ToInt32(row["resell_status_id"]),
                };
            }
            catch (Exception ex)
            {
                _messaging.LogExceptionWithParameterInfo("GetBookingResellByBookingId fail", ex, parameterInfos);
                throw;
            }
        }
    }
}
