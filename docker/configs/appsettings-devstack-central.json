{
    "Payment": {
        "ConnectionStrings": [
            {
                "Name": "application_ebe",
                "Value": "Server=mdb,1433;uid=sa;pwd=*********;database=Application_EBE;Max Pool Size=50;APP=EBE_PAYMENTAGENTV2"
            },
            {
                "Name": "RabbitMQ",
                "Value": "host=rabbitmq:5672;username=devrabbit;password=******;requestedHeartbeat=10;prefetchcount=1;publisherConfirms=true;timeout=180;persistentMessages=true"
            }
        ],
        "ConfigurationGroupID": "47",
        "IsActive": true,
        "StoreFrontID": 37,
        "MetricName": "Agoda.Agents.Payment",
        "UserGuid": "f524e4d0-7048-4a5c-bf82-e4590d3709c0", // pragma: allowlist secret
        "ComponentName": "PaymentAgent",
        "QueueName": "EBE.Payment",
        "QueueMaxSubscriber": 10,
        "WorkflowRetryInterval": 5,
        "WorkflowRetryCount": 5,
        "CertificatePublicKeyPath": "/opt/Agoda/Backend/Keys/public/17",
        "SecureKeyId": 17,
        "SubSystemId": 1,
        "SettlementAmountRank": 201,
        "WaitForSettlementId": 308,
        "WorkflowWebserviceUrls": [
            "http://ebe-workflow-webapi:80/api/workflow"
        ],
        "PaymentServiceUrl": "http://ebe-payment-api-netcore:5000/paymentapi/paymentservice.svc",
        "PaymentServiceUrlV2": "http://ebe-payment-api-netcore:5000/paymentapi/v2/payment",
        "PaymentServiceV2ConsulProxyUrl": "https://local.agoda.local:11000/paymentapi/v2/payment",
        "PaymentServiceTimeout": 100,
        "PaymentServiceBindingProtocol": "http",
        "CreditCardServiceApiKey": "P4bC6uYJ8HOVRVECUFQx6jcK32Ry4o7LIYRdJfvqBB2nHK0aXi", // pragma: allowlist secret
        "CreditCardServiceClientId": 15,
        "CreditCardServiceUrl": "http://ebe-creditcard-service-netcore:80/creditcard/v3",
        "CreditCardServiceConsulProxyUrl": "https://local.agoda.local:11000/creditcardapi/v3",
        "UseConsulProxy": false,
        "CustomerApiServiceUrl": [
            "http://customerapi:80"
        ],
        "CustomerApiServiceUrlUat": [
            "http://customerapi:80"
        ],
        "GiftCardApiConfiguration": {
            "Urls": [
                "http://giftcard-api:8080/GiftCardService/API/giftcard"
            ],
            "ApiKey": "lusBYtdAQzaDXnZsgKTTnNIRxknbzGIthpJ616YP6wSVOqlPDI", // pragma: allowlist secret
            "IsConnectViaConsul": false
        },
        "ExternalLoyaltyApiConfiguration": {
            "Urls": [
                "http://whitelabel-loyalty-api:80"
            ],
            "IsConnectViaConsul": false
        },
        "WhiteLabelClientConfiguration": {
            "LocalCacheFilePath": ".",
            "TimeoutMilliseconds": 500,
            "ServerSettings": [
                {
                    "Url": "http://whitelabel-service-api",
                    "MaxServerWeight": 99
                }
            ],
            "InitiateAsyncInterval": 300000
        },
        "IsRedirectPaymentActive": true,
        "RedirectQueueName": "EBE.RedirectPayment",
        "RedirectQueueMaxSubscriber": 20,
        "WaitForPaymentRequestStatus": 14,
        "IsRedirectReadonlyExperiment": false,
        "RewardsApiKey": "7TkB77Y8s6wVRkBd", // pragma: allowlist secret
        "ClientId": "15",
        "BapiEndpoint": "http://booking-api:8080",
        "BapiApiKey": "OGjOiYtdNEof2LgpsHzXRKbtuRoXwauwQb2hwYX5uz6H97neqn", // pragma: allowlist secret
        "BapiWhiteLabel": "F1A5905F-9620-45E5-9D91-D251C07E0B42", // pragma: allowlist secret
        "IsBapiSSLEnabled": false,
        "MaxRefundReprocessingAttempts": 3,
        "AutoChargeRetryQueueName": "EBE.Payment.AutoCharge.Retry",
        "AutoChargeRetryIntervalSeconds": 3600,
        "AutoChargeRetryCount": 3,
        "IsEnabledDelaySettlement": true,
        "IsEnabledVisaUSDAdyen": false,
        "EligibleDelaySettlementState": [
            301,
            303
        ],
        "DelaySettlementIntervalMin": 4320,
        "ConsulSetting": {
            "AgentUrl": "http://localhost:8500",
            "ServerUrl": "http://localhost:8500",
            "DataCenter": "dc1",
            "PollingIntervalInSeconds": 600,
            "LocalFileCachePath": "consulCache.txt",
            "DontUseConsul": true
        },
        "SkipSettlements": [
            {
                "Name": "Umrah",
                "HotelId": 202019617,
                "CreditCardNumber": "****************",
                "Email": "<EMAIL>"
            }
        ],
        "BookingApiConfiguration": {
            "Urls": [
                "http://booking-api:8080"
            ],
            "UatUrls": [
                "http://booking-api:8080"
            ],
            "ClientId": 15,
            "ApiKey": "OGjOiYtdNEof2LgpsHzXRKbtuRoXwauwQb2hwYX5uz6H97neqn", // pragma: allowlist secret
            "IsConnectBapiViaConsul": false
        }
    },
    "Email": {
        "ConnectionStrings": [
            {
                "Name": "application_ebe",
                "Value": "Server=mdb,1433;uid=sa;pwd=*********;database=Application_EBE;Max Pool Size=50;APP=EBE_EMAILAGENT"
            },
            {
                "Name": "RabbitMQ",
                "Value": "host=rabbitmq:5672;username=devrabbit;password=******;requestedHeartbeat=10;prefetchcount=1;publisherConfirms=true;timeout=180;persistentMessages=true"
            }
        ],
        "ConfigurationGroupID": "46",
        "HotelContactMethodId": 102,
        "DmcContactMethodId": 202,
        "CustomerContactMethodId": 1,
        "OfflineDeliveryContactMethodId": 310,
        "WorkflowWebserviceUrls": [
            "http://ebe-workflow-webapi:80/api/workflow"
        ],
        "WorkflowRetryInterval": 5,
        "RabbitMQueueRetrySleep": 5,
        "WorkflowRetryCount": 5,
        "HermesUrl": "http://bpf-wiremock:8080/v2/messages",
        "HermesRetryCount": 2,
        "HermesTimeoutSeconds": 10,
        "PigeonRetryCount": 0,
        "PigeonUrl": "http://bpf-wiremock",
        "PigeonTimeoutSeconds": 5,
        "IsPigeonEnabled": true,
        "ContactsApiUrl": "http://bpf-wiremock",
        "ContactsApiApiKey": "Wg7P7giMqy2q6um4bvOTswZr7yNqLTxm", // pragma: allowlist secret
        "ContactsApiTimeoutSeconds": 10000,
        "IsContactsApiEnabled": false,
        "CustomerApiServiceUrl": [
            "http://customerapi:80"
        ],
        "CustomerApiServiceUrlUat": [
            "http://customerapi:80"
        ],
        "CoBrandingTemplateId": "4,32,66,68,10,15,18,19,29,45,56,70,221,20,38,25,43,78",
        "MeasurementWorkflowStateIds": "114,117,122,211,212,213,270,295,516,604,614,704,900,171,172",
        "EmailServiceUrl": "http://bpf-wiremock/email/emailservice.asmx/Send",
        "EmailVerifyState": [
            102
        ],
        "EmailConfirmationState": [
            250,
            400
        ],
        "EmailHotelVoucherState": [
            201,
            218,
            251,
            291,
            296,
            314,
            401,
            661,
            752,
            753,
            811,
            832,
            834,
            841,
            852,
            861,
            872
        ],
        "CancellationHotelVoucherState": [
            811,
            832,
            834,
            841,
            852,
            861,
            872
        ],
        "EnablePayHotelDMCForSendingHotelVoucher": [
            332,
            29004,
            44444
        ],
        "SentEmailWorkflowStateIds": "250,291,400,401",
        "MeasurementEmailTemplateIds": [
            72,
            78,
            88
        ],
        "IsActive": true,
        "StoreFrontID": 35,
        "MetricName": "Agoda.Agents.Email",
        "UserGuid": "d7708705-22d2-49fc-ac8b-4959f111096a",
        "ComponentName": "EmailAgent",
        "QueueName": "EBE.Email",
        "QueueMaxSubscriber": 10,
        "YcsServiceUrl": "UNUSED",
        "V3ApiServiceUrl": "UNUSED",
        "V3ServiceRetryCount": 3,
        "V3ServiceRetryInterval": 1000,
        "V3ServiceTimeout": 10000,
        "V3ServiceAPIKey": "cqIGVbvEhiPpXauimPFIAjr2jPFhhXDJOjTR8NZjJt0r0CmM78", // pragma: allowlist secret
        "V3ServiceClientId": 37,
        "ResendEmailQueueName": "EBE.Email.Resend",
        "IsResendEmailActive": true,
        "EnigmaServiceUrls": [
            "http://enigma:8080"
        ],
        "EnigmaApiKey": "test", // pragma: allowlist secret
        "ExternalServiceTimeoutSeconds": 5,
        "DeliveryApiServiceUrl": "http://bpf-wiremock:8080/",
        "IsStoreContent": true,
        "DeliveryApiClientId": 5003,
        "IsEnableUseCusCo": true,
        "MeasurementCusCoTemplateIds": [], //leave it blank until we turn on the confirmation email by CusCo, then update it with CusCo Template Id
        "DeliveryApiTimeout": 3000,
        "AllowedSmsTemplateIds": [
            1053,
            1100
        ], //cusco template ids of confirmation and amendment
        "NotAllowedCuscoTemplateIdsForEmailLogin": [
            1053,
            1100
        ], //cusco template ids of confirmation and amendment
        "RewardsApiKey": "7TkB77Y8s6wVRkBd", // pragma: allowlist secret
        "AllowedPushTemplateIds": [
            4347
        ],
        "DeliveryApiRetryCount": 1,
        "DeliveryApiServerMaxWeight": 100,
        "NhaBorSendEmailState": [
            835,
            836,
            319,
            400,
            401,
            833,
            834
        ],
        "HermesDummyEmail": "<EMAIL>",
        "HermesBorDefaultEmail": "<EMAIL>",
        "SendEmailQueueName": "EBE.Email.SendEmail.Federated",
        "IsSendEmailQueueActive": true,
        "emailOverrideAllowedTemplateId": [
            79,
            78,
            88
        ],
        "UseBapiForSecureLinkGeneration": true,
        "ConsulSetting": {
            "AgentUrl": "http://localhost:8500",
            "ServerUrl": "http://localhost:8500",
            "DataCenter": "dc1",
            "PollingIntervalInSeconds": 600,
            "LocalFileCachePath": "consulCache.txt",
            "DontUseConsul": true
        },
        "BookingApiConfiguration": {
            "Urls": [
                "http://booking-api:8080"
            ],
            "UatUrls": [
                "http://booking-api:8080"
            ],
            "ClientId": 37,
            "ApiKey": "nZWPc5LOhagpbItoUwDVXKOpClv4vhWVGQ1xxboV8oXhwyAEoy", // pragma: allowlist secret
            "IsConnectBapiViaConsul": false
        },
        "WhiteLabelClientConfiguration": {
            "LocalCacheFilePath": ".",
            "TimeoutMilliseconds": 500,
            "ServerSettings": [
                {
                    "Url": "http://whitelabel-service-api",
                    "MaxServerWeight": 99
                }
            ],
            "InitiateAsyncInterval": 300000
        },
        "IsCommunicationApiEnabled": true,
        "CommunicationApiServiceUrl": "https://bpf-wiremock",
        "CommunicationApiKey": "7jj4OQE4KFLJh5WqwEjVeWzNWGwg8UIaPpXrclJjGSQ3MsKANoyiK9CteK4Qbq6v" // pragma: allowlist secret
    },
    "Provisioning": {
        "ConnectionStrings": [
            {
                "Name": "Application_EBE",
                "Value": "Server=mdb,1433;uid=sa;pwd=*********;database=Application_EBE;Max Pool Size=50;App=EBE_ProvisioningAgent"
            },
            {
                "Name": "RabbitMQ",
                "Value": "host=rabbitmq:5672;username=devrabbit;password=******;requestedHeartbeat=10;prefetchcount=1;publisherConfirms=true;timeout=180;persistentMessages=true"
            },
            {
                "Name": "Application_BFDB",
                "Value": "Server=bfdb,1433;Failover Partner=bfdb,1433;uid=sa;pwd=*********;database=Application_Booking_API_Local;Max Pool Size=50;App=EBE_ProvisioningAgent"
            },
            {
                "Name": "CDB",
                "Value": "Server=cdb,1433;uid=sa;pwd=*********;database=Application_EBE_RO;Max Pool Size=50"
            }
        ],
        "UpcToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRJZCI6IjUiLCJyb2xlSWQiOjF9.QInM7KVPFIHljAmIyzOLnrVmLl2iWqzc1UjEtX5Pjww", // pragma: allowlist secret
        "ConfigurationGroupID": "48",
        "KeyPath": "/opt/Agoda/Backend/Keys/public/17",
        "SecureId": 17,
        "AbsTimeout": 120000,
        "AbsUrls": [
            "http://abs-phoenix-app:8080"
        ],
        "AbsTatClusterTimeout": 120000,
        "AbsTatClusterUrls": [
            "http://abs-phoenix-app:8080"
        ],
        "WorkflowWebserviceUrls": [
            "http://ebe-workflow-webapi:80/api/workflow"
        ],
        "UpcServiceUrl": "http://bpf-wiremock:8080/upc/upcservice.asmx",
        "UPCWebserviceUrl": "http://upc-api:80/upc/upcwebservice",
        "LocalUPCWebserviceUrl": "http://upc-api:80/upc/upcwebservice",
        "PayoutConfiguration": {
            "UsePayoutStaticUrls": true,
            "PayoutUrls": "http://bpf-wiremock:8080", // pragma: allowlist secret
            "PayoutToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjb21wYW55IjoiQWdvZGEiLCJjbGllbnQiOiJtYnBlLWNsaWVudCIsInJvbGVzIjpbInBjaSJdfQ.Y6j1hXE9CY9hMFJbWN6TlFGvDRWmhGKKhwBxV0-xNtY" // pragma: allowlist secret
        },
        "CreditCardServiceUrl": "http://ebe-creditcard-service-netcore:80/creditcard/v3",
        "CreditCardServiceConsulProxyUrl": "https://local.agoda.local:11000/creditcardapi/v3",
        "UseConsulProxy": false,
        "CreditCardServiceApiKey": "DNwdhZYZXq0xSj4uwzMTg6pcNBBX4aMVHKgT8ba3fCRvWBT8S0", // pragma: allowlist secret
        "GeneralApiServiceUrl": "UNUSED",
        "RewardsRestServiceUrl": [
            "http://giftcard-api:8080/"
        ],
        "RewardsRestServiceUrlUat": [
            "http://giftcard-api:8080/"
        ],
        "AckDmcs": [
            "332",
            "27800",
            "27802",
            "27804",
            "29000",
            "29001",
            "29003",
            "29005"
        ],
        "MetricName": "Agoda.Agents.Provisioning",
        "ComponentName": "ProvisioningAgent",
        "IsActive": true,
        "StoreFrontID": 36,
        "UserGuid": "148ba443-0d30-45bc-b098-bbe3ebd46298", // pragma: allowlist secret
        "QueueName": "EBE.Provisioning",
        "QueueMaxSubscriber": 10,
        "CreateAckRetryTime": 5,
        "CreateAckRetryCount": 3,
        "AbsRetryCount": 3,
        "WorkflowRetryCount": 53,
        "WorkflowRetryInterval": 5,
        "EnigmaServiceUrls": [
            "http://enigma:8080"
        ],
        "EnigmaApiKey": "test", // pragma: allowlist secret
        "EnigmaTimeoutInSeconds": 5,
        "EnigmaRetryCount": 3,
        "BookingYcsProtocol": "https",
        "RewardsApiKey": "7TkB77Y8s6wVRkBd", // pragma: allowlist secret
        "OrchestratorQueue": "multi.orchestration",
        "OrchestratorHistoryQueue": "multi.orchestration.history",
        "Mode": 0,
        "CommanderQueueSize": 1,
        "ConsumerQueueSize": 20,
        "ConsulSetting": {
            "AgentUrl": "http://localhost:8500",
            "ServerUrl": "http://localhost:8500",
            "DataCenter": "dc1",
            "PollingIntervalInSeconds": 600,
            "LocalFileCachePath": "consulCache.txt",
            "DontUseConsul": true
        },
        "CdbServiceTags": [],
        "UseCdbRoundRobin": false,
        "FallbackCdbServer": "cdb,1433",
        "WhiteLabelClientConfiguration": {
            "LocalCacheFilePath": ".",
            "TimeoutMilliseconds": 500,
            "ServerSettings": [
                {
                    "Url": "http://whitelabel-service-api",
                    "MaxServerWeight": 99
                }
            ],
            "InitiateAsyncInterval": 300000
        },
        "DMCConfiguration": [
            {
                "DMCID": 332,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 27800,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 27802,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 27804,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 27912,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 27914,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 27918,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 27934,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 27935,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 27961,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 27990,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 27991,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 27992,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 27994,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 28011,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 28021,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 28032,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 28666,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 29000,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 29001,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 29002,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 29003,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 29004,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 29005,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 29014,
                "IsCreateAcknowledge": true
            },
            {
                "DMCID": 33333,
                "IsCreateAcknowledge": true
            }
        ],
        "UseBapiForCreateAcknowledgement": true,
        "BookingApiConfiguration": {
            "Urls": [
                "http://booking-api:8080"
            ],
            "UatUrls": [
                "http://booking-api:8080"
            ],
            "ClientId": 30,
            "ApiKey": "OGjOiYtdNEof2LgpsHzXRKbtuRoXwauwQb2hwYX5uz6H97neqn", // pragma: allowlist secret
            "IsConnectBapiViaConsul": false
        },
        "InstantBookConfiguration": {
            "YcsMinimumRemainingTimeMs": 1000,
            "NonYcsMinimumRemainingTimeMs": 30000
        },
        "AbsClientConfiguration": {
            "ConsulConfiguration": {
                "ServerUrl": "https://localhost:8501",
                "DataCenter": "qa1",
                "PollingIntervalInSeconds": 5,
                "LocalFileCachePath": "provisioningConsulCache.txt",
                "DontUseConsul": false
            },
            "LocalConfiguration": {
                "Urls": [
                    "http://abs-phoenix-app:8080"
                ],
                "IsConnectAbsViaConsul": false
            },
            "ConsulServiceTags": [
                "abs-phoenix",
                "qa",
                "mock"
            ],
            "UseHttps": false
        }
    },
    "UPC": {
        "ConnectionStrings": [
            {
                "Name": "Application_EBE",
                "Value": "Server=mdb,1433;uid=sa;pwd=*********;database=Agoda_Core;Max Pool Size=50;App=EBE_UPCAGENT"
            },
            {
                "Name": "Agoda_Finance",
                "Value": "Server=mdb,1433;uid=sa;pwd=*********;database=application_finance;Max Pool Size=50;App=EBE_UPCAGENT_FINANCE"
            },
            {
                "Name": "RabbitMQ",
                "Value": "host=rabbitmq:5672;username=devrabbit;password=******;requestedHeartbeat=10;prefetchcount=1;publisherConfirms=true;timeout=180;persistentMessages=true" // pragma: allowlist secret
            },
            {
                "Name": "Application_BFDB",
                "Value": "Server=bfdb,1433;Failover Partner=bfdb,1433;uid=sa;pwd=*********;database=Application_Booking_API_Local;Max Pool Size=50;App=EBE_UPCAGENT"
            },
            {
                "Name": "CDB",
                "Value": "Server=cdb,1433;uid=sa;pwd=*********;database=Application_EBE_RO;Max Pool Size=50"
            },
            {
                "Name": "MDC",
                "Value": "Server=mdc,1433\\MDC;database=Application_Finance;uid=sa;pwd=*********;App=Agoda_Finance;"
            }
        ],
        "MultiProductSetting": {
            "Vehicle": {
                "UpcApiMode": 2
            }
        },
        "ConfigurationGroupID": "59",
        "IsActive": true,
        "UPCAccountID": 0,
        "UserGuid": "a3f89632-9a1c-4553-8f32-e87ff3266904",
        "UPCWebserviceUrl": "http://bpf-mock:8080/upc/upcwebservice",
        "LocalUPCWebserviceUrl": "http://bpf-mock:8080/upc/upcwebservice",
        "WorkflowWebserviceUrls": [
            "http://ebe-workflow-webapi:80/api/workflow"
        ],
        "FinanceSetting": "DmcDll",
        "QueueMaxSubscriber": 20,
        "NonRefundableCXLList": [
            "365D100P_100P"
        ],
        "HotelClassForUPCOnBooking": [
            "A",
            "B",
            "C"
        ],
        "MetricName": "EBE.Agents.UPC",
        "ComponentName": "UpcAgent",
        "StoreFrontID": 59,
        "EnableUpdateFireDrillFlag": "N",
        "LogUpdateFireDrillFlag": "",
        "QueueName": "EBE.UPC",
        "ConsulSetting": {
            "AgentUrl": "http://localhost:8500",
            "ServerUrl": "http://localhost:8500",
            "DataCenter": "dc1",
            "PollingIntervalInSeconds": 600,
            "LocalFileCachePath": "consulCache.txt",
            "DontUseConsul": true
        },
        "OrchestratorQueue": "multi.orchestration",
        "OrchestratorHistoryQueue": "multi.orchestration.history",
        "Mode": 0,
        "CdbServiceTags": [],
        "UseCdbRoundRobin": false,
        "FallbackCdbServer": "cdb,1433",
        "WhiteLabelClientConfiguration": {
            "LocalCacheFilePath": ".",
            "TimeoutMilliseconds": 500,
            "ServerSettings": [
                {
                    "Url": "http://whitelabel-service-api",
                    "MaxServerWeight": 99
                }
            ],
            "InitiateAsyncInterval": 300000
        },
        "UpcToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRJZCI6IjUiLCJyb2xlSWQiOjF9.QInM7KVPFIHljAmIyzOLnrVmLl2iWqzc1UjEtX5Pjww", // pragma: allowlist secret
        "UsePayoutStaticUrls": true,
        "PayoutUrls": "http://bpf-wiremock:8080",
        "PayoutToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjb21wYW55IjoiQWdvZGEiLCJjbGllbnQiOiJtYnBlLWNsaWVudCIsInJvbGVzIjpbInBjaSJdfQ.Y6j1hXE9CY9hMFJbWN6TlFGvDRWmhGKKhwBxV0-xNtY", // pragma: allowlist secret
        "PayoutConfiguration": {
            "UsePayoutStaticUrls": true,
            "PayoutUrls": "http://bpf-wiremock:8080",
            "PayoutToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjb21wYW55IjoiQWdvZGEiLCJjbGllbnQiOiJtYnBlLWNsaWVudCIsInJvbGVzIjpbInBjaSJdfQ.Y6j1hXE9CY9hMFJbWN6TlFGvDRWmhGKKhwBxV0-xNtY" // pragma: allowlist secret
        }
    }
}