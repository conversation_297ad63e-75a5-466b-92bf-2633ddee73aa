{
  "UPC": {
    "ConnectionStrings": [
	  {
		{{with secret "secret/it-dbops-sql/sql/credential/ebe_upc/mdb"}}
	    "Name": "Application_EBE",
	    "Value": "Server=mdb;uid={{.Data.username}};pwd={{.Data.password}};database=Agoda_Core;Max Pool Size=50;App=EBE_UPCAGENT"
		{{end}}
	  },
	  {
		{{with secret "secret/it-dbops-sql/sql/credential/finance_api_user/mdb"}}
	    "Name": "Agoda_Finance",
	    "Value": "Server=mdb;uid={{.Data.username}};pwd={{.Data.password}};database=application_finance;Max Pool Size=50;App=EBE_UPCAGENT_FINANCE"
		{{end}}
	  },
	  {
	    {{with secret "secret/ebe_netcore_agents/central/UPC"}}
	    "Name": "RabbitMQ",
	    "Value": "host={{ env "RABBITMQ_HOST" }};username={{ env "RABBITMQ_USERNAME" }};password={{ env "RABBITMQ_PASSWORD" }};requestedHeartbeat=2;prefetchcount=1;publisherConfirms=true;timeout=20;persistentMessages=true"
		{{end}}
	  },
	  {
	    {{with secret "secret/it-dbops-sql/sql/credential/ebe_netcore_agents_rw_user/bfdb"}}
	    "Name": "Application_BFDB",
	    "Value": "Server=bfdb;Failover Partner=bfdb;uid={{.Data.username}};pwd={{.Data.password}};database=Application_Booking_API_Local;Max Pool Size=50;App=EBE_UPCAGENT"
		{{end}}
	  },
	  {
	    {{with secret "secret/it-dbops-sql/sql/credential/ebe_netcore_agents_ro_user/cdb"}}
	    "Name": "CDB",
	    "Value": "Server={server_to_be_replaced_by_consul};uid={{.Data.username}};pwd={{.Data.password}};database=Application_EBE_RO;Max Pool Size=50"
		{{end}}
	  },
	  {
	    {{with secret "secret/it-dbops-sql/sql/credential/ebe_netcore_agents_rw_user/mdc"}}
	    "Name": "MDC",
	    "Value": "Server=mdc;database=Application_Finance;uid={{.Data.username}};pwd={{.Data.password}};App=EBE_UPCAGENT;"
		{{end}}
	  }
	],
         "PayoutConfiguration": {
          	 {{with secret "secret/ebe_netcore_agents/central/UPC"}}
                 "PayoutToken": "{{.Data.PayoutToken}}"
          	 {{end}}
         },
         {{with secret "secret/it-payout/payout-booking-client/ebe-netcore-agents"}}
         "PayoutBookingToken": "{{.Data.token}}",
         {{end}}
	{{with secret "secret/ebe_netcore_agents/central/common"}}
    "UpcToken": "{{.Data.UpcToken}}"
	{{end}}
  },
  "Provisioning": {
	"ConnectionStrings": [
	  {
	    {{with secret "secret/it-dbops-sql/sql/credential/ebe_provision/mdb"}}
		"Name": "Application_EBE",
		"Value": "Server=mdb;uid={{.Data.username}};pwd={{.Data.password}};database=Application_EBE;App=EBE_ProvisioningAgent;Max Pool Size=50"
		{{end}}
	  },
	  {
	    {{with secret "secret/ebe_netcore_agents/central/Provisioning"}}
		"Name": "RabbitMQ",
		"Value": "host={{ env "RABBITMQ_HOST" }};username={{ env "RABBITMQ_USERNAME" }};password={{ env "RABBITMQ_PASSWORD" }};requestedHeartbeat=2;prefetchcount=1;publisherConfirms=true;timeout=20;persistentMessages=true"
		{{end}}
	  },
	  {
	    {{with secret "secret/it-dbops-sql/sql/credential/ebe_netcore_agents_rw_user/bfdb"}}
	    "Name": "Application_BFDB",
	    "Value": "Server=bfdb;Failover Partner=bfdb;uid={{.Data.username}};pwd={{.Data.password}};database=Application_Booking_API_Local;Max Pool Size=50;App=EBE_ProvisioningAgent"
		{{end}}
	  },
	  {
	    {{with secret "secret/it-dbops-sql/sql/credential/ebe_netcore_agents_ro_user/cdb"}}
	    "Name": "CDB",
	    "Value": "Server={server_to_be_replaced_by_consul};uid={{.Data.username}};pwd={{.Data.password}};database=Application_EBE_RO;Max Pool Size=50"
		{{end}}
	  }
	],
         "PayoutConfiguration": {
               {{with secret "secret/ebe_netcore_agents/central/UPC"}}
                    "PayoutToken": "{{.Data.PayoutToken}}"
               {{end}}
         },
	{{with secret "secret/ebe_netcore_agents/central/Provisioning"}}
	"CreditCardServiceApiKey": "{{.Data.CreditCardServiceApiKey}}",
    "BookingApiConfiguration": {
	  "ClientId": {{.Data.BookingApiConfiguration__ClientId}},
	  "ApiKey": "{{.Data.BookingApiConfiguration__ApiKey}}"
    },
	{{end}}
	{{with secret "secret/ebe_netcore_agents/central/common"}}
    "EnigmaApiKey": "{{.Data.EnigmaApiKey}}",
    "RewardsApiKey": "{{.Data.RewardsApiKey}}",
	"UpcToken": "{{.Data.UpcToken}}"
	{{end}}
  },
  "Email": {
	"ConnectionStrings": [
	  {
	    {{with secret "secret/it-dbops-sql/sql/credential/ebe_email/mdb"}}
        "Name": "application_ebe",
        "Value": "Server=mdb;uid={{.Data.username}};pwd={{.Data.password}};database=Application_EBE;Max Pool Size=50;APP=EBE_EMAILAGENT"
		{{end}}
	  },
	  {
	    {{with secret "secret/ebe_netcore_agents/central/Email"}}
	    "Name": "RabbitMQ",
	    "Value": "host={{ env "RABBITMQ_HOST" }};username={{ env "RABBITMQ_USERNAME" }};password={{ env "RABBITMQ_PASSWORD" }};requestedHeartbeat=2;prefetchcount=1;publisherConfirms=true;timeout=20;persistentMessages=true"
		{{end}}
	  }
	],
	{{with secret "secret/ebe_netcore_agents/central/Email"}}
	"DeliveryApiClientId": {{.Data.DeliveryApiClientId}},
	"BookingApiConfiguration": {
	  "ClientId": {{.Data.BookingApiConfiguration__ClientId}},
	  "ApiKey": "{{.Data.BookingApiConfiguration__ApiKey}}"
	},
	"HermesClientId": {{.Data.HermesClientId}},
	"HermesApiKey": "{{.Data.HermesApiKey}}",
	"PigeonClientId": {{.Data.PigeonClientId}},
    "PigeonApiKey": "{{.Data.PigeonApiKey}}",
    "ContactsApiApiKey": "{{.Data.ContactsApiApiKey}}",
    "IsContactsApiEnabled": "{{.Data.IsContactsApiEnabled}}",
	"IsCommunicationApiEnabled": "{{.Data.IsCommunicationApiEnabled}}",
	"IsEnabledFailedInPigeon": "{{.Data.IsEnabledFailedInPigeon}}",
	{{end}}
	{{with secret "secret/ebe_netcore_agents/central/common"}}
	"EnigmaApiKey": "{{.Data.EnigmaApiKey}}",
	"RewardsApiKey": "{{.Data.RewardsApiKey}}",
	{{end}}
	{{with secret "secret/it-cusco/it-cusco-clients/ebe_netcore_agents"}}
   	"CommunicationApiKey": "{{.Data.apiKey}}"
   	{{end}}
  },
  "Payment": {
    "ConnectionStrings": [
	  {
	    {{with secret "secret/it-dbops-sql/sql/credential/ebe_payment/mdb"}}
        "Name": "application_ebe",
	    "Value": "Server=mdb;uid={{.Data.username}};pwd={{.Data.password}};database=Application_EBE;Max Pool Size=50;APP=EBE_PAYMENTAGENTV2"
		{{end}}
	  },
	  {
	    {{with secret "secret/ebe_netcore_agents/central/Payment"}}
	    "Name": "RabbitMQ",
	    "Value": "host={{ env "RABBITMQ_HOST" }};username={{ env "RABBITMQ_USERNAME" }};password={{ env "RABBITMQ_PASSWORD" }};requestedHeartbeat=2;prefetchcount=1;publisherConfirms=true;timeout=20;persistentMessages=true"
		{{end}}
	  }
	],
	{{with secret "secret/ebe_netcore_agents/central/Payment"}}
	"CreditCardServiceApiKey": "{{.Data.CreditCardServiceApiKey}}",
	"CreditCardServiceClientId": {{.Data.CreditCardServiceClientId}},
	"ClientId": {{.Data.ClientId}},
	"BapiApiKey": "{{.Data.BapiApiKey}}",
	"BapiWhiteLabel": "{{.Data.BapiWhiteLabel}}",
    "GiftCardApiConfiguration": {
	  "ApiKey": "{{.Data.GiftCardApiConfiguration__ApiKey}}"
	},
	"BookingApiConfiguration": {
	  
	  "ClientId": {{.Data.BookingApiConfiguration__ClientId}},
	  "ApiKey": "{{.Data.BookingApiConfiguration__ApiKey}}"
	},
	{{end}}
	{{with secret "secret/ebe_netcore_agents/central/common"}}
	"RewardsApiKey": "{{.Data.RewardsApiKey}}",
	{{end}}
	"PaymentClientIdConfigurations": [
      {
      	{{with secret "secret/ebe_netcore_agents/central/Payment"}}
        "ClientId": "131",        
        "APIKey": "{{.Data.PaymentServiceAPIKey_SPWL}}"
        {{end}}
      },
	  {
      	{{with secret "secret/ebe_netcore_agents/central/Payment"}}
        "ClientId": "164",        
        "APIKey": "{{.Data.PaymentServiceAPIKey_PAN}}"
        {{end}}
      }
    ]
  },
  "Analytics": {
	"ConnectionStrings": [
	  {
	    {{with secret "secret/it-dbops-sql/sql/credential/ebe_analytics_agent_rw_user/mdb"}}
	    "Name": "application_ebe",
	    "Value": "Server=mdb;uid={{.Data.username}};pwd={{.Data.password}};database=Application_EBE;Max Pool Size=50;APP=EBE_ANALYTICS_AGENT",
		{{end}}
	  },
	  {
	    {{with secret "secret/ebe_netcore_agents/central/Analytics"}}
	    "Name": "RabbitMQ",
	    "Value": "host={{ env "RABBITMQ_HOST" }};username={{ env "RABBITMQ_USERNAME" }};password={{ env "RABBITMQ_PASSWORD" }};requestedHeartbeat=2;prefetchcount=1;publisherConfirms=true;timeout=20;persistentMessages=true"
		{{end}}
	  }
	]
  },
  "PushBookingSummary": {
    "ConnectionStrings": [
      {
	    {{with secret "secret/it-dbops-sql/sql/credential/ebe_push_booking_summary_agent_rw_user/mdb"}}
        "Name": "application_ebe",
        "Value": "Server=mdb;uid={{.Data.username}};pwd={{.Data.password}};database=Application_EBE;Max Pool Size=50;APP=EBE_PUSH_BOOKING_SUMMARY_AGENT;Timeout=1"
		{{end}}
      },
	  {
	    {{with secret "secret/it-dbops-sql/sql/credential/ebe_push_booking_summary_agent_rw_user/hisdb"}}
		"Name": "agoda_archived_booking",
		"Value": "Server=HK-QABEDB-2002;uid={{.Data.username}};pwd={{.Data.password}};database=agoda_archived_booking;Max Pool Size=50;APP=EBE_PUSH_BOOKING_SUMMARY_AGENT;Timeout=1"
		{{end}}
	  },
      {
	    {{with secret "secret/ebe_netcore_agents/central/PushBookingSummary"}}
        "Name": "RabbitMQ",
        "Value": "host={{ env "RABBITMQ_HOST" }};username={{ env "RABBITMQ_USERNAME" }};password={{ env "RABBITMQ_PASSWORD" }};requestedHeartbeat=2;prefetchcount=1;publisherConfirms=true;timeout=20;persistentMessages=true"
		{{end}}
      }
    ],
	{{with secret "secret/ebe_netcore_agents/central/PushBookingSummary"}}
    "CreditCardServiceClientId": {{.Data.CreditCardServiceClientId}},
    "CreditCardServiceApiKey": "{{.Data.CreditCardServiceApiKey}}",
	{{end}}
	{{with secret "secret/ebe_netcore_agents/central/common"}}
	"EnigmaApiKey": "{{.Data.EnigmaApiKey}}",
	"RewardsApiKey": "{{.Data.RewardsApiKey}}"
	{{end}}
  },
	{{with secret "secret/ebe_netcore_agents/central/AdpMessaging"}}
	"AdpMessaging": {
		"ApiKey": "{{.Data.ApiKey}}",
	}
	{{end}}
}
