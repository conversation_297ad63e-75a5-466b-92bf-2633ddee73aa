#!/usr/bin/env bash

# Set the errexit option
set -e #Exit when error

# Wait for sidecar containers to be ready
# Ref: https://privatecloud.pages.agodadev.io/docs/introduction/sidecar-containers/README.html#update-entrypoint-script
waitForSidecarContainers() {
    echo "Waiting for consul-sidecar"
    until curl --output /dev/null --silent --head --fail http://localhost:8500; do sleep 3; done
   
    echo "Waiting for vault-sidecar"
    until curl --output /dev/null --silent --head --fail http://localhost:8200/v1/sys/health; do sleep 3; done
   
    echo "Waiting for istio-sidecar"
    until curl --output /dev/null --silent --head --fail http://localhost:15021/healthz/ready; do sleep 3; done
   
    echo "Done waiting for sidecar containers"
}

APPSETTING_DC_FILE=""

echo "App starting in PC"

#Build application instance identifier
INSTANCE_IDENTIFIER=${APC_NETWORK_ZONE}_${AG_DC}

if [ "$APC_NETWORK_ZONE" == "prod" ]; then
  APPLICATION_FILE="application-${AG_DC}.config"
  APPSETTING_FILE="appsettings-prod-central.json"
  APPSETTING_DC_FILE="appsettings-prod-central-${AG_DC}.json"
  CREDENTIAL_FILE="credential-prod-central-${AG_DC}-json.ctmpl"
elif [ "$IS_DEVSTACK" == "true" ]; then
  APPLICATION_FILE="application-devstack.config"
  APPSETTING_FILE="appsettings-devstack-central.json"
  CREDENTIAL_FILE="credential-devstack-central-json.ctmpl"
else
  APPLICATION_FILE="application.config"
  APPSETTING_FILE="appsettings-qa-central.json"
  CREDENTIAL_FILE="credential-qa-central-json.ctmpl"
fi

## Add Private Cloud Override
echo "APPSETTING_PC_FILE: ${APPSETTING_PC_FILE}"
ln -sf /opt/ebe/agents/configs/${APPSETTING_PC_FILE} /opt/ebe/agents/publish/appsettings-privatecloud.json

waitForSidecarContainers

ln -sf /opt/ebe/agents/configs/${APPSETTING_FILE} /opt/ebe/agents/publish/appsettings.json

if [ -n "$APPSETTING_DC_FILE" ]; then
  ln -sf /opt/ebe/agents/configs/${APPSETTING_DC_FILE} /opt/ebe/agents/publish/appsettings-override-dc.json
fi

consul-template -config /etc/consul.d/template/config.json -template "/opt/ebe/agents/configs/${CREDENTIAL_FILE}:/opt/ebe/agents/publish/credential.json" -once
ln -sf /opt/ebe/agents/publish/Configs/${APPLICATION_FILE} /opt/ebe/agents/publish/Agoda.EBE.Agents.Runner.dll.config

echo "APPSETTING_FILE: ${APPSETTING_FILE}"
echo "APPSETTING_DC_FILE: ${APPSETTING_DC_FILE}"
echo "CREDENTIAL_FILE: ${CREDENTIAL_FILE}"
echo "APPLICATION_FILE: ${APPLICATION_FILE}"

ls -ln  /opt/ebe/agents/configs
ls -ln /opt/ebe/agents/publish/Configs | grep ${APPLICATION_FILE}

echo "starting agent. agent:${Agent} defaultPortNumber: ${PortNumber}, appDir:${APP_DIR}"
cd $APP_DIR/publish

start_service() {
  /usr/bin/dotnet "${APP_DIR}/publish/Agoda.EBE.Agents.Runner.dll" "${1}" "${2}">>"${APP_DIR}/${1}_log.txt" 2>&1 &
  echo "$!"
}

monitor() {
  while sleep 30; do
    for agent in "${agentsList[@]}"; do
      printf '\e[34m[LAST 100 LINES LOG OF %s]\e[0m\n' "${agent}"
      tail -n 100 "${APP_DIR}/${agent}_log.txt"
    done
  done
}

run_multiple_agents() {
  IFS='_'
  read -ra agentsList <<< "$Agent"

  agentCounter=0
  agentDefaultPort=${PortNumber}

  for agent in "${agentsList[@]}"; do
    printf '\e[33mStarting %s\e[0m\n' "${agent}"
    local pid
    port=$((agentCounter + agentDefaultPort))
    pid="$(start_service "${agent}" "${port}")"
    ps -u --pid "${pid}"
    agentsPidList+=("${pid}")
    agentCounter=${agentCounter}+1
  done

  printf '\n\e[32m----------------------------\e[0m\n'

  printf 'Started Agents PIDs: %s \n' "${agentsPidList[@]}"

  monitor "${agentsList[@]}"
}

run_multiple_agents "$@"