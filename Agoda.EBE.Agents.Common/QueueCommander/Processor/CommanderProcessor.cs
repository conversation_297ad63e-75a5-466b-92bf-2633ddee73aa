using System;
using System.Collections.Concurrent;
using System.Threading;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Base.Interface;
using Agoda.EBE.Agents.Common.EbeCommonException;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.QueueCommander.Object;
using Agoda.EBE.Agents.Common.QueueCommander.Processor.Interface;
using Agoda.EBE.Agents.Common.QueueCommander.Utils.Interface;
using Agoda.EBE.Agents.Common.QueueConsumer.Processor.Interface;
using Agoda.EBE.Agents.Common.Util;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.MQ;
using Agoda.EBE.Framework.MQ.Interface;
using Agoda.EBE.Framework.MQ.MessageType;
using EasyNetQ;

namespace Agoda.EBE.Agents.Common.QueueCommander.Processor
{
    public class CommanderProcessor: ICommanderProcessor
    {
        #region Private Members

        private readonly IConsumerProcessor _consumer;
        private readonly IRabbitMQAdapter _publishQueueAdepter;
        private readonly IQueueProcessorBase _queueProcessorBase;
        private readonly IMessaging _messaging;
        private readonly CommanderConfiguration _config;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly ConcurrentDictionary<string, int> _consumersDictionary;
        private readonly IQueueNameUtils _queueNameUtils;
        private readonly object _consumerLock = new object();
        

        #endregion

        #region Ctor

        public CommanderProcessor(
            IRabbitMQAdapter publishQueueAdepter,
            IConsumerProcessor consumer,
            IQueueProcessorBase queueProcessorBase,
            IMessaging messaging,
            CommanderConfiguration config,
            CancellationTokenSource cancellationTokenSource,
            IQueueNameUtils queueNameUtils
        )
        {
            _publishQueueAdepter = publishQueueAdepter;
            _consumer = consumer;
            _queueProcessorBase = queueProcessorBase;
            _messaging = messaging;
            _config = config;
            _cancellationTokenSource = cancellationTokenSource;
            _consumersDictionary = new ConcurrentDictionary<string, int>();
            _queueNameUtils = queueNameUtils;
            _messaging.ExceptionMessage.Send(
                $"Commander Processor Initialized", null,
                LogLevel.DEBUG);
        }

        #endregion

        #region Public Methods

        public void Process()
        {
            _queueProcessorBase.SubscribeAndProcess<CommandMessage>(_queueNameUtils.CommandQueue(),
                _config.CommanderQueueSize,
                _config.QueueConnectionString,
                _cancellationTokenSource,
                OnReceived);
        }
        
        public void OnReceived(IMessage<CommandMessage> message, MessageReceivedInfo info)
        {
            var queueSuffix = message.Body.QueueSuffix;
            try
            {
                if (!_consumersDictionary.ContainsKey(queueSuffix))
                {
                    lock (_consumerLock)
                    {
                        if (!_consumersDictionary.ContainsKey(queueSuffix))
                        {
                            var consumerQueueName = _queueNameUtils.ConsumerQueue(queueSuffix);
                            var dmcs = string.Join(",", _consumersDictionary.Keys);
                            _messaging.ExceptionMessage.Send(
                                $"Initial ${consumerQueueName} queue, Consumers: {_config.ConsumerQueueSize}, All Dmcs: {dmcs}",
                                null,
                                LogLevel.DEBUG);
                            CreateConsumers(consumerQueueName, _config.ConsumerQueueSize);
                            _consumersDictionary.Add(queueSuffix, _config.ConsumerQueueSize);
                        }
                    }
                }
            }
            catch (NonRetryableException ex)
            {
                _messaging.ExceptionMessage.Send($"Fail to initial ${queueSuffix} queue", ex, LogLevel.FATAL);
            }
            catch(Exception ex)
            {
                _messaging.ExceptionMessage.Send($"Fail to initial ${queueSuffix} queue", ex, LogLevel.FATAL);
                _publishQueueAdepter.PublishToRetryQueue(message.Body, _queueNameUtils.CommandQueue(), 0, 0);
            }
        }
        
        #endregion

        #region Private Methods

        private void CreateConsumers(string queueName, int size)
        {
            _queueProcessorBase.SubscribeAndProcess<DefaultMessage>(
                queueName,
                size,
                _config.QueueConnectionString,
                _cancellationTokenSource,
                ConsumerCallback);
        }

        private void ConsumerCallback(IMessage<DefaultMessage> message, MessageReceivedInfo info)
        {
            var tagBuilder = new TagBuilder().AddBookingTag(message.Body.BookingId).AddBookingSessionIdTag(message.Body.BookingSessionId);
            try
            {
                _consumer.Process(message.Body, info.Redelivered);
            }
            catch (NonRetryableException ex)
            {
                _messaging.ExceptionMessage.Send(ex.Message, ex, LogLevel.FATAL, tagBuilder.Build());
            }
            catch (ProvisioningDuplicationException ex)
            {
                _messaging.ExceptionMessage.Send(ex.Message, ex, LogLevel.WARN, tagBuilder.Build());
            }
            catch (ProcessorException ex) when (ex.EnableLinearRetry)
            {
                message.Body.NumRetry++;
                var retryInput = new RetryStrategy.RetryEstimatorInput();
                var retryPeriod = RetryStrategy.TruncatedLinearRetryPeriod(message.Body.NumRetry, retryInput);
                var retryQueue = _queueNameUtils.BaseQueue() + "." + retryPeriod + ".Retry";
                _messaging.ExceptionMessage.Send(ex.Message, ex, LogLevel.WARN, tagBuilder.Build());
                _messaging.ExceptionMessage.Send($"Publish {message.Body.BookingId} to retry queue of {retryQueue}", null,
                    LogLevel.DEBUG, tagBuilder.Build());
                PublishToRetryQueue(message.Body, _queueNameUtils.BaseQueue(), retryQueue, retryPeriod);
            }
            catch (Exception ex)
            {
                message.Body.NumRetry++;
                _messaging.ExceptionMessage.Send(ex.Message, ex, LogLevel.WARN, tagBuilder.Build());
                _messaging.ExceptionMessage.Send($"Publish {message.Body.BookingId} to retry queue of {_queueNameUtils.BaseQueue()}", null,
                    LogLevel.DEBUG, tagBuilder.Build());
                _publishQueueAdepter.PublishToRetryQueue(message.Body, _queueNameUtils.BaseQueue(), 0, 0);
            }
        }
        
        private void PublishToRetryQueue(IDefaultMessage message, string originalQueueName, string retryQueueName, int ttlInSeconds)
        {
            ObjectUtils.ExecuteAsyncSafely(() => _publishQueueAdepter.PublishToRetryQueueAsync((DefaultMessage) message, originalQueueName, retryQueueName, ttlInSeconds, 0, 0));
        }

        #endregion
    }
}
