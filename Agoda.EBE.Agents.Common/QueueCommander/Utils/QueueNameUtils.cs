using Agoda.EBE.Agents.Common.QueueCommander.Utils.Interface;

namespace Agoda.EBE.Agents.Common.QueueCommander.Utils
{
    public class QueueNameUtils: IQueueNameUtils
    {
        private readonly string _baseQueueName;
        private readonly string _suffixType;
        public QueueNameUtils(string baseQueueName, string suffixType)
        {
            _baseQueueName = baseQueueName;
            _suffixType = suffixType;
        }

        public string BaseQueue() => _baseQueueName;
        public string RetryQueue() => $"{_baseQueueName}.Retry";
        public string CommandQueue() => $"{_baseQueueName}.Cmd";
        public string CommandRetryQueue() => $"{_baseQueueName}.Cmd.Retry";
        public string ConsumerQueue(string suffix) => $"{_baseQueueName}.{_suffixType}.{suffix}";
    }
}