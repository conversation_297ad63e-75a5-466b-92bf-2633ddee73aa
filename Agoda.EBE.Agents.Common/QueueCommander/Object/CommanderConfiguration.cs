namespace Agoda.EBE.Agents.Common.QueueCommander.Object
{
    public class CommanderConfiguration
    {
        public CommanderConfiguration(string queueBaseName, int consumerQueueSize, int commanderQueueSize, string queueConnectionString)
        {
            QueueBaseName = queueBaseName;
            ConsumerQueueSize = consumerQueueSize;
            CommanderQueueSize = commanderQueueSize;
            QueueConnectionString = queueConnectionString;
        }

        public string QueueBaseName { get; set; }
        public int ConsumerQueueSize { get; set; }
        public int CommanderQueueSize { get; set; }
        public string QueueConnectionString { get; }
    }
}