using System.Collections.Generic;
using Agoda.BookingApi.GenClient.Models;
using Newtonsoft.Json;

namespace Agoda.EBE.Agents.Common.Model
{
    public class PreAuth3dsRequiredResult
    {
        [JsonProperty("continuation")]
        public Continuation Continuation { get; set; }
        
        [JsonProperty("redirect3ds")]
        public Payment3DsResponse Redirect3Ds { get; set; }
    }

    public class Payment3DsResponse
    {
        [JsonProperty(PropertyName = "post3DFields")]
        public IDictionary<string, string> Post3DFields { get; set; }

        [JsonProperty(PropertyName = "issuerUrl")]
        public string IssuerUrl { get; set; }

        [JsonProperty(PropertyName = "require3DFields")]
        public IList<string> Require3DFields { get; set; }

        [JsonProperty(PropertyName = "returnUrlField")]
        public string ReturnUrlField { get; set; }
    }

    public class Redirect
    {
        [JsonProperty("issuerUrl")]
        public string IssuerUrl { get; set; }
        
        [JsonProperty("postFields")]
        public Dictionary<string, string> PostFields { get; set; }
    }
}