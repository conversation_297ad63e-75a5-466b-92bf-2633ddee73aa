using System.Collections.Generic;
using Newtonsoft.Json;

namespace Agoda.EBE.Agents.Common.Model
{
    public class ClientSideJavaScriptPaymentResult
    {
        [JsonProperty("continuation")] 
        public Continuation Continuation { get; set; }

        [JsonProperty("tokenConfiguration")] 
        public TokenConfiguration TokenConfiguration { get; set; }
    }

    public class Continuation
    {
        [JsonProperty("gatewayId")] 
        public string GatewayId { get; set; }
        
        [JsonProperty("gatewayInfoId")] 
        public string GatewayInfoId { get; set; }
        
        [JsonProperty("transactionId")] 
        public string TransactionId { get; set; }
        
        [JsonProperty("paymentToken")] 
        public PaymentToken PaymentToken { get; set; }
    }

    public class PaymentToken
    {
        [JsonProperty("tokenType")]
        public string TokenType { get; set; }
        
        [JsonProperty("tokenValue")]
        public string TokenValue { get; set; }
        
        [JsonProperty("additionalInfo")]
        public Dictionary<string, string> AdditionalInfo { get; set; }
    }

    public class TokenConfiguration
    {
        [JsonProperty("externalScriptUrl")]
        public string MaskedCreditCardNumber { get; set; }
        
        [JsonProperty("localScriptType")]
        public string LocalScriptType { get; set; }
        
        [JsonProperty("tokenParameters")]
        public Dictionary<string, string> TokenParameters { get; set; }
    }
}