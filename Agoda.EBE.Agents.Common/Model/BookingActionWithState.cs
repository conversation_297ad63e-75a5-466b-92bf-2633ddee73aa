using System;
using Agoda.EBE.Framework.Objects;
using Newtonsoft.Json;

namespace Agoda.EBE.Agents.Common.Model
{
    public class BookingActionWithState
    {
        [JsonProperty("actionId")]
        public long ActionId { get; set; }

        [JsonProperty("itineraryId")]
        public long ItineraryId { get; set; }

        [JsonProperty("bookingType")]
        public int BookingType { get; set; }

        [JsonProperty("bookingId")]
        public long BookingId { get; set; }

        [JsonProperty("memberId")]
        public int MemberId { get; set; }

        [JsonProperty("actionTypeId")]
        public int ActionTypeId { get; set; }

        [JsonProperty("correlationId")]
        public string CorrelationId { get; set; }

        [JsonProperty("requestId")]
        public string RequestId { get; set; }

        [JsonProperty("workflowId")]
        public int WorkflowId { get; set; }

        [JsonProperty("workflowStateId")]
        public int WorkflowStateId { get; set; }

        [JsonProperty("stateSchemaVersion")]
        public int StateSchemaVersion { get; set; }

        [JsonProperty("state")]
        public BookingActionState State { get; set; }

        [JsonProperty("storeFrontId")]
        public int StoreFrontId { get; set; }

        [JsonProperty("languageId")]
        public int LanguageId { get; set; }

        [JsonProperty("recStatus")]
        public int RecStatus { get; set; }

        [JsonProperty("recCreatedWhen")]
        public DateTime RecCreatedWhen { get; set; }

        [JsonProperty("recModifyWhen")]
        public DateTime RecModifyWhen { get; set; }

        [JsonProperty("productTypeId")]
        public int ProductTypeId { get; set; }
    }
}