using Newtonsoft.Json;

namespace Agoda.EBE.Agents.Common.Model
{
    public class AgentBookingActionMessage
    {
        [JsonProperty("preAuth3dsRequired")]
        public PreAuth3dsRequiredResult PreAuth3dsRequiredResult { get; set; }
        
        [JsonProperty("provisioningResult")]
        public int? ProvisioningResult { get; set; }
        
        [JsonProperty("jtbPartnerProvisioningResult")]
        public JtbPartnerProvisioningResult JtbPartnerProvisioningResult { get; set; }
        
        [Json<PERSON>roperty("clientSideJavaScriptPaymentResult")]
        public ClientSideJavaScriptPaymentResult ClientSideJavaScriptPaymentResult { get; set; }
        
        [JsonProperty("provisioningData")]
        public ProvisioningData ProvisioningData { get; set; }
    }
}