using System;
using Agoda.EBE.Workflow.Structure;
using Agoda.EBE.Workflow.Structure.Request;

namespace Agoda.EBE.Agents.Common.Request
{
    public class CommonChangeBookingStateRequest 
    {
        public long ItineraryID { get; set; }
    
        public int BookingID { get; set; }
    
        public int WorkflowActionResultID { get; set; }
    
        public int WorkflowReasonID { get; set; }
    
        public string Remark { get; set; }
    
        public Guid EmailID { get; set; }
    
        public Guid UserID { get; set; }
    
        public bool isCuscoEmailId { get; set; }
    
        public string NodeIdentifier { get; set; }
        
        public int? WorkflowID { get; set; }
        
        public bool? IsPartialTransfer { get; set; }
        
        public bool? IsSyncTransfer { get; set; }
        
        public long? OperationId { get; set; }
    }
    
}