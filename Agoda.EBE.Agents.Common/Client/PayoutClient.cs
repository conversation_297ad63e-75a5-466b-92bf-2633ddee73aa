using System.Net.Http;
using System.Net.Http.Headers;
using Agoda.EBE.Agents.Common.Configuration.Interface;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.ServiceDiscovery;
using Agoda.PayoutApi.Client.Generated;

public static class PayoutClientFactory
{
    public static PayoutAPI GetPayoutClient(IPayoutConfiguration config, PayoutApiDiscovery payoutApiDiscovery,
        IMessaging messaging)
    {
        var payoutToken = config.PayoutToken; //pragma: allowlist secret
        var clientHandler = new HttpClientHandler(){UseProxy = false};
        clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
        var httpClient = new System.Net.Http.HttpClient(clientHandler);
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", payoutToken);
        var payoutLoadBalancingConfig =
            PayoutLoadBalancingConfigFactory.GetPayoutLoadBalancingConfig(config, payoutApiDiscovery, messaging);
        var payoutClient = new PayoutAPI(httpClient,payoutLoadBalancingConfig);
        return payoutClient;
    }
}