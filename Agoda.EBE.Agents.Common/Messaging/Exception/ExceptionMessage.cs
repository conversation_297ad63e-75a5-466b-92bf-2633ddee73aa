﻿using Agoda.EBE.Framework.Messaging;
using System;
using System.Collections.Generic;
using System.Text;

namespace Agoda.EBE.Agents.Common.Messaging
{
    public class ExceptionMessage : EBEExceptionMessage
    {
        public override int StorefrontID => this.storeFrontID; 

        private int storeFrontID { get; set; }

        public ExceptionMessage(int storeFrontID)
        {
            this.storeFrontID = storeFrontID;
        }
    }
}
