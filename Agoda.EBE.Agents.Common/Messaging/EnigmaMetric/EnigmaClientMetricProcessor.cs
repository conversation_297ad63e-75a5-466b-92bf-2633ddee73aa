﻿using System;
using System.Collections.Generic;
using System.Linq;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.RoundRobin;

namespace Agoda.EBE.Agents.Common.Messaging
{
    public class EnigmaClientMetricProcessor
    {
        private readonly IMessaging _messaging;

        public EnigmaClientMetricProcessor(IMessaging messaging)
        {
            this._messaging = messaging;
        }

        public void Handle(LogLevel level, Dictionary<string, object> data, string endpoint, long elapsedMilliseconds)
        {
            var enigmaClientCall = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            var metricData = data.ToDictionary(k => k.Key, k => k.Value?.ToString() ?? "");
            metricData.Add("endpoint", endpoint);
            metricData.Add("elapsedMilliseconds", elapsedMilliseconds.ToString());
            enigmaClientCall.Tags = metricData;
            enigmaClientCall.TrackValue((int)Enum.Measurement.PiiClientMetric, "elapsedMilliseconds", (int)elapsedMilliseconds);
            _messaging.ExceptionMessage.Send(
                "Invoked Enigma for Endpoint: " + endpoint + ". elapsedMilliseconds: " + elapsedMilliseconds
                , null, level);
        }

        public void HandleError(string clientName,Exception exception, string routeName, ExecuteResult executeResult,object payload=null)
        {
            _messaging.ExceptionMessage.Send(
                    "Exception while hitting Enigma for clientName: " + clientName + ". Route: " + routeName
                    , exception, LogLevel.ERROR);
            }
    }
}
