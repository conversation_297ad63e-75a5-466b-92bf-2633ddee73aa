﻿using Agoda.EBE.Framework.Messaging;

namespace Agoda.EBE.Agents.Common.Messaging
{
    public class MeasurementMessageFactory : IMeasurementMessageFactory
    {
        private readonly string _metricName;
        private readonly string _componentName;

        public MeasurementMessageFactory(string metricName, string componentName)
        {
            _metricName = metricName;
            _componentName = componentName;
        }

        public IEBEMeasurementMessage CreateNewMeasurement()
        {
            return new MeasurementMessage(_metricName, _componentName);
        }
    }
}
