﻿using System;
using System.Collections.Generic;
using System.Text;
using Agoda.EBE.Framework.Messaging;

namespace Agoda.EBE.Agents.Common.Messaging
{
    public class MeasurementMessage : EBEMeasurementMessage
    {
        private string metricName;
        private string componentName;

        public MeasurementMessage(string metricName, string componentName)
        {
            this.metricName = metricName;
            this.componentName = componentName;
        }

        public override string MetricName => this.metricName;
        public override string ComponentName => this.componentName;
    }
}
