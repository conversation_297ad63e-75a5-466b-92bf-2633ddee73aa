﻿using System;
using System.Collections.Generic;
using System.Text;
using Agoda.EBE.Agents.Common.Messaging.Log;
using Agoda.EBE.Framework.Messaging;

namespace Agoda.EBE.Agents.Common.Messaging
{
    public interface IMessaging
    {
        IMeasurementMessageFactory MeasurementMessageFactory { get; }
        IEBEExceptionMessage ExceptionMessage { get; }
        void SendLogToHadoop(EbeExternalApiLogMessage logMessage);
    }
}
