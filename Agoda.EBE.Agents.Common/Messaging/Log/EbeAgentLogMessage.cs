using Agoda.EBE.Framework;
using Agoda.EBE.Framework.Messaging;

namespace Agoda.EBE.Agents.Common.Messaging.Log
{
    public class EbeAgentLogMessage: EBEGenericMessage
    {
        public string RefId { get; }
        public string Content { get; }
        public int LogRefType { get; }
        public string FunctionName { get; }
        public string ClassName { get; }
        public string Agent { get; }

        public long ItineraryId { get; }
        
        public EbeAgentLogMessage(string refId, string agent, string className, string functionName, string content, ConstantEnum.LogRefType logRefType, long itineraryId)
        {
            RefId = refId;
            Content = content;
            LogRefType = (int)logRefType;
            FunctionName = functionName;
            ClassName = className;
            Agent = agent;
            ItineraryId = itineraryId;

        }
    }
}