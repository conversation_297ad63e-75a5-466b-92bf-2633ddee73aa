using Agoda.EBE.Framework;

namespace Agoda.EBE.Agents.Common.Messaging.Log
{
    public class EbePaymentApiLogMessage : EbeExternalApiLogMessage
    {
        public EbePaymentApiLogMessage(string refId, object request, object response, string className, string functionName, ConstantEnum.LogRefType logRefType)
        {
            RefId = refId;
            Request = DataUtils.JsonSerializeObjWithMaskData(request);
            Response = DataUtils.JsonSerializeObjWithMaskData(response);
            ClassName = className;
            FunctionName = functionName;
            LogRefType = (int)logRefType;
        }
        
    }
}