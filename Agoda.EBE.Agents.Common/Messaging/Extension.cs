using System;
using System.Collections.Generic;
using System.Linq;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Object;
using Dapper;

namespace Agoda.EBE.Agents.Common.Messaging
{
    public static class MessagingExtension
    {
        public static void LogExceptionWithParameterInfo(this IMessaging messaging, string message, Exception ex, IEnumerable<ParameterInfo> parameterInfos = null,
            LogLevel level = LogLevel.FATAL)
        {
            if (parameterInfos != null)
            {
                var parameterInfosStr = string.Join(", ", parameterInfos.Select(param => $"{param.Name}: {param.Value}"));
                messaging.ExceptionMessage.Send($"{message}, {parameterInfosStr}", ex, level);
            }
            else
            {
                messaging.ExceptionMessage.Send(message, ex, level);
            }
        }
        
        public static void LogExceptionWithDynamicParameters(this IMessaging messaging, string message, Exception ex,
            DynamicParameters parameters = null, LogLevel level = LogLevel.FATAL)
        {
            if (parameters != null)
            {   
                var parameterInfosStr = string.Join(", ", parameters.ParameterNames.Select(param => $"{param}: {parameters.Get<string>(param)}"));
                messaging.ExceptionMessage.Send($"{message}, {parameterInfosStr}", ex, level);
            }
            else
            {
                messaging.ExceptionMessage.Send(message, ex, level);
            }
        }
    }
}