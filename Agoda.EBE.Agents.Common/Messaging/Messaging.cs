﻿using Agoda.EBE.Agents.Common.Messaging.Log;
using Agoda.EBE.Framework.Messaging;

namespace Agoda.EBE.Agents.Common.Messaging
{
    public class Messaging : IMessaging
    {
        public IMeasurementMessageFactory MeasurementMessageFactory { get; }
        public IEBEExceptionMessage ExceptionMessage { get; }

        public Messaging(IMeasurementMessageFactory measurementMessageFactory, IEBEExceptionMessage exceptionMessage)
        {
            this.MeasurementMessageFactory = measurementMessageFactory;
            this.ExceptionMessage = exceptionMessage;
        }
        public void SendLogToHadoop(EbeExternalApiLogMessage logMessage)
        {
            logMessage.Send();
        }

    }
}
