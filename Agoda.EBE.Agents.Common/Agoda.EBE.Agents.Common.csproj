﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <LangVersion>default</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Agoda.BookingApi.GenClient" Version="6.549.0" />
    <PackageReference Include="Agoda.Builds.Metrics" Version="1.0.113" />
    <PackageReference Include="Agoda.Config.Consul" Version="5.2.140" />
    <PackageReference Include="Agoda.Cusco.CommunicationApi.Client" Version="1.767.0" />
    <PackageReference Include="Agoda.Cusco.Customer.Api.Client" Version="5.6.14" />

    <PackageReference Include="Agoda.Cusco.Delivery.Api.Client" Version="3.0.0.64" />
    <PackageReference Include="Agoda.EBE.Framework" Version="1.1.0.2903" />
    <PackageReference Include="Agoda.EBE.Framework.MQ" Version="1.1.0.1435" />

    <PackageReference Include="Agoda.EBE.Workflow.Client" Version="2.0.776" />
    <PackageReference Include="Agoda.ExternalLoyaltyApi.Client" Version="24.12.1" />
    <PackageReference Include="Agoda.Payment.Client" Version="1.824.0" />
    <PackageReference Include="Agoda.Payments.Upc.Integrate.Client" Version="1.0.8318.25625" />
    <PackageReference Include="Agoda.PayoutApi.Client.Generated" Version="0.82.3-RELEASE" />
    <PackageReference Include="Agoda.Secure.Structure" Version="1.344.0" />
    <PackageReference Include="Agoda.Upc.Structure" Version="2.0.7961.31976" />
    <PackageReference Include="Agoda.WhiteLabelApi.Client" Version="10.1124.0" />
    <PackageReference Include="Agoda.Enigma.Client" Version="2.0.99" />
    <PackageReference Include="Autofac" Version="5.2.0" />
    <PackageReference Include="Dapper" Version="2.0.35" />
    <PackageReference Include="GitForWindows" Version="2.22.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.FxCopAnalyzers" Version="3.3.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="3.1.3" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="3.1.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="NUnit" Version="3.12.0" />
    <PackageReference Include="OpenCover" Version="4.7.922" />
    <PackageReference Include="Roslynator.Analyzers" Version="4.12.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.Data.SqlClient" Version="4.5.1" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Configuration\Interface\IConsoleConfiguration.cs">
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Enums\" />
  </ItemGroup>
</Project>
