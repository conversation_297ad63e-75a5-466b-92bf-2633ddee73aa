﻿using Agoda.EBE.Framework.MQ;
using Agoda.EBE.Framework.MQ.Interface;
using Agoda.EBE.Framework.MQ.MessageType;
using EasyNetQ;
using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Base.Interface;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Util;
using Agoda.EBE.Framework.Messaging;

namespace Agoda.EBE.Agents.Common.Base
{
    public class QueueProcessorBase: IQueueProcessorBase
    {
        private readonly ConcurrentBag<IRabbitMQAdapter> _adapterList = new ConcurrentBag<IRabbitMQAdapter>();
        private readonly IRabbitMQAdapter _publishQueueAdepter;
        
        public QueueProcessorBase(IRabbitMQAdapter publishQueueAdepter)
        {
            _publishQueueAdepter = publishQueueAdepter;
        }
        
        protected void UnSubscribe()
        {
            while (_adapterList.TryTake(out IRabbitMQAdapter result))
            {
                result.Dispose();
            }
        }


        public void SubscribeAndProcess<TMessage>(string queueName, int maxSubscriber, string connectionString,
            CancellationTokenSource tokenSource, Action<IMessage<TMessage>, MessageReceivedInfo> onReceive)
            where TMessage : class
        {
            if (tokenSource.IsCancellationRequested) return;
            
            Parallel.For(
                0, maxSubscriber,
                new ParallelOptions
                {
                    MaxDegreeOfParallelism = maxSubscriber,
                    CancellationToken = tokenSource.Token
                },
                idx =>
                {
                    IRabbitMQAdapter adapter = new RabbitMQAdapter(connectionString);
                                       adapter.Subscribe(queueName, onReceive);
                    _adapterList.Add(adapter);
                });
        }
        
        public void SubscribeAndProcess<TMessage>(string queueName, int maxSubscriber, RabbitMQAdapterConfiguration configuration,
            CancellationTokenSource tokenSource, Action<IMessage<TMessage>, MessageReceivedInfo> onReceive)
            where TMessage : class
        {
            if (tokenSource.IsCancellationRequested) return;
            
            Parallel.For(
                0, maxSubscriber,
                new ParallelOptions
                {
                    MaxDegreeOfParallelism = maxSubscriber,
                    CancellationToken = tokenSource.Token
                },
                idx =>
                {
                    IRabbitMQAdapter adapter = new RabbitMQAdapter(configuration);
                    adapter.Subscribe(queueName, onReceive);
                    _adapterList.Add(adapter);
                });
        }

        public virtual void PublishToRetryQueue(IDefaultMessage message, string originalQueueName,  int retrySleepSeconds = 0)
        {
            _publishQueueAdepter.PublishToRetryQueue((DefaultMessage)message, originalQueueName, retrySleepSeconds, 0);
        }
        
        public virtual void PublishToRetryQueue(IDefaultMessage message, string originalQueueName, string retryQueueName, int ttlInSeconds)
        {
            ObjectUtils.ExecuteAsyncSafely(() => _publishQueueAdepter.PublishToRetryQueueAsync((DefaultMessage) message, originalQueueName, retryQueueName, ttlInSeconds, 0, 0));
        }

        public virtual void KickToWaitForRetryState(IMessaging messaging, IMessage<DefaultMessage> message,
            System.Enum measurementEnum, string queueName)
        {
            var retryStat = messaging.MeasurementMessageFactory.CreateNewMeasurement();
            retryStat.BeginTrack();
            var tagBuilder = new TagBuilder();
            tagBuilder.AddBookingTag(message.Body.BookingId);
            tagBuilder.AddBookingSessionIdTag(message.Body.BookingSessionId);
            try
            {
                message.Body.NumRetry++;
                messaging.ExceptionMessage.Send($"Publish {message.Body.BookingId} to retry queue of {queueName}", null,
                    LogLevel.DEBUG, tagBuilder.Build());
                PublishToRetryQueue(message.Body, queueName);
                retryStat.EndTrack(measurementEnum);
            }
            catch (Exception exWhenPublishToRetryQueue)
            {
                retryStat.EndTrack(measurementEnum, false);
                messaging.ExceptionMessage.Send(
                    $"PublishToRetryQueue Fail. BookingID: {message.Body.BookingId}, stateInQueue: {message.Body.WorkflowStateId}",
                    exWhenPublishToRetryQueue,
                    LogLevel.WARN,
                    tagBuilder.Build());
                throw;
            }
        }
        
        public virtual void KickToWaitForRetryStateWithCustomRetry(IMessaging messaging, IMessage<DefaultMessage> message,
            System.Enum measurementEnum, string queueName, Func<int, RetryStrategy.RetryEstimatorInput, int> retryEstimator, RetryStrategy.RetryEstimatorInput retryEstimatorParams)
        {
            var retryStat = messaging.MeasurementMessageFactory.CreateNewMeasurement();
            retryStat.BeginTrack();
            var tagBuilder = new TagBuilder();
            tagBuilder.AddBookingTag(message.Body.BookingId);
            tagBuilder.AddBookingSessionIdTag(message.Body.BookingSessionId);
            try
            {
                message.Body.NumRetry++;
                var retryPeriod = retryEstimator(message.Body.NumRetry, retryEstimatorParams);
                var retryQueue = queueName + "." + retryPeriod + ".Retry";
                messaging.ExceptionMessage.Send($"Publish {message.Body.BookingId} to retry queue of {retryQueue}", null,
                    LogLevel.DEBUG, tagBuilder.Build());
                PublishToRetryQueue(message.Body, queueName, retryQueue, retryPeriod);
                retryStat.EndTrack(measurementEnum);
            }
            catch (Exception exWhenPublishToRetryQueue)
            {
                retryStat.EndTrack(measurementEnum, false);
                messaging.ExceptionMessage.Send(
                    $"PublishToRetryQueue Fail. BookingID: {message.Body.BookingId}, stateInQueue: {message.Body.WorkflowStateId}",
                    exWhenPublishToRetryQueue,
                    LogLevel.WARN,
                    tagBuilder.Build());
                throw;
            }
        }
    }
}
