using System;
using System.Collections.Generic;
using System.Diagnostics;
using Agoda.EBE.Agents.Common.Messaging;
using SystemEnum = System.Enum;
using AdpMeasurementMessage = Agoda.Adp.Messaging.Client.MeasurementMessage;

namespace Agoda.EBE.Agents.Common.Base
{
    public abstract class BaseProxy
    {
        private const string BaseMetricName = "ebe_net_core.common.external";
        private const string AdpMessagingKey = "ebe";

        protected abstract IMeasurementMessageFactory MeasurementMessageFactory { get; }

        protected abstract ExternalServiceName ApiName { get; }

        protected TResult MeasureCall<TResult>(
            SystemEnum methodName,
            Func<TResult> methodFunc,
            Func<TResult, bool> defineSuccessFunc,
            Func<Exception, TResult> onError = null,
            Func<TResult, Dictionary<string, string>> additionalTagsFunc = null
        )
        {
            var legacyEbeMeasurement = MeasurementMessageFactory.CreateNewMeasurement();
            var proxyMeasurement = new AdpMeasurementMessage(BaseMetricName);
            proxyMeasurement.AddTag("component_name", legacyEbeMeasurement.ComponentName);
            proxyMeasurement.AddTag("api_name", ApiName.ToString());
            proxyMeasurement.AddTag("method_name", methodName.ToString());

            var isSuccess = false;
            var additionalTags = new Dictionary<string, string>();

            var stopWatch = Stopwatch.StartNew();
            legacyEbeMeasurement.BeginTrack();
            try
            {
                var response = methodFunc();
                isSuccess = defineSuccessFunc(response);

                if (additionalTagsFunc != null)
                {
                    additionalTags = additionalTagsFunc(response);
                }

                return response;
            }
            catch (Exception ex)
            {
                isSuccess = false;
                if (onError == null) throw;
                return onError(ex);
            }
            finally
            {
                if (additionalTags != null)
                {
                    foreach (var tag in additionalTags)
                    {
                        legacyEbeMeasurement.Tags[tag.Key] = tag.Value;
                        proxyMeasurement.AddTag(tag.Key, tag.Value);
                    }
                }

                stopWatch.Stop();
                legacyEbeMeasurement.EndTrack(methodName, isSuccess);

                proxyMeasurement.MeasurementValue = (long)stopWatch.Elapsed.TotalMilliseconds;
                proxyMeasurement.AddTag("is_success", isSuccess.ToString());
                proxyMeasurement.SendAsync(AdpMessagingKey);
            }
        }
    }
}