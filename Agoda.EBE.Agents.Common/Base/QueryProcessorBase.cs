﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Agoda.EBE.Agents.Common.Base
{
    public abstract class QueryProcessorBase
    {
        protected abstract List<object> GetData();
        public void ProcessBatch(List<object> storeProcDataList,CancellationTokenSource tokenSource,int maxProcessPararell,ExecuteHandler handler)
        {
            Parallel.ForEach(
                storeProcDataList,
                new ParallelOptions
                {
                    MaxDegreeOfParallelism = maxProcessPararell,
                    CancellationToken = tokenSource.Token
                },
                x =>
                {
                    handler(x);
                }
            );
        }

        public delegate void ExecuteHandler(object x);
    }
}
