using System;
using System.Threading;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Framework.MQ;
using Agoda.EBE.Framework.MQ.MessageType;
using EasyNetQ;

namespace Agoda.EBE.Agents.Common.Base.Interface
{
    public interface IQueueProcessorBase
    {
        void SubscribeAndProcess<TMessage>(string queueName, int maxSubscriber, string connectionString,
            CancellationTokenSource tokenSource, Action<IMessage<TMessage>, MessageReceivedInfo> onReceive)
            where TMessage : class;
        
        void SubscribeAndProcess<TMessage>(string queueName, int maxSubscriber, RabbitMQAdapterConfiguration configuration,
            CancellationTokenSource tokenSource, Action<IMessage<TMessage>, MessageReceivedInfo> onReceive)
            where TMessage : class;
        
        void PublishToRetryQueue(IDefaultMessage message, string originalQueueName,int retrySleepSeconds);

        void KickToWaitForRetryState(IMessaging messaging, IMessage<DefaultMessage> message,
            System.Enum measurementEnum, string queueName);
    }
}