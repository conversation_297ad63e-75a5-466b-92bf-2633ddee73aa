using System;
using System.Net;
using System.Threading;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Configuration;
using Agoda.EBE.Agents.Common.Configuration.Interface;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Util;
using Agoda.WhiteLabelApi.Client;
using Autofac;

namespace Agoda.EBE.Agents.Common.Base
{
    public abstract class RunnerBase
    {
        protected string AgentName { get; }
        protected IConsoleConfiguration ConsoleConfig { get; }
        protected CancellationTokenSource CancelToken { get; }

        public abstract void Start();

        public abstract void Stop();

        protected abstract void GetConfig();

        public RunnerBase(string agentName, IConsoleConfiguration consoleConfig, CancellationTokenSource cancelToken)
        {
            AgentName = agentName;
            CancelToken = cancelToken;
            ConsoleConfig = consoleConfig;

            InitializeSetting();
            GetConfig();
        }

        private void InitializeSetting()
        {
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
        }
        
        protected void InitWhiteLabelClientWithRetry(IContainer container, WhiteLabelClientConfiguration config)
        {
            var messaging = container.Resolve<IMessaging>();
            var retryInterval = TimeSpan.FromMilliseconds(config.RetryInitializationInterval ?? 1000);
            var maxAttemptCount = config.RetryInitializationCount ?? 60;

            try
            {
                Retry.Do(() =>
                {
                    try
                    {
                        InitWhiteLabelClient(container, config);
                    }
                    catch (Exception err)
                    {
                        messaging.ExceptionMessage.Send(err.Message, err, LogLevel.ERROR);
                        throw;
                    }
                }, retryInterval, maxAttemptCount);
            }
            catch (Exception err)
            {
                messaging.ExceptionMessage.Send(
                    $"Unable to start white label client after {maxAttemptCount} retries.",
                    err,
                    LogLevel.FATAL);
                throw;
            }
        }

        private void InitWhiteLabelClient(IContainer container, WhiteLabelClientConfiguration config)
        {
            try
            {
                container.Resolve<IWhiteLabelClient>()
                    .InitiateAsync(config.InitiateAsyncInterval)
                    .Wait();
            }
            catch (Exception e)
            {
                throw new WhiteLabelClientInitializationException(e);
            }
        }
    }
}
