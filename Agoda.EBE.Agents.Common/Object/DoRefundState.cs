namespace Agoda.EBE.Agents.Common.Object
{
    public class DoRefundState
    {
        public decimal AmountToRefund { get; set; }
        public int WorkflowActionResult { get; set; } // for update workflow service
        public int FinalActionResult { get; set; } // for result action result response
        public string Remark { get; set; }
        public bool ShouldProcessToNextStep { get; set; }
        public bool ExternalLoyaltyInfoExists { get; set; }
        public bool EnableLoyaltyRefundProcessedEmail { get; set; }
    }
}