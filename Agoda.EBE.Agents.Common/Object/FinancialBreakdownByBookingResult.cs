using System;

namespace Agoda.EBE.Agents.Common.Object
{
    public class FinancialBreakdownByBookingResult:IEquatable<FinancialBreakdownByBookingResult>
    {
        public int ItemId { get; set; }
        public int TypeId { get; set; }
        public decimal LocalAmount { get; set; }
        public decimal ExchangeRate { get; set; }
        public decimal USDAmount { get; set; }
        public int Quantity { get; set; }

        public bool Equals(FinancialBreakdownByBookingResult other)
        {
            if (other == null) return false;
            
            return (ItemId == other.ItemId && TypeId == other.TypeId && LocalAmount == other.LocalAmount &&
                    ExchangeRate == other.ExchangeRate && USDAmount == other.USDAmount && Quantity == other.Quantity);
        }
    }
}