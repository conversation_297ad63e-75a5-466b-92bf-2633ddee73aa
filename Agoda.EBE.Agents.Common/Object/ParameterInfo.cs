﻿using System;
using System.Data;
using System.Data.SqlClient;

namespace Agoda.EBE.Agents.Common.Object
{
    public class ParameterInfo
    {
        public string Name { get; }
        public object Value { get; }
        private readonly SqlDbType _type;

        public ParameterInfo(string name, object value, SqlDbType type = SqlDbType.BigInt)
        {
            this.Name = name;
            this.Value = value ?? DBNull.Value;
            this._type = type;
        }

        public SqlParameter AddToCommand(SqlCommand command)
        {
            var sqlParameter = command.Parameters.AddWithValue(this.Name, this.Value);
            if (this._type != SqlDbType.BigInt)
                sqlParameter.SqlDbType = this._type;
            return sqlParameter;
        }
    }
}