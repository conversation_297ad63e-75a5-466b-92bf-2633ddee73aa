using System.Collections.Generic;
using Agoda.EBE.Framework.Objects.MultiProductBooking;
using Newtonsoft.Json;

namespace Agoda.EBE.Agents.Common.Object
{
    public class BookingPartnerInfo
    {
        [JsonProperty("bookingId")]
        public int BookingId { get; set; }
        [JsonProperty("partnerLevel")]
        public int? PartnerLevel { get; set; }
        [JsonProperty("partnerId")]
        public int? PartnerId { get; set; }
        [JsonProperty("partnerName")]
        public string PartnerName { get; set; }
        
        public static BookingPartnerInfo FromEbePartnerInfo(EbePropertyBookingBookingHoldingPartnerName ebeBookingPartnerInfo){
            return new BookingPartnerInfo
            {
                BookingId = ebeBookingPartnerInfo.BookingId,
                PartnerLevel = ebeBookingPartnerInfo.Level,
                PartnerId = ebeBookingPartnerInfo.PartnerId,
                PartnerName = ebeBookingPartnerInfo.Name
            };
        }
        
        public static string ToJsonString(IList<BookingPartnerInfo> partnerInfos){
            return JsonConvert.SerializeObject(partnerInfos);
        }
    }
    
  
}