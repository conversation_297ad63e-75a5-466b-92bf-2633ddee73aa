using System.Collections.Generic;
using Agoda.EBE.Framework.MQ.MessageType;
using Newtonsoft.Json;

namespace Agoda.EBE.Agents.Common.Object
{
	/// <summary>
	/// Wrapper on rabbit message which provides functionality to bookmark a long process and save required parameters so that message could be easily retried from where it left. 
	/// </summary>
	public class RabbitMessageRequest
	{
		#region Private Fields

		private readonly IDefaultMessage _message;
		private readonly IDictionary<string, string> _additionalDataForReprocess;

		#endregion

		#region Constructor

		public RabbitMessageRequest(IDefaultMessage message)
		{
			if (message.AdditionalData==null)
			{
				message.AdditionalData= new Dictionary<string, string>();
			}

			_message = message;
			_additionalDataForReprocess = message.AdditionalData;
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// Returns Current Message Retry Number
		/// </summary>
		public int MessageRetryCount => _message.NumRetry;

		#endregion

		#region Private Methods
		
		private void SetReprocessDataInternal(string step, string value)
		{
			_additionalDataForReprocess[step] = value;
		}

		private string GetReprocessDataInternal(string key)
		{
			string value;

			_additionalDataForReprocess.TryGetValue(key, out value);

			return value;
		}

		private TValue GetReprocessDataInternal<TValue>(string key, TValue defaultValue = default(TValue))
		{
			if (_additionalDataForReprocess.ContainsKey(key))
			{
				var value = _additionalDataForReprocess[key];

				return JsonConvert.DeserializeObject<TValue>(value);
			}

			return defaultValue;
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Updates the progress of a particular step with true of false.
		/// </summary>
		/// <param name="step">step name</param>
		/// <param name="value">boolean value for step completion</param>
		public void UpdateProgress(string step, bool value)
		{
			SetReprocessDataInternal(step, value.ToString().ToLower());
		}

		/// <summary>
		/// Check if the step was performed before.
		/// </summary>
		/// <param name="step">step name</param>
		/// <returns></returns>
		public bool IsStepPerformed(string step)
		{
			return GetReprocessDataInternal<bool>(step, false);
		}

		/// <summary>
		/// Save parameters so that a particular step could be retried in case of failures.
		/// </summary>
		/// <param name="key">Key</param>
		/// <param name="value">Value</param>
		public void SetReprocessData(string key, string value)
		{
			_additionalDataForReprocess[key] = value;
		}

		/// <summary>
		/// Returns the parameters which was saved before. Return default value in case there was no such parameter saved before. 
		/// </summary>
		/// <typeparam name="TValue">Type of the parameter</typeparam>
		/// <param name="key">Key</param>
		/// <param name="defaultValue">default value in case the key was not found</param>
		/// <returns></returns>
		public TValue GetReprocessData<TValue>(string key, TValue defaultValue = default(TValue))
		{
			return GetReprocessDataInternal<TValue>(key, defaultValue);
		}

		#endregion
	}
}