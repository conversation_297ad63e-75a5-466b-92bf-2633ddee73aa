using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.DataAccess.Interface;
using Agoda.EBE.Agents.Common.EbeCommonException;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Model;
using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Agents.Common.Util;
using Agoda.EBE.Framework.Objects;
using Newtonsoft.Json;
using Agoda.EBE.Agents.Common.Util.Data;
using Agoda.EBE.Framework.Messaging;
using Dapper;
using ItineraryCreditCardEncrypted = Agoda.EBE.Agents.Common.Object.ItineraryCreditCardEncrypted;

namespace Agoda.EBE.Agents.Common.DataAccess
{
    public enum Measurement
    {
        GetBookingAction = 100001,
        UpdateBookingActionState = 100002,
        GetItineraryCreditCardEncrypted = 100003,
        GetEncryptionKey = 100004,
        GetBookingActionByItineraryId = 100005,
        InsertBookingActionMessage = 100006,
        GetBookingActionMessage = 100007,
        InsertBookingProcess = 100008,
        UpdateBookingProcess = 100009,
        DeleteBookingProcess = 100010,
        InsertBookingSnapshot = 100011
    }

    public class BFDBDataAccess : IBFDBDataAccess, IEncryptedDataAccess
    {
        const int BFDBCommandTimeout = 30;
        
        private static readonly JsonSerializerSettings SerializeSettings = new JsonSerializerSettings
        {
            // Serialization settings to make DateTime compatible with Scala workflow-agent
            NullValueHandling = NullValueHandling.Ignore,            // Ignore Null value
            DateTimeZoneHandling = DateTimeZoneHandling.Unspecified, // No time zone specified
            DateFormatString = "yyyy'-'MM'-'dd'T'HH':'mm':'ss.fff"   // Only 3 digits of milliseconds to comply with ISO-8601 and millisecond precision
        };
        
        private readonly IMessaging _messaging;
        private readonly String _bfdbConnection;
        private readonly IConnectionProvider _connectionProvider;
        public BFDBDataAccess(String bfdbConnection, IMessaging messaging, IConnectionProvider connectionProvider)
        {
            _messaging = messaging;
            _bfdbConnection = bfdbConnection;
            _connectionProvider = connectionProvider;
        }

        public BookingAction GetBookingAction(int bookingID)
        {
            DefaultTypeMap.MatchNamesWithUnderscores = true;
            var result = _connectionProvider.WithConnection(_bfdbConnection, c => c.QueryFirst<BookingAction>("dbo.netcore_agent_get_booking_action_by_booking_id_v1",
                new
                {
                    bookingId = bookingID
                },
                commandTimeout: BFDBCommandTimeout,
                commandType: CommandType.StoredProcedure), Measurement.GetBookingAction);

            return result;
        }

        public List<BookingAction> GetBookingActionByItineraryID(long itineraryId)
        {
            DefaultTypeMap.MatchNamesWithUnderscores = true;
            var result = _connectionProvider.WithConnection(
                _bfdbConnection,
                c => c.Query<BookingAction>(
                    "dbo.get_booking_action_by_itinerary_id_v1",
                    new
                    {
                        itineraryId
                    },
                    commandTimeout: BFDBCommandTimeout,
                    commandType: CommandType.StoredProcedure),
                Measurement.GetBookingActionByItineraryId);

            return result.ToList();
        }

        public bool UpdateBookingActionState(long actionId, BookingActionState state) 
        { 
            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("@actionId", actionId, DbType.Int64); 
            parameters.Add("@actionState", JsonConvert.SerializeObject(state, SerializeSettings), DbType.String); 
            parameters.Add("@recordCount", null,DbType.Int32, ParameterDirection.Output);
               
            var count = _connectionProvider.WithConnection(_bfdbConnection, conn => 
                conn.Execute("dbo.update_booking_action_v1", parameters, commandType: CommandType.StoredProcedure), Measurement.UpdateBookingActionState);

            return true;
        }

        public bool InsertBookingActionMessage(long actionId, int topic, AgentBookingActionMessage actionMessage, Guid userId)
        {
            
            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("@action_id", actionId, DbType.Int64);
            parameters.Add("@topic", topic, DbType.Int32);
            var content = JsonConvert.SerializeObject(actionMessage, SerializeSettings);
            parameters.Add("@content", content, DbType.String);
            parameters.Add("@rec_created_by", userId, DbType.Guid);
            var count = _connectionProvider.WithConnection(_bfdbConnection, conn => 
                conn.Execute("dbo.netcore_agent_booking_action_message_insert_v1", 
                    parameters,
                    commandTimeout: BFDBCommandTimeout,
                    commandType: CommandType.StoredProcedure), Measurement.InsertBookingActionMessage);

            return true;
        }
        
        public List<BookingActionMessage> GetBookingActionMessages(long actionId)
        {
            DefaultTypeMap.MatchNamesWithUnderscores = true;
            var result = _connectionProvider.WithConnection(_bfdbConnection, c => 
                c.Query<BookingActionMessage>("dbo.netcore_agent_booking_action_message_get_v1",
                new
                {
                    actionId
                },
                commandTimeout: BFDBCommandTimeout,
                commandType: CommandType.StoredProcedure), Measurement.GetBookingActionMessage);

            return result.ToList();
        }

        public ItineraryCreditCardEncrypted GetEncryptedCreditCard(long itineraryId)
        {
            var getEncryptedCreditCardSPName = "dbo.netcore_agent_itinerary_credit_card_encrypted_select_v1";
            try
            {
                return _connectionProvider.WithConnection(_bfdbConnection, c => c.QueryFirst<ItineraryCreditCardEncrypted>(
                    sql: getEncryptedCreditCardSPName,
                    param: new
                    {
                        itineraryId = itineraryId
                    },
                    commandTimeout: BFDBCommandTimeout,
                    commandType: CommandType.StoredProcedure), Measurement.GetItineraryCreditCardEncrypted);
            }
            catch (InvalidOperationException ex)
            {
                throw new NonRetryableException(getEncryptedCreditCardSPName, ex);
            }
        }

        public EncryptionKey GetEncryptionKeyFromId(long encryptionKeyId)
        {
            return _connectionProvider.WithConnection(_bfdbConnection, c => c.QueryFirst<EncryptionKey>(
                sql: "dbo.ebe_get_encryption_key_by_id_v1",
                param: new
                {
                    key_id = encryptionKeyId
                },
                commandTimeout: BFDBCommandTimeout,
                commandType: CommandType.StoredProcedure), Measurement.GetEncryptionKey);
        }
        
        public Tuple<int, String> InsertBookingProcess(long itineraryId, long operationId, String processKey, String processResult, DateTime expiryDatetime, Guid userId)
        {
            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("@itineraryId", itineraryId, DbType.Int64);
            parameters.Add("@operationId", operationId, DbType.Int64);
            parameters.Add("@processKey", processKey, DbType.String);
            parameters.Add("@processResult", processResult, DbType.String);
            parameters.Add("@expiryDatetime", expiryDatetime, DbType.DateTime);
            parameters.Add("@recCreatedBy", userId, DbType.Guid);
            parameters.Add("@result", null, DbType.Int32, ParameterDirection.Output);
            
            var queryProcessResult = _connectionProvider.WithConnection(_bfdbConnection, conn => 
                conn.QueryFirst<String>("dbo.workflow_agent_insert_booking_process_record_v3", 
                    parameters,
                    commandTimeout: BFDBCommandTimeout,
                    commandType: CommandType.StoredProcedure), Measurement.InsertBookingProcess);
            
            var spResult = parameters.Get<int>("@result");

            return Tuple.Create(spResult, queryProcessResult);
        }
        
        public bool UpdateBookingProcess(long itineraryId, long operationId, String processKey, String processResult, Guid userId, int bookingId)
        {
            
            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("@processKey", processKey, DbType.String);
            parameters.Add("@processResult", processResult, DbType.String);
            parameters.Add("@recModifiedBy", userId, DbType.Guid);
            parameters.Add("@recordCount", null, DbType.Int32, ParameterDirection.Output);

            var count = _connectionProvider.WithConnection(_bfdbConnection, conn => 
                conn.Execute("dbo.workflow_agent_update_booking_process_record_v1", 
                    parameters,
                    commandTimeout: BFDBCommandTimeout,
                    commandType: CommandType.StoredProcedure), Measurement.UpdateBookingProcess);
            
            var recordCount = parameters.Get<int>("@recordCount");
            if (recordCount == 0)
            {
                _messaging.ExceptionMessage.Send(
                    $"BFDBDataAccess:UpdateBookingProcess was called but no updated record, processKey: {processKey}",
                    null,
                    LogLevel.WARN,
                    new TagBuilder().AddBookingTag(bookingId).Build());
            }
            
            return true;
        }
        
        public void DeleteBookingProcess(String processKey)
        {

            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("@processKey", processKey, DbType.String);
            
            _connectionProvider.WithConnection(_bfdbConnection, conn => 
                conn.Execute("dbo.workflow_agent_delete_booking_process_record_v1", 
                    parameters,
                    commandTimeout: BFDBCommandTimeout,
                    commandType: CommandType.StoredProcedure), Measurement.DeleteBookingProcess);

        }

        public void InsertBookingSnapshot<T>(long itineraryId, long bookingId, Enum.SnapshotType snapshotType, T payload)
        {
            var content = JsonConvert.SerializeObject(payload, SerializeSettings);
            InsertBookingSnapshotString(itineraryId, bookingId, snapshotType, content);
        }
        
        private void InsertBookingSnapshotString(long itineraryId, long? bookingId, Enum.SnapshotType snapshotType, string content)
        {
            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("@itineraryId", itineraryId, DbType.Int64);
            parameters.Add("@bookingId", (object)bookingId ?? DBNull.Value, DbType.Int64);
            parameters.Add("@snapshotTypeId", (int)snapshotType, DbType.Int32);
            parameters.Add("@payload", content, DbType.String);
            
            _connectionProvider.WithConnection(_bfdbConnection, conn => 
                conn.Execute("dbo.netcore_agent_booking_snapshot_insert_v1", 
                    parameters,
                    commandTimeout: BFDBCommandTimeout,
                    commandType: CommandType.StoredProcedure), Measurement.InsertBookingSnapshot);
        }

        public void InsertBookingActionsSnapshot(long itineraryId, long bookingId, Enum.SnapshotType snapshotType)
        {
            var allBookingActions = GetBookingActionByItineraryID(itineraryId);
            var masterAction = allBookingActions.Find(b => b.BookingId == 0);
            if (masterAction == null)
            {
                throw new InvalidBookingException("Master booking action not found during snapshot insert");
            }
            InsertBookingSnapshotString(itineraryId, bookingId: null, snapshotType, masterAction.State);
            var productAction = allBookingActions.Find(b => b.BookingId == bookingId);
            if (productAction == null)
            {
                throw new InvalidBookingException("Product action not found during snapshot insert");
            }
            InsertBookingSnapshotString(itineraryId, bookingId, snapshotType, productAction.State);
        }
    }
}