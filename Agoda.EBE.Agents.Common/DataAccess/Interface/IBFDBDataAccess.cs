using System;
using System.Collections.Generic;
using Agoda.EBE.Agents.Common.Model;
using Agoda.EBE.Framework.Objects;

namespace Agoda.EBE.Agents.Common.DataAccess.Interface
{
    public interface IBFDBDataAccess
    {
        BookingAction? GetBookingAction(Int32 bookingID);
        List<BookingAction> GetBookingActionByItineraryID(long ItineraryID);
        bool UpdateBookingActionState(long actionId, BookingActionState state);

        bool InsertBookingActionMessage(long actionId, int topic, AgentBookingActionMessage content, Guid userId);

        List<BookingActionMessage> GetBookingActionMessages(long actionId);

        Tuple<int, String> InsertBookingProcess(long itineraryId, long operationId, String processKey,
            String processResult, DateTime expiryDatetime, Guid userId);

        bool UpdateBookingProcess(long itineraryId, long operationId, String processKey,
            String processResult, Guid userId, int bookingId);

        void DeleteBookingProcess(String processKey);

        void InsertBookingSnapshot<T>(long itineraryId, long bookingId, Enum.SnapshotType snapshotType, T data);

        void InsertBookingActionsSnapshot(long itineraryId, long bookingId, Enum.SnapshotType snapshotType);
    }
}