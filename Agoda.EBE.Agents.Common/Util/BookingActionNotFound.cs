using System;

namespace Agoda.EBE.Agents.Common.Util
{
    /// <summary>
    /// Can throw this exception when there is no booking action found for a booking id in BFDB.
    /// </summary>
    public class BookingActionNotFound : Exception
    {
        public BookingActionNotFound(string message)
            : base(message)
        {
        }

        public BookingActionNotFound(string message, Exception inner)
            : base(message, inner)
        {
        }
    }
}