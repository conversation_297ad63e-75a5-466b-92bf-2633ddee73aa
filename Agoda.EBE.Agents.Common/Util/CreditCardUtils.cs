namespace Agoda.EBE.Agents.Common.Util
{
    public static class CreditCardUtils
    {
        public static (int Month, int Year) SplitToCCExpiryMonthAndYear(string ccExpiryDate)
        {
            var month = 0;
            var year = 0;

            if (ccExpiryDate != null)
            {
                var splitted = ccExpiryDate.Split('/');
                if (splitted.Length > 0)
                {
                    int.TryParse(splitted[0], out month);
                }

                if (splitted.Length > 1)
                {
                    var canParse = int.TryParse(splitted[1], out year);
                    if (canParse && year < 2000)
                    {
                        // Expiry date in 2 digit year format e.g. 11/25 (Nov/2025), so need to correct by +2000
                        year += 2000;
                    }
                }
            }
            
            return (month, year);
        }
    }
}