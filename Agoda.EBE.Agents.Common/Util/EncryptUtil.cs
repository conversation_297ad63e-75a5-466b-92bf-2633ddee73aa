using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class EncryptUtil
    {
        public static string Encode(string str)
        {
            var bytes = Encoding.Unicode.GetBytes(str);
            return BitConverter.ToString(bytes).Replace("-", "");
        }

        public static string Decode(string hexString)
        {
            byte[] bytes = Enumerable.Range(0, hexString.Length)
                .Where(x => x % 2 == 0)
                .Select(x => Convert.ToByte(hexString.Substring(x, 2), 16))
                .ToArray();
            return Encoding.Unicode.GetString(bytes);
        }
        
        public static string CalculateHashCode(string str, string hashKey)
        {
            var strWithHash = str + hashKey;

            var hashPayload = Encoding.UTF8.GetBytes(strWithHash);
            var sha1Managed = new SHA1Managed();
            var hashedBytes =  sha1Managed.ComputeHash(hashPayload);
            
            StringBuilder spsHashBuilder = new StringBuilder();
            foreach (var hashedByte in hashedBytes)
            {
                spsHashBuilder.Append(hashedByte.ToString("x2"));
            }

            return spsHashBuilder.ToString();
        }
    }
}