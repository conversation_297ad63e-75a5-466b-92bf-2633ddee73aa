using System;
using System.Collections.Generic;
using System.Linq;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.DataAccess.Interface;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Messaging.Log;
using Agoda.EBE.Agents.Common.Model;
using Agoda.EBE.Framework;
using Agoda.EBE.Framework.Messaging;
using Agoda.EBE.Framework.Objects;

namespace Agoda.EBE.Agents.Common.Util
{
    public interface IBookingActionUtils
    {
        BookingActionWithState? GetBookingAction(int bookingId, string agent);
        List<BookingActionWithState>? GetBookingActions(long itineraryId, string agent);
    }

    public class BookingActionUtils : IBookingActionUtils
    {
        private readonly IMessaging _messaging;
        private readonly IBFDBDataAccess _bfdbDataAccess;

        public BookingActionUtils(IMessaging messaging, IBFDBDataAccess bfdbDataAccess)
        {
            _messaging = messaging;
            _bfdbDataAccess = bfdbDataAccess;
        }

        public BookingActionWithState? GetBookingAction(int bookingId, string agent)
        {
            return GetBookingActionImp(bookingId, agent, 0);
        }

        public List<BookingActionWithState>? GetBookingActions(long itineraryId, string agent)
        {
            var bookingActions = _bfdbDataAccess.GetBookingActionByItineraryID(itineraryId);
            if (bookingActions == null || bookingActions.Count == 0)
            {
                return null;
            }

            var masterBookingAction = bookingActions.Find(b => b.BookingId == 0);
            if (masterBookingAction == null)
            {
                return null;
            }

            return bookingActions.Select(MapWithState).ToList();
        }

        private BookingActionWithState? GetBookingActionImp(int bookingId, string agent, int retry)
        {
            try
            {
                var bookingAction = _bfdbDataAccess.GetBookingAction(bookingId);

                if (bookingAction == null)
                {
                    return null;
                }

                return MapWithState(bookingAction);
            }
            catch (Exception ex)
            {
                if (retry < MaxRetries) // handle reading uncommitted data from BFDB [MPBE-2872]
                {
                    _messaging.ExceptionMessage.Send(
                        $"{nameof(BookingActionUtils)}.{nameof(GetBookingAction)} Fail. BookingID: {bookingId} retry {retry}",
                        ex,
                        LogLevel.WARN,
                        new TagBuilder().AddBookingTag(bookingId).Build());

                    return GetBookingActionImp(bookingId, agent, ++retry);
                }
                else
                {
                    throw;
                }
            }
        }

        public readonly int MaxRetries = 3;

        private BookingActionWithState MapWithState(BookingAction bookingAction)
        {
            var bookingActionState = bookingAction.GetBookingActionState();

            return new BookingActionWithState
            {
                ActionId = bookingAction.ActionId,
                ItineraryId = bookingAction.ItineraryId,
                BookingType = bookingAction.BookingType,
                BookingId = bookingAction.BookingId,
                MemberId = bookingAction.MemberId,
                ActionTypeId = bookingAction.ActionTypeId,
                CorrelationId = bookingAction.CorrelationId,
                RequestId = bookingAction.RequestId,
                WorkflowId = bookingAction.WorkflowId,
                WorkflowStateId = bookingAction.WorkflowStateId,
                StateSchemaVersion = bookingAction.StateSchemaVersion,
                State = bookingActionState,
                StoreFrontId = bookingAction.StoreFrontId,
                LanguageId = bookingAction.LanguageId,
                RecStatus = bookingAction.RecStatus,
                RecCreatedWhen = bookingAction.RecCreatedWhen,
                RecModifyWhen = bookingAction.RecModifyWhen,
                ProductTypeId = bookingAction.ProductTypeId
            };
        }
    }
}