using System.Collections.Generic;
using System.Data;
using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Agents.Common.Util.Data.Interface;

namespace Agoda.EBE.Agents.Common.Util.Data
{
    public class DatabaseProvider: IDatabaseProvider
    {
        private readonly string _connectionString;
        
        public DatabaseProvider(string connectionString)
        {
            _connectionString = connectionString;
        }
        
        public void ExecuteNonQuery(string storeProcedureName, List<ParameterInfo> parameterInfos)
        {
            DbUtils.ExecuteNonQuery(_connectionString, storeProcedureName, parameterInfos);
        }

        public DataSet ExecuteDataSet(string storeProcedureName, List<ParameterInfo> parameterInfos)
        {
            return DbUtils.ExecuteDataSet(_connectionString, storeProcedureName, parameterInfos);
        }
    }
}