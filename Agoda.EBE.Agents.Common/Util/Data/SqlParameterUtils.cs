﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class DataUtils
    {
        public static void AddParam(this SqlCommand cmd, string name, object val, SqlDbType type)
        {
            var param = new SqlParameter(name, type);
            param.Direction = ParameterDirection.Input;
            param.Value = val;
            cmd.Parameters.Add(param);
        }

        public static bool HasRow(this DataTable dt)
        {
            if (dt != null && dt.Rows.Count > 0)
            {
                return true;
            }
            return false;
        }

        public static T ParseDataRow<T>(this DataRow dr, string key, object defaultValue = null)
        {
            object value = null;
            dr.TryParseDataRowObject(key, out value);
            return TryParseValue<T>(value, defaultValue);
        }

        private static bool TryParseDataRowObject(this DataRow row, string key, out object value)
        {
            try
            {
                if (row == null)
                {
                    value = null;
                    return false;
                }
                else if (row.Table.Columns.Contains(key) && !string.IsNullOrEmpty(key) && row != null && row[key] != DBNull.Value)
                {
                    value = row[key];
                    return true;
                }
            }
            catch (Exception)
            {
                throw;
            }

            value = null;
            return false;
        }

        private static T TryParseValue<T>(object value, object defaultValue = null)
        {
            if (value != null && value != DBNull.Value)
            {
                if (typeof(T) == value.GetType())
                {
                    return (T)value;
                }
                else
                {
                    try
                    {
                        Type type = typeof(T);
                        if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
                        {
                            return (T)Convert.ChangeType(value, Nullable.GetUnderlyingType(type));
                        }
                        return (T)Convert.ChangeType(value, type);
                    }
                    catch (Exception ex)
                    {
                        string errorText = string.Format("Cannot convert object '{0}' to type '{1}', {2}", value, typeof(T), ex.Message);
                        throw new ApplicationException(errorText, ex);
                    }
                }
            }

            if (defaultValue != null && defaultValue.GetType() == typeof(T))
            {
                return (T)defaultValue;
            }

            return default(T);
        }
    }
}
