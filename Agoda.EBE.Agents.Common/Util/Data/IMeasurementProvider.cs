using System;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Messaging;

namespace Agoda.EBE.Agents.Common.Util.Data
{
    public interface IMeasurementProvider
    {
        TResult CallWithMeasurement<TResult>(Func<TResult> callbackFunc, System.Enum measurement);
    }

    public class MeasurementProvider : IMeasurementProvider
    {
        private readonly IMessaging _messaging;

        public MeasurementProvider(IMessaging messaging)
        {
            _messaging = messaging;
        }

        public TResult CallWithMeasurement<TResult>(Func<TResult> callbackFunc, System.Enum measurement)
        {
            string methodName = callbackFunc.Method.Name;
            var stat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            stat.BeginTrack();

            try
            {
                // execute action
                TResult result = callbackFunc();
                stat.EndTrack(measurement);
                return result;
            }
            catch (Exception ex)
            {
                // log the exception
                string message = $"Failed to Execute {methodName}";
                _messaging.ExceptionMessage.Send(message, ex, LogLevel.FATAL);

                // add the stats
                stat.EndTrack(measurement, false);

                throw;
            }
        }
    }
}