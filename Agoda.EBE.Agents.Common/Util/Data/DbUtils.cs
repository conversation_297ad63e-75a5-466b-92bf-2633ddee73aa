﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Agoda.EBE.Agents.Common.Object;

namespace Agoda.EBE.Agents.Common.Util.Data
{
    public static class DbUtils
    {
        private static SqlCommand CreateCommand(string storedProcedure, SqlConnection connection,
            IEnumerable<ParameterInfo> parameters, int? timeoutSeconds = null)
        {
            var command = new SqlCommand(storedProcedure, connection) {CommandType = CommandType.StoredProcedure};
            foreach (var parameter in parameters)
                parameter.AddToCommand(command);
            if (timeoutSeconds.HasValue)
                command.CommandTimeout = timeoutSeconds.Value;
            return command;
        }

        public static Task<DataSet> ExecuteDataSetAsync(string connectionString, string storedProcedure, params ParameterInfo[] parameters) =>
            Task.Run(() => ExecuteDataSet(connectionString, storedProcedure, parameters));

        public static DataSet ExecuteDataSet(string connectionString, string storedProcedure, List<ParameterInfo> parameterInfos, int? timeoutSeconds = null) =>
            ExecuteDataSetWithTimeout(connectionString, storedProcedure, timeoutSeconds, parameterInfos.ToArray());

        public static object ExecuteScalar(string connectionString, string storedProcedure, List<ParameterInfo> parameterInfos) =>
            ExecuteScalar(connectionString, storedProcedure, parameterInfos.ToArray());

        public static int ExecuteNonQuery(string connectionString, string storedProcedure, List<ParameterInfo> parameterInfos) =>
            ExecuteNonQuery(connectionString, storedProcedure, parameterInfos.ToArray());


        public static DataSet ExecuteDataSet(string connectionString, string storedProcedure,
            params ParameterInfo[] parameters)
        {
            return ExecuteDataSetWithTimeout(connectionString, storedProcedure, null, parameters);
        }
        
        public static DataSet ExecuteDataSetWithTimeout(string connectionString, string storedProcedure,
            int? timeoutSeconds, params ParameterInfo[] parameters)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                using (var command = CreateCommand(storedProcedure, connection, parameters, timeoutSeconds))
                {
                    using (var sqlDataAdapter = new SqlDataAdapter(command))
                    {
                        var dataSet = new DataSet();
                        connection.Open();
                        sqlDataAdapter.Fill(dataSet);
                        return dataSet;
                    }
                }
            }
        }

        public static object ExecuteScalar(string connectionString, string storedProcedure,
            params ParameterInfo[] parameters)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                using (var command = CreateCommand(storedProcedure, connection, parameters))
                {
                    if (connection.State != ConnectionState.Open)
                        connection.Open();
                    var obj = command.ExecuteScalar();
                    if (connection.State != ConnectionState.Closed)
                        connection.Close();
                    return obj;
                }
            }
        }

        public static int ExecuteNonQuery(string connectionString, string storedProcedure,
            params ParameterInfo[] parameters)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                using (var command = CreateCommand(storedProcedure, connection, parameters))
                {
                    connection.Open();
                    var result = command.ExecuteNonQuery();
                    connection.Close();
                    return result;
                }
            }
        }

        public static TResult WithConnection<TResult>(string connectionString, Func<IDbConnection, TResult> getData)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                return getData(connection);
            }
        }

        public static async Task<TResult> WithConnection<TResult>(string connectionString, 
            Func<IDbConnection, Task<TResult>> getData)
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    return await getData(connection);
                }
            }
            catch (TimeoutException ex)
            {
                throw new Exception($"DbUtils.WithConnection SQL Timeout. Exception: {ex}");
            }
            catch (SqlException ex)
            {
                throw new Exception($"DbUtils.WithConnection SQL Connection Problem. Exception: {ex}");
            }
        }
    }
}