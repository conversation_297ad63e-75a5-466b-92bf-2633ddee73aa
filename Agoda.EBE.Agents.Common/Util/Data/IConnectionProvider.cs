using System;
using System.Data;
using System.Data.SqlClient;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Messaging;

namespace Agoda.EBE.Agents.Common.Util.Data
{
    public interface IConnectionProvider
    {
        TResult WithConnection<TResult>(string connectionString, Func<IDbConnection, TResult> getData, System.Enum measurement);
    }
    
    public class SqlConnectionProvider : IConnectionProvider
    {
        private readonly IMessaging _messaging;

        public SqlConnectionProvider(IMessaging messaging)
        {
            _messaging = messaging;
        }

        public TResult WithConnection<TResult>(string connectionString, Func<IDbConnection, TResult> getData, System.Enum measurement)
        {
            string methodName = getData.Method.Name;
            var stat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            stat.BeginTrack();
            
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                try
                {
                    // open database connection
                    connection.Open();

                    // execute the db action
                    TResult result = getData(connection);

                    stat.EndTrack(measurement);

                    return result;
                }
                catch (Exception ex)
                {
                    // log the exception
                    string message = $"Failed to Execute {methodName}";
                    _messaging.ExceptionMessage.Send(message, ex, LogLevel.FATAL);

                    // add the stats
                    stat.Tags["is_success"] = "fail";
                    stat.EndTrack(measurement, false);

                    throw;
                }
            }
        }
    }
}