using System;
using Agoda.EBE.Agents.Common.Util.Samples;
using Agoda.EBE.Framework.Objects;


namespace Agoda.EBE.Agents.Common.Util
{
    public class CartMultiProductTypeUtil
    {
        public Boolean IsCartPartialSuccessAllowed(BookingActionState bookingActionState)
        {
            return bookingActionState.Request.IsPartialSuccessAllowed == true;
        }

        public Boolean IsCleanUpFlow(BookingActionState bookingActionState)
        {
            return bookingActionState.Request.IsCleanUpFlow == true;
        }

        public Boolean IsCartBookingHotelConfirmed(BookingAction productAction)
        {
            if (productAction.BookingId != 0 )
            {
                var productTypeId = (Enum.MultiProductType)(productAction.ProductTypeId);
                switch (productTypeId)
                {
                    case Enum.MultiProductType.SingleProperty:
                        var propertyBookingActionState = productAction.GetBookingActionState();
                        var propertyBookingActionUtils = new PropertyBookingActionUtils();
                        var ans =   propertyBookingActionUtils.IsPropertyConfirmed(propertyBookingActionState);
                        return ans;
                    default:
                        return false;
                }
            }
            return false;
        }
    }
}