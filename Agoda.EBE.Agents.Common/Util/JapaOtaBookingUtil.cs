using Agoda.EBE.Framework;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class JapaOtaBookingUtil
    {
        /// <summary>
        /// Check booking is Agoda OTAJAPA Fixed Exchange Rate for cancellation
        /// </summary>
        /// <param name="whiteLabelId"></param>
        /// <param name="paymentModel"></param>
        /// <param name="dmcId"></param>
        /// <param name="inventoryTypeId"></param>
        /// <param name="paymentCurrency"></param>
        /// <param name="supplierCurrency"></param>
        /// <param name="exchangeRateOption"></param>
        /// <returns></returns>
        public static bool IsAgodaOtaJaPaFixedExchangeRate(int whiteLabelId, 
            int paymentModel, 
            int dmcId, 
            int inventoryTypeId, 
            string paymentCurrency, 
            string supplierCurrency,
            int exchangeRateOption)
        {
            return  whiteLabelId == (int) Enum.WhitelabelId.Agoda &&
                    paymentModel == (int) ConstantEnum.PaymentModel.PayHotel &&
                    dmcId == (int) Enum.DmcId.JTB &&
                    inventoryTypeId == (int) Enum.InventoryType.OtaJapanican && 
                    paymentCurrency == supplierCurrency && 
                    exchangeRateOption == (int) Enum.ExchangeRateOption.FixedFX;
        }
    }
}