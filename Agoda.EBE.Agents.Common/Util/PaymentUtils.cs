﻿using System;
using System.Linq;
using static Agoda.EBE.Agents.Common.Enum.ItineraryProductType;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class PaymentUtils
    {
        public static long GetTransactionId(string gatewayTransactionId, int index = 0)
        {
            return GetTransactionId<long>(gatewayTransactionId, index);
        }

        public static T GetTransactionId<T>(string gatewayTransactionId, int index = 0)
        {
            var strTransactionId = new string[] { };
            var currentTransaction = string.Empty;

            if (string.IsNullOrEmpty(gatewayTransactionId))
            {
                return default(T);
            }

            if (gatewayTransactionId.Contains("/")) //NTT
            {
                strTransactionId = gatewayTransactionId.Split('/'); // Example : 100002596/100313011106
                currentTransaction = strTransactionId[index].Trim();
            }
            else if (gatewayTransactionId.Contains("-"))
            {
                strTransactionId = gatewayTransactionId.Split('-'); // Example : 100002596-2
                currentTransaction = strTransactionId.ElementAtOrDefault(index)?.Trim();
            }
            else
            {
                currentTransaction = index == 0 ? gatewayTransactionId : "0";
            }

            return GetValue<T>(currentTransaction);
        }

        public static T GetValue<T>(string value)
        {
            return (T)Convert.ChangeType(value, typeof(T));
        }

        public static int GetPaymentClientId(int? whitelabelId,
            Enum.MultiProductType multiProductType, bool usePropertyAgodaPaymentConfigV2)
        {
            var itineraryProductType = Enum.mapItineraryProductTypeFromMultiProductType(multiProductType);

            if (Enum.IsSPWLGroup(Enum.GetWhitelabelId(whitelabelId)) && itineraryProductType.Equals(Hotel))
                return (int)Enum.PaymentClientIdType.SPWhitelabel;
            else if(usePropertyAgodaPaymentConfigV2)
                return (int)Enum.PaymentClientIdType.PropertyAgodaV2;
            else
                return (int)Enum.PaymentClientIdType.PropertyAgoda;
        }
    }
}
