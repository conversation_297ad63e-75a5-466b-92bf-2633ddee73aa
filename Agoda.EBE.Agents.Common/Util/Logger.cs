using System;
using Agoda.Adp.Messaging.Client;

namespace Agoda.EBE.Agents.Common.Util;

public interface ILogger
{
    public void Log(string message, LogLevel level, Exception e = null);
}
public class Logger: ILogger
{
    private string _appName;
    private string _apiKey;

    public Logger(string appName, string apiKey)
    {
        _appName = appName;
        _apiKey = apiKey;
    }
    public void Log(string message, LogLevel level, Exception e = null)
    {
        var logging = new LogMessage(_appName, level, message, e);
        logging.SendAsync(_apiKey);
    }
}