﻿using System;

namespace Agoda.EBE.Agents.Common.Util
{

    /// <summary>
    /// Can throw this exception when there is no credit card found.
    /// </summary>
    public class BookOnRequestNotFoundException : Exception
    {
        public BookOnRequestNotFoundException(string message)
            : base(message)
        {
        }

        public BookOnRequestNotFoundException(string message, Exception inner)
            : base(message, inner)
        {
        }
    }
}