using System;
using System.Linq;
using System.Text;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class DateTimeUtil
    {
        public static string GetCurrentTimeWithTimeZone(TimeZoneInfo timeZoneInfo)
        {
            return TimeZoneInfo.ConvertTime(DateTime.Now, timeZoneInfo).ToString("yyyyMMddHHmmss"); 
        }

        public static TimeZoneInfo GetTokyoTimeZoneInfo()
        {
            // Fallback for different OS isuue
            return TimeZoneInfo.GetSystemTimeZones().Any(x => x.Id == "Tokyo Standard Time") ? 
                TimeZoneInfo.FindSystemTimeZoneById("Tokyo Standard Time") : 
                TimeZoneInfo.FindSystemTimeZoneById("Asia/Tokyo");
        }
    }
}