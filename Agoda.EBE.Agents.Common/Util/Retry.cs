﻿using System;
using System.Collections.Generic;
using System.Threading;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class Retry
    {
        public static void Do(
            Action action,
            TimeSpan retryInterval,
            int maxAttemptCount = 3)
        {
            Do<object>(() =>
            {
                action();
                return null;
            }, retryInterval, maxAttemptCount);
        }

        public static T Do<T>(
            Func<T> action,
            TimeSpan retryInterval,
            int maxAttemptCount = 3)
        {
            var exceptions = new List<Exception>();

            for (var attempted = 0; attempted < maxAttemptCount; attempted++)
            {
                try
                {
                    if (attempted > 0)
                    {
                        Thread.Sleep(retryInterval);
                    }
                    return action();
                }
                catch (Exception ex)
                {
                    exceptions.Add(ex);
                }
            }
            throw new AggregateException(exceptions);
        }
        
        public static T DoConditional<T>(
            Func<T> action,
            TimeSpan retryInterval,
            Func<T, bool> isRetryNeeded,
            int maxAttemptCount = 3)
        {
            var exceptions = new List<Exception>();
            
            for (var attempted = 0; attempted < maxAttemptCount; attempted++)
            {
                try
                {
                    if (attempted > 0)
                    {
                        Thread.Sleep(retryInterval);
                    }

                    var result = action();
                    if (isRetryNeeded(result))
                    {
                        exceptions.Add(new Exception($"Retry No. {attempted}. Failed because of predicate {isRetryNeeded.Method.Name}"));
                        continue;
                    }

                    return result;
                }
                catch (Exception ex)
                {
                    exceptions.Add(ex);
                }
            }
            
            throw new AggregateException(exceptions);
        }
    }
}