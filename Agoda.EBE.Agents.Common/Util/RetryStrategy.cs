using System;
using System.Linq;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class RetryStrategy
    {
        private static int[] primes = {
            19, 23, 29, 31, 37, 41, 43, 47, 53, 59
        };
        private static int? retryMean = null;

        private static int RetryLevels
        {
            get => primes.Length;
        }

        public static int RetryPeriod(int numRetry)
        {
            return primes[numRetry % RetryLevels];
        }

        public static int RetryPeriodMean
        {
            get
            {
                if(!retryMean.HasValue)
                    retryMean = primes.Sum() / RetryLevels;
                return retryMean.Value;
            }
        }
        
        public class RetryEstimatorInput
        {
            public long InitialRetryTime { get; set; } = 15;
            public int MaxRetryTime { get; set; } = 120;
        }
        
        public static int TruncatedLinearRetryPeriod(int numRetry, RetryEstimatorInput estimators)
        {
            return (int) Math.Min(numRetry * estimators.InitialRetryTime, estimators.MaxRetryTime);
        }
        
        public static int ExponentialRetryPeriod(int numRetry, RetryEstimatorInput estimators)
        {
            int repeatAmount = 3;
            int expo = numRetry / repeatAmount; 
            double maxExpo = Math.Log(estimators.MaxRetryTime, 2);
            
            if (expo > maxExpo)
            {
                return estimators.MaxRetryTime;
            }
            return (int) (estimators.InitialRetryTime * Math.Pow(2, expo));
        }
    }
}