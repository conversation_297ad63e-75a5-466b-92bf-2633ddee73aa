using System.Text.RegularExpressions;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class MaskingUtil
    {
        public static string MaskPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber))
            {
                return phoneNumber;
            }
            string maskedCc = phoneNumber.Replace(phoneNumber, new string('x', phoneNumber.Length - 4));
            return Regex.Replace(maskedCc, @"(.{4})", "$1") + phoneNumber.Substring(phoneNumber.Length - 4, 4);
        }
    }
}