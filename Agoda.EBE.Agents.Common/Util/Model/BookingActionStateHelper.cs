using System;
using System.Collections;
using System.Linq;
using Agoda.EBE.Framework.Objects;
using Agoda.EBE.Framework.Objects.MultiProductBooking;
using Com.Agoda.Mpbe.State.Product.Property;
using Com.Agoda.Commons.Agprotobuf.Net.Utils;
using Com.Agoda.Commons.Agprotobuf.Types;
using EbePropertyBookingSupplierData = Com.Agoda.Mpbe.State.Product.Property.EbePropertyBookingSupplierData;

namespace Agoda.EBE.Agents.Common.Util.Model
{
    public static class BookingActionStateHelper
    {
        public static bool WithZoneRetained = true;
        #region Provision Helper


        public static BookingActionState UpdatePriceDisplaySettingProto(this BookingActionState bookingActionState, int bookingId, string priceDisplaySetting)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var booking = itineraryState.Product.Properties.AsQueryable()
                    .SingleOrDefault(p => p.Booking.BookingId == bookingId);

                if (booking != null)
                    booking.BookingSummary.PriceDisplaySetting = priceDisplaySetting;

                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }

        public static BookingActionState UpdateEBEBookingProvisioningProto(this BookingActionState bookingActionState,
            int bookingProvisioningID,
            int processType,
            int replyStatus,
            string requestXML,
            string responseXML,
            decimal netRate,
            string localCurrency,
            string remarks,
            DateTime dmcDueDate,
            string bookingExternalReference,
            string token, Guid userGuid,
            DateTime now)
        {

            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var booking = itineraryState.Product.Properties.First();
                var provisioning = booking.Provisionings
                    .FirstOrDefault(x => x.ReferenceId == bookingProvisioningID);

                if (provisioning != null)
                {
                    // Update
                    provisioning.MethodId = (int)processType;
                    provisioning.TransmittalDate = Converters.ToTimestamp(now, WithZoneRetained);
                    provisioning.ReplyDate = Converters.ToTimestamp(now, WithZoneRetained);
                    provisioning.ReplyStatus = replyStatus;
                    provisioning.RequestXML = requestXML;
                    provisioning.ResponseXML = responseXML;
                    provisioning.Remarks = remarks;
                    provisioning.NetRate = netRate.toProtoDecimalValue();
                    provisioning.LocalDmcCurrency = localCurrency;
                    provisioning.DmcDueDate = Converters.ToTimestamp(dmcDueDate, WithZoneRetained);
                    provisioning.BookingExternalReference = bookingExternalReference;
                    provisioning.RecModifyWhen = Converters.ToTimestamp(now, WithZoneRetained);
                    provisioning.RecModifyBy = userGuid.toUUID();
                    provisioning.Token = token;
                }

                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }

        public static BookingActionState InsertEBEBookingProvisioningProto(this BookingActionState bookingActionState,
            int bookingId,
            int processType,
            int newReferenceId,
            int dmcId,
            DateTime dmcDueDate,
            string localDMCCurrency,
            decimal netRate,
            int replyStatus,
            decimal originalNetRate,
            string requestXML,
            string responseXML,
            string remarks,
            string bookingExternalReference,
            string token,
            Guid userGuid,
            DateTime now)
        {

            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var booking = itineraryState.Product.Properties.First();

                var provisioningToInsert = new EbePropertyBookingProvisioning
                {
                    ReferenceId = newReferenceId,
                    BookingId = bookingId,
                    DmcId = dmcId,
                    MethodId = processType,
                    TransmittalDate = Converters.ToTimestamp(now, WithZoneRetained),
                    ReplyDate = Converters.ToTimestamp(now, WithZoneRetained),
                    DmcDueDate = Converters.ToTimestamp(dmcDueDate, WithZoneRetained),
                    LocalDmcCurrency = localDMCCurrency,
                    OriginalNetRate = originalNetRate.toProtoDecimalValue(),
                    NetRate = netRate.toProtoDecimalValue(),
                    BookingExternalReference = bookingExternalReference,
                    RequestXML = requestXML,
                    ResponseXML = responseXML,
                    Remarks = remarks,
                    ReplyStatus = replyStatus,
                    RecStatus = (int)Common.Enum.RecStatus.Active,
                    RecCreatedWhen = Converters.ToTimestamp(now, WithZoneRetained),
                    RecCreatedBy = userGuid.toUUID(),
                    PurchaseDetails = string.Empty
                };

                booking.Provisionings.Add(provisioningToInsert);

                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }


        public static BookingActionState UpdateDMCSpecificDataProto(this BookingActionState bookingActionState,
            string supplierSpecificData,
            Guid userGuid,
            DateTime now)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var booking = itineraryState.Product.Properties.First();

                booking.Booking.DmcSpecificData = supplierSpecificData;
                booking.Booking.RecModifyBy = userGuid.toUUID();
                booking.Booking.RecModifyWhen = Converters.ToTimestamp(now, WithZoneRetained);

                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }

        public static BookingActionState UpdateBookingExtReferenceProto(this BookingActionState bookingActionState, string bookingExtRef, Guid userGuid, DateTime now)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var activeBooking = itineraryState.Product.Properties.First();

                activeBooking.Booking.BookingExternalReference = bookingExtRef;
                activeBooking.Booking.RecModifyBy = userGuid.toUUID();
                activeBooking.Booking.RecModifyWhen = Converters.ToTimestamp(now, WithZoneRetained);

                bookingActionState.ItineraryState = itineraryState;


            }

            return bookingActionState;
        }
        
        public static BookingActionState UpsertBookingSupplierDataProto(this BookingActionState bookingActionState,
            string supplierSiteId,
            string supplierSubsiteId,
            string supplierTransactionId,
            string inventoryData,
            Guid userGuid,
            DateTime now,
            string supplierResponseInfo)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var propertyProduct = itineraryState.Product.Properties.First();

                if (propertyProduct.BookingSupplierData == null)
                {
                    propertyProduct.BookingSupplierData = new EbePropertyBookingSupplierData
                    {
                        RecStatus = (int)Enum.RecStatus.Active,
                        RecCreatedBy = userGuid.toUUID(),
                        RecCreatedWhen = Converters.ToTimestamp(now, WithZoneRetained)
                    };
                }
                else
                {
                    propertyProduct.BookingSupplierData.RecModifyBy = userGuid.toUUID();
                    propertyProduct.BookingSupplierData.RecModifyWhen = Converters.ToTimestamp(now, WithZoneRetained);
                }

                propertyProduct.BookingSupplierData.SupplierSiteId = supplierSiteId;
                propertyProduct.BookingSupplierData.SupplierSubSiteId = supplierSubsiteId;
                propertyProduct.BookingSupplierData.SupplierTransactionId = supplierTransactionId;
                propertyProduct.BookingSupplierData.InventoryData = inventoryData;
                propertyProduct.BookingSupplierData.SupplierResponseInfo = supplierResponseInfo;

                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }

        public static BookingActionState UpdateIsConfirmedBookingProto(this BookingActionState bookingActionState, bool isConfirmed, Guid userGuid, DateTime now)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var activeBooking = itineraryState.Product.Properties.First();

                activeBooking.BookingSummary.IsConfirmedBooking = isConfirmed;
                activeBooking.BookingSummary.RecModifyBy = userGuid.toUUID();
                activeBooking.BookingSummary.RecModifyWhen = Converters.ToTimestamp(now, WithZoneRetained);

                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }

        public static BookingActionState UpdateInventoryTypeIdProto(this BookingActionState bookingActionState,
            int inventoryTypeId,
            Guid userGuid,
            DateTime now)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var booking = itineraryState.Product.Properties.First();

                booking.RateCategory.InventoryTypeId = inventoryTypeId;
                booking.RateCategory.LastUpdatedBy = userGuid.toUUID();
                booking.RateCategory.LastUpdatedWhen = Converters.ToTimestamp(now, WithZoneRetained);

                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }
        
        public static BookingActionState UpdateSupplierSessionIdProto(
            this BookingActionState bookingActionState, string supplierSessionId)
        {
            if (string.IsNullOrEmpty(bookingActionState.ItineraryProtoState)) return bookingActionState;
            
            var itineraryState = bookingActionState.ItineraryState;
            var bookingProto = itineraryState.Product.Properties.First();
            var additionalData = bookingProto.AdditionalData ?? new EbeAdditionalBookingData();

            additionalData.SupplierSessionId = supplierSessionId;
            bookingProto.AdditionalData = additionalData;
            bookingActionState.ItineraryState = itineraryState;
            return bookingActionState;
        }
        #endregion

        #region UPC Helper

        public static BookingActionState UpdateBreakdownProto(this BookingActionState state, int hotelPaymentMethodId, Guid userId, long? upcId, DateTime now)
        {
            if (!String.IsNullOrEmpty(state.ItineraryProtoState))
            {
                var itineraryState = state.ItineraryState;
                var propertyState = itineraryState.Product.Properties.First();
                if (ListHelper.IsNullOrEmpty(propertyState.FinancialBreakdowns)) return state;

                propertyState.FinancialBreakdowns.Where(fb => { return fb.HotelPaymentMethodId == 0; }).ToList()
                    .ForEach(financialBreakDown =>
                    {
                        financialBreakDown.HotelPaymentMethodId = hotelPaymentMethodId;
                        if (upcId != null)
                            financialBreakDown.UpcId = upcId.Value;

                        financialBreakDown.RecModifyWhen = Converters.ToTimestamp(now, WithZoneRetained);
                        financialBreakDown.RecModifyBy = Converters.toUUID(userId);
                    });

                state.ItineraryState = itineraryState;
            }

            return state;
        }

        public static BookingActionState InsertSuccProto(this BookingActionState bookingActionState, int bookingid,
            long succid, Guid? payoutUuid, string last4, double exchangerate, string requestcurrency,
            double requestcreditlimit, double actualcreditlimit, string currencycode, double amount, int cardtype,
            int cardstatus, Guid userid, DateTime now)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var propertyInternalBooking = itineraryState.Product.Properties.First();


                var nextBookingSuccId = 1;

                if (!ListHelper.IsNullOrEmpty(propertyInternalBooking.BookingSingleUsedCreditCards))
                {
                    nextBookingSuccId = propertyInternalBooking.BookingSingleUsedCreditCards
                        .OrderByDescending(x => x.BookingSuccId.GetValueOrDefault())
                        .First().BookingSuccId.GetValueOrDefault() + 1;
                }


                UUID payoutUuidParameter = null;
                if (payoutUuid.HasValue)
                {
                    payoutUuidParameter = payoutUuid.Value.toUUID();
                }
                var bookingSuccRecord = new EbePropertyBookingSingleUsedCreditCard();
                bookingSuccRecord.BookingSuccId = nextBookingSuccId;
                bookingSuccRecord.BookingId = bookingid;
                bookingSuccRecord.SuccId = succid;
                bookingSuccRecord.PayoutUuid = payoutUuidParameter;
                bookingSuccRecord.Last4 = last4;
                bookingSuccRecord.ExchangeRate = Converters.toProtoDecimalValue((decimal)exchangerate);
                bookingSuccRecord.RequestCurrency = requestcurrency;
                bookingSuccRecord.RequestCreditLimit = Converters.toProtoDecimalValue((decimal)requestcreditlimit);
                bookingSuccRecord.ActualCreditLimit = Converters.toProtoDecimalValue((decimal)actualcreditlimit);
                bookingSuccRecord.CurrencyCode = currencycode;
                bookingSuccRecord.Amount = Converters.toProtoDecimalValue((decimal)amount);
                bookingSuccRecord.CardType = cardtype;
                bookingSuccRecord.CardStatus = cardstatus;
                bookingSuccRecord.IsSubmitted = false;
                bookingSuccRecord.RecStatus = (int)Common.Enum.RecStatus.Active;
                bookingSuccRecord.RecCreatedWhen = Converters.ToTimestamp(now, WithZoneRetained);
                bookingSuccRecord.RecCreatedBy = userid.toUUID();

                propertyInternalBooking.BookingSingleUsedCreditCards.Add(bookingSuccRecord);

                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }

        public static BookingActionState UpdateSuccAmountProto(this BookingActionState bookingActionState,
            int bookingId, int bookingsuccid, decimal exchangerate, decimal requestcreditlimit, decimal actualcreditlimit,
            string currencycode, Guid userid, string requestcurrency, decimal amount, DateTime now)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState?.ItineraryState;
                var ebeBookingSucc = itineraryState?.Product.Properties.First().BookingSingleUsedCreditCards
                    .FirstOrDefault(x => x.BookingSuccId == bookingsuccid);

                if (ebeBookingSucc == null) return bookingActionState;

                ebeBookingSucc.ExchangeRate = exchangerate.toProtoDecimalValue();
                ebeBookingSucc.RequestCreditLimit = requestcreditlimit.toProtoDecimalValue();
                ebeBookingSucc.ActualCreditLimit = actualcreditlimit.toProtoDecimalValue();
                ebeBookingSucc.CurrencyCode = currencycode;
                ebeBookingSucc.RequestCurrency = requestcurrency;
                ebeBookingSucc.Amount = amount.toProtoDecimalValue();
                ebeBookingSucc.RecModifyWhen = Converters.ToTimestamp(now, WithZoneRetained);
                ebeBookingSucc.RecModifyBy = userid.toUUID();

                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }

        public static BookingActionState UpdateSuccCancelStatusProto(this BookingActionState bookingActionState,
            int bookingId, int bookingSuccId, int recStatus, int cardStatus, Guid userId, DateTime now)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var booking = itineraryState.Product.Properties.First();
                var ebeBookingSucc = booking.BookingSingleUsedCreditCards.FirstOrDefault(x => x.BookingSuccId == bookingSuccId);

                if (ebeBookingSucc == null) return bookingActionState;

                ebeBookingSucc.RequestCreditLimit = decimal.Zero.toProtoDecimalValue();
                ebeBookingSucc.ActualCreditLimit = decimal.Zero.toProtoDecimalValue();
                ebeBookingSucc.Amount = decimal.Zero.toProtoDecimalValue();
                ebeBookingSucc.RecStatus = recStatus;
                ebeBookingSucc.CardStatus = cardStatus;
                ebeBookingSucc.RecModifyBy = userId.toUUID();
                ebeBookingSucc.RecModifyWhen = Converters.ToTimestamp(now, WithZoneRetained);

                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }

        public static BookingActionState UpdateFinanceInfoInAdditionalBookingDataProto(
            this BookingActionState bookingActionState, int hotelPaymentMethodId, int hotelPaymentConditionId)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var booking = itineraryState.Product.Properties.First();
                var additionalData = booking.AdditionalData ?? new EbeAdditionalBookingData();
                additionalData.HotelPaymentMethodId = hotelPaymentMethodId;
                additionalData.HotelPaymentConditionId = hotelPaymentConditionId;
                booking.AdditionalData = additionalData;
                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }

        public static BookingActionState UpdateBookingIsAdvanceGuaranteeProto(
            this BookingActionState bookingActionState, int bookingId, bool isAdvanceGuarantee, Guid userId, DateTime now)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var booking = itineraryState.Product.Properties.First();
                var sellInfo = booking.SellInfo;

                if (sellInfo == null) return bookingActionState;

                var oldSellInfo = new EbePropertyBookingSellInfo()
                {
                    BookingId = sellInfo.BookingId,
                    PricingTemplateId = sellInfo.PricingTemplateId,
                    DownLiftAmountUsd = sellInfo.DownLiftAmountUsd,
                    SellTagId = sellInfo.SellTagId,
                    SearchId = sellInfo.SearchId,
                    IsAdvanceGuarantee = sellInfo.IsAdvanceGuarantee,
                    OfferId = sellInfo.OfferId,
                    LastUpdatedWhen = sellInfo.LastUpdatedWhen,
                    LastUpdatedBy = sellInfo.LastUpdatedBy,
                    PricingRequestId = sellInfo.PricingRequestId
                };

                sellInfo.IsAdvanceGuarantee = isAdvanceGuarantee;
                sellInfo.LastUpdatedWhen = Converters.ToTimestamp(now, WithZoneRetained);
                sellInfo.LastUpdatedBy = userId.toUUID();

                var history = new EbePropertyBookingSellInfoHistory();
                history.HistoryActionId = 2;
                history.HistoryActionDate = Converters.ToTimestamp(now, WithZoneRetained);
                history.HistoryActionBy = userId.toUUID();
                history.BookingId = oldSellInfo.BookingId;
                history.PricingTemplateId = oldSellInfo.PricingTemplateId;
                history.DownliftAmountUsd = oldSellInfo.DownLiftAmountUsd;
                history.SellTagId = oldSellInfo.SellTagId;
                history.LastUpdatedWhen = oldSellInfo.LastUpdatedWhen;
                history.SearchId = oldSellInfo.SearchId;
                history.IsAdvanceGuarantee = oldSellInfo.IsAdvanceGuarantee;
                history.LastUpdatedBy = oldSellInfo.LastUpdatedBy;
                history.FiredrillContractId = oldSellInfo.FiredrillContractId;
                history.FiredrillContractTypeId = oldSellInfo.FiredrillContractTypeId;

                booking.SellInfoHistories.Add(history);

                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }
        
        public static BookingActionState InsertBookingResellForCancellationProto(this BookingActionState bookingActionState,
            int bookingId,
            int resellStatusId,
            Guid userGuid,
            DateTime now)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var booking = itineraryState.Product.Properties.First();

                booking.BookingResell.BookingId = bookingId;
                booking.BookingResell.ResellStatusId = resellStatusId;
                booking.BookingResell.RecStatus = 1;
                booking.BookingResell.RecCreatedWhen = Converters.ToTimestamp(now, WithZoneRetained);
                booking.BookingResell.RecCreatedBy = userGuid.toUUID();
                
                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }
        
        public static BookingActionState UpdateBookingResellForCancellation(this BookingActionState bookingActionState,
            int bookingId,
            int resellBookingId,
            int resellStatusId,
            Guid userGuid,
            DateTime now)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var booking = itineraryState.Product.Properties.First();

                booking.BookingResell.BookingId = bookingId;
                booking.BookingResell.ResellBookingId = resellBookingId;
                booking.BookingResell.ResellStatusId = resellStatusId;
                booking.BookingResell.RecStatus = 1;
                booking.BookingResell.RecModifyWhen = Converters.ToTimestamp(now, WithZoneRetained);
                booking.BookingResell.RecModifyBy = userGuid.toUUID();
                
                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }
        
        public static BookingActionState UpdateBookingResellForCreateBooking(this BookingActionState bookingActionState,
            int bookingId,
            int resellBookingId,
            int resellStatusId,
            Guid userGuid,
            DateTime now)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;
                var booking = itineraryState.Product.Properties.First();

                booking.BookingResell.BookingId = bookingId;
                booking.BookingResell.ResellBookingId = resellBookingId;
                booking.BookingResell.ResellStatusId = resellStatusId;
                booking.BookingResell.RecStatus = 1;
                booking.BookingResell.RecModifyWhen = Converters.ToTimestamp(now, WithZoneRetained);
                booking.BookingResell.RecModifyBy = userGuid.toUUID();
                
                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }


        #endregion

        #region Vehicle UPC Helper

        public static BookingActionState VehicleUpdateBookingActionStateProto(
            this BookingActionState bookingActionState,
            long ccid)
        {
            if (!String.IsNullOrEmpty(bookingActionState.ItineraryProtoState))
            {
                var itineraryState = bookingActionState.ItineraryState;

                var vehicleProto = itineraryState.Product.Vehicles.First();
                foreach (var vehicleFinancialBreakdown in vehicleProto.Breakdowns)
                {
                    vehicleFinancialBreakdown.UpcId = ccid;
                }

                bookingActionState.ItineraryState = itineraryState;
            }

            return bookingActionState;
        }

        #endregion
    }

    static class ListHelper
    {
        public static Boolean IsNullOrEmpty(IList list)
        {
            if (list == null || list.Count == 0) return true;

            return false;
        }
    }
}