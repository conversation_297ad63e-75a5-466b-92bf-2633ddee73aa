using System;
using System.Collections.Generic;
using Agoda.EBE.Agents.Common.Messaging;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class ExperimentUtils
    {
        public const string BVariant = "B";
        
        public static bool IsForcedB(Dictionary<string, string> forcedExperiments, string experimentName, IMessaging messaging)
        {
            if (forcedExperiments == null)
            {
                CountUnsafeExperimentLookup(experimentName, messaging);
                return false;
            }
            var hasValue = forcedExperiments.TryGetValue(experimentName, out var experimentValue);
            if (experimentValue == null || !hasValue)
            {
                CountUnsafeExperimentLookup(experimentName, messaging);
                return false;
            }
            return experimentValue.Equals(BVariant, StringComparison.OrdinalIgnoreCase);
        }

        private static void CountUnsafeExperimentLookup(string experimentName, IMessaging messaging)
        {
            var measurement = messaging.MeasurementMessageFactory.CreateNewMeasurement();
            measurement.BeginTrack();
            measurement.Tags["experiment"] = experimentName;
            measurement.EndTrack(Enum.Measurement.UnsafeExperimentLookup, true);
        }
    }
}