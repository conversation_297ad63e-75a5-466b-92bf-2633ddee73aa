using System.Collections.Generic;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class MultiProductUtil
    {
        /// <summary>
        /// Products that run under Multi-Product Booking Engine Flow (completion in local DC)
        /// </summary>
        private static readonly HashSet<Enum.MultiProductType> MultiProducts = new HashSet<Enum.MultiProductType>(new Enum.MultiProductType[] {
            Enum.MultiProductType.Package,
            Enum.MultiProductType.MixAndSave,
            Enum.MultiProductType.HackerFare,
            Enum.MultiProductType.SingleFlight,
            Enum.MultiProductType.SingleProperty,
            Enum.MultiProductType.FlightWithProtection,
            Enum.MultiProductType.SingleVehicle,
            Enum.MultiProductType.SingleProtection,
            Enum.MultiProductType.MultiProperties
        });
        
        /// <summary>
        /// Products that we have itinerary_payment table in BFDB
        /// </summary>
        private static readonly HashSet<Enum.MultiProductType> ItineraryPaymentProducts = new HashSet<Enum.MultiProductType>(new Enum.MultiProductType[] {
            Enum.MultiProductType.Package,
            Enum.MultiProductType.MixAndSave,
            Enum.MultiProductType.HackerFare,
            Enum.MultiProductType.SingleFlight,
            Enum.MultiProductType.FlightWithProtection,
            Enum.MultiProductType.SingleVehicle,
            Enum.MultiProductType.SingleProtection,
            Enum.MultiProductType.MultiProperties
        });
        
        public static bool IsMultiProductFlow(Enum.MultiProductType multiProductType)
        {
            return MultiProducts.Contains(multiProductType);
        }

        public static bool IsItineraryPaymentProduct(Enum.MultiProductType multiProductType)
        {
            return ItineraryPaymentProducts.Contains(multiProductType);
        }
    }
}