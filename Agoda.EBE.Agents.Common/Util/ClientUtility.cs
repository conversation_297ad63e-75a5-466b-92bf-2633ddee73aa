﻿using System.Linq;
using Agoda.EBE.Framework.ClientBase;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class ClientUtility
    {
        public static string BuildErrorFromClientBaseResponse(Response response)
        {
            if (response?.ErrorList == null || !response.ErrorList.Any())
            {
                return string.Empty;
            }

            var errorList = response.ErrorList.Select(x => $"{x.ErrorID}: {x.ErrorMessage}");
            return string.Join(",", errorList);
        }
    }
}
