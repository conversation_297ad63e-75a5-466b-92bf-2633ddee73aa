using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading.Tasks;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class ObjectUtils
    {
        public static Byte[] DecompressBytes(Byte[] data)
        {
            using (MemoryStream input = new MemoryStream())
            {
                input.Write(data, 0, data.Length);
                input.Position = 0;
                using (GZipStream gzip = new GZipStream(input, CompressionMode.Decompress, true))
                {
                    using (MemoryStream output = new MemoryStream())
                    {
                        Byte[] buff = new Byte[1024];
                        int read = -1;

                        read = gzip.Read(buff, 0, buff.Length);
                        while (read > 0)
                        {
                            output.Write(buff, 0, read);
                            read = gzip.Read(buff, 0, buff.Length);
                        }

                        return output.ToArray();
                    }
                }
            }
        }

        public static T ExecuteAsyncSafely<T>(Func<Task<T>> func)
        {
            try
            {
                return Task.Run(async () => await func()).Result;
            }
            catch (Exception ex)
            {
                if (ex is AggregateException ae)
                {
                    Exception innerException = ae.Flatten().InnerException;
                    if (innerException != null) throw innerException;
                }

                throw;
            }
            
        }
        
        public static void ExecuteAsyncSafely(Func<Task> func)
        {
            try
            {
                Task.Run(async () => await func()).Wait();
            }
            catch (AggregateException ex)
            {
                Exception innerException = ex.Flatten().InnerException;
                if (innerException != null) throw innerException;

                throw;
            }
            
        }
    }

    
    
    
}