using System;
using System.Collections.Generic;
using System.Linq;
using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Agents.Common.Util.Model;
using Agoda.EBE.Framework.Objects.MultiProductBooking;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class TotalChargeAmountUtils
    {
        public static bool IsCurrencyDifferent(string chargeCurrency, string succCurrency)
        {
            return !string.IsNullOrEmpty(succCurrency) &&
                     !string.IsNullOrWhiteSpace(succCurrency) &&
                     succCurrency != chargeCurrency;
        }

        public static AmountByBookingResult GetAmountByBookingResults(List<EbeBookingCharges> bookingCharges,
            bool calculateDiscount = true)
        {
            if (ListHelper.IsNullOrEmpty(bookingCharges))
            {
                return null;
            }
            
            var tempBookingCharges = bookingCharges.Where(bc =>
            {
                if ((bc.RecStatus != (int)Common.Enum.RecStatus.Active) || (calculateDiscount && bc.ChargeTypeId == 207)) return false;
                return true;
            });

            var totalLocalCogs = decimal.Zero;
            var totalChargesLocal = decimal.Zero;

            tempBookingCharges.ToList().ForEach(bc =>
            {
                totalChargesLocal += (bc.SellingAmount.GetValueOrDefault() * bc.ExchangeRateLocalToSupplier.GetValueOrDefault());
                totalLocalCogs += bc.LocalSupplierAmount.GetValueOrDefault();
            });

            return new AmountByBookingResult
            {
                TotalLocalCOGS = totalLocalCogs,
                TotalChargesLocal = totalChargesLocal
            };
        }

        public static (decimal amountUsd, decimal amountLocal) GetFutureBalanceAmount(
            bool isPaymentModelMerchantCommission,
            List<FinancialBreakdownByBookingResult> financialBreakdownListResult,
            AmountByBookingResult? amountByBookingResult,
            decimal chargeExchangeRate,
            bool isDifferentCurrency,
            decimal succExchangeRate)
        {
            (decimal amountUsd, decimal amountLocal) totalCharge = (0.0M, 0.0M);

            if (amountByBookingResult == null ||
                (amountByBookingResult.TotalChargesLocal == 0.0M && amountByBookingResult.TotalLocalCOGS == 0.0M))
            {
                return totalCharge;
            }

            var chargeLocalAmount = isPaymentModelMerchantCommission ?
                amountByBookingResult.TotalChargesLocal :
                amountByBookingResult.TotalLocalCOGS;

            if (isPaymentModelMerchantCommission && financialBreakdownListResult != null)
            {
                // use a sum of ref sell in for calculation in case of Merchant Commission
                var refSellInResultList = financialBreakdownListResult.Where(f => f.ItemId == 41);
                if (refSellInResultList.Count() != 0)
                {
                    chargeLocalAmount = refSellInResultList.Sum(r => r.LocalAmount * r.Quantity);
                }
            }

            var chargeAmount = chargeLocalAmount * chargeExchangeRate;
            totalCharge.amountUsd = chargeAmount;

             // if hotel setting currency is changed, for update card need to calculate local amount by succ currency.
            chargeLocalAmount = isDifferentCurrency ?
                 (chargeAmount / succExchangeRate) : // SUCC Local Currency
                chargeLocalAmount; // Charge Local Currency

            totalCharge.amountLocal = chargeLocalAmount;

            return totalCharge;
        }
    }
}