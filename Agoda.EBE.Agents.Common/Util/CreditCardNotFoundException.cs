﻿using System;

namespace Agoda.EBE.Agents.Common.Util
{

    /// <summary>
    /// Can throw this exception when there is no credit card found.
    /// </summary>
    public class CreditCardNotFoundException : Exception
    {
        public CreditCardNotFoundException(string message)
            : base(message)
        {
        }

        public CreditCardNotFoundException(string message, Exception inner)
            : base(message, inner)
        {
        }
    }
}