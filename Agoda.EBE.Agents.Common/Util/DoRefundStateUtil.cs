﻿using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Framework.Objects;
using System;

namespace Agoda.EBE.Agents.Common.Util
{
    public static class DoRefundStateUtil
    {
        public static DoRefundState InitialState(decimal amountToRefund, int actionResult)
        {
            return new DoRefundState()
            {
                AmountToRefund = Math.Abs(amountToRefund),
                WorkflowActionResult = actionResult,
                FinalActionResult = actionResult,
                Remark = string.Empty,
                ShouldProcessToNextStep = true,
            };
        }

        public static void UpdateStateWithExternalLoyaltyInfo(DoRefundState doRefundState, bool extLoyaltyInfoExists, bool loyaltyRefundProcessedFeatureEnabled)
        {
            doRefundState.ExternalLoyaltyInfoExists = extLoyaltyInfoExists;
            doRefundState.EnableLoyaltyRefundProcessedEmail = extLoyaltyInfoExists && loyaltyRefundProcessedFeatureEnabled;
        }

        public static void UpdateStateNotToProcessNextStep(DoRefundState doRefundState,
            int actionResult,
            string remark)
        {
            doRefundState.ShouldProcessToNextStep = false;
            doRefundState.WorkflowActionResult = actionResult;
            doRefundState.Remark = remark;
        }

        public static void UpdateFinalActionResult(DoRefundState doRefundState, int actionResult)
        {
            doRefundState.FinalActionResult = actionResult;
        }

        public static void UpdateDoRefundStateAmount(DoRefundState doRefundState, decimal refundedAmount)
        {
            doRefundState.AmountToRefund -= refundedAmount;
        }
    }
}
