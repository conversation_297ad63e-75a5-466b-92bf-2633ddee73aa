using System;

namespace Agoda.EBE.Agents.Common.Util
{
    /// <summary>
    /// Should throw this exception when found the booking state in RabbitMQ message and DB is mismatch.
    /// </summary>
    public class InvalidBookingStateException : Exception
    {
        public InvalidBookingStateException(string message)
            : base(message)
        {
        }

        public InvalidBookingStateException(string message, Exception inner)
            : base(message, inner)
        {
        }
    }
}