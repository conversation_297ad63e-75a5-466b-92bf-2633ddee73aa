using System;
using System.Linq;
using Agoda.EBE.Framework.Objects;


namespace Agoda.EBE.Agents.Common.Util.Samples
{
    public class PropertyBookingActionUtils
    {
        public bool IsPropertyConfirmed(BookingActionState bookingActionState)
        {
            var propertyInternalModel = bookingActionState?.PropertyInternalModel;

            var replyStatus = propertyInternalModel?.Bookings.FirstOrDefault()?.Provisioning
                .OrderBy(provisioning => provisioning.ReferenceId)
                .LastOrDefault()
                ?.ReplyStatus;
            return replyStatus == (int)Enum.ABSResponseStatus.AllotmentConfirmed;
        }
    }
}