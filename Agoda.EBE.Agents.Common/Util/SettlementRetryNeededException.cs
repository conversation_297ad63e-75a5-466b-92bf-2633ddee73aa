using System;

namespace Agoda.EBE.Agents.Common.Util
{
    /// <summary>
    /// Exception to indicate that we explicitly need to retry the whole process again.
    /// </summary>
    public class SettlementRetryNeededException : Exception
    {
        public SettlementRetryNeededException()
        {
            
        }
        
        public SettlementRetryNeededException(string message) : base(message)
        {
        }
        
        public SettlementRetryNeededException(string message, Exception inner)
            : base(message, inner)
        {
        }
    }
}