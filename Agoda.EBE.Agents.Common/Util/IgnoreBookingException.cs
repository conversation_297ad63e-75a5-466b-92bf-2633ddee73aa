using System;

namespace Agoda.EBE.Agents.Common.Util
{
    /// <summary>
    /// Can throw this exception when to ignore particular bookings in expected condition (no error).
    /// </summary>
    public class IgnoreBookingException : Exception
    {
        public IgnoreBookingException(string message)
            : base(message)
        {
        }

        public IgnoreBookingException(string message, Exception inner)
            : base(message, inner)
        {
        }
    }
}