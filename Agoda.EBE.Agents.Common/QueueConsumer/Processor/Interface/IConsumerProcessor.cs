using Agoda.EBE.Framework.MQ.MessageType;

namespace Agoda.EBE.Agents.Common.QueueConsumer.Processor.Interface
{
    public interface IConsumerProcessor
    {
        /// <summary>
        ///  Consumes the AMQP message
        /// </summary>
        /// <param name="message"></param>
        /// <param name="isRedelivered"></param>
        void Process(IDefaultMessage message, bool isRedelivered);
    }
}