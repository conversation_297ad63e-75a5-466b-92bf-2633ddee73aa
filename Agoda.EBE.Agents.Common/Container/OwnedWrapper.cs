using Autofac.Features.OwnedInstances;
using Autofac.Util;

namespace Agoda.EBE.Agents.Common
{
    public class OwnedWrapper<T> : Disposable, IOwned<T>
    {
        private readonly Owned<T> _ownedValue;

        public OwnedWrapper(Owned<T> ownedValue)
        {
            _ownedValue = ownedValue;    
        }

        public T Value => _ownedValue.Value;

        protected override void Dispose(bool disposing)
        {
            if (disposing)
                _ownedValue.Dispose();
        }
    }
}