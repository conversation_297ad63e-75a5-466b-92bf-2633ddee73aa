﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Agoda.Adp.Messaging.Client;

namespace Agoda.EBE.Agents.Common
{
    public class Enum
    {
        public const string MessagingApiKey = "ebe";

        public enum VisibleBookingStatus
        {
            Processing = 1,
            PayNothingUntil = 2,
            PayThePropertyAtCheckin = 3,
            PayTheProperty = 4,
            AwaitingPayment = 5,
            Paid = 6,
            Departed = 7,
            Cancelled = 8,
            PendingApproval = 9,
            ApprovedAndPaid = 10,
            HostDeclined = 11,
            RequestExpired = 12,
            CreditCardDeclined = 13,
            PendingCreditCardValidation = 14
        }

        public enum CreditCardType
        {
            None = 0,
            Visa = 1,
            MasterCard = 2,
            JCB = 3,
            AmericanExpress = 4,
            PayPal = 5,
            CarteBleue = 6,
            IPS = 7,
            IDEAL = 8,
            ALIPAY = 9,
            UnionPay = 10,
            Diners = 11,
            TAT = 221
        }

        public enum WorkflowPhaseId
        {
            None = 0,
            BookingReceived = 1,
            BookingConfirmed = 2,
            BookingCharged = 3,
            Departed = 4,
            CxlFullyBooked = 5,
            CxlByCustomer = 6,
            CxlOther = 7,
            CxlTestBooking = 8,
            TechnicalError = 10
        }

        public enum WorkflowState
        {
            None = 0,
            WaitingForPaymentOnReceivedBooking = 114,
            ULMBDueCustWaitDoc = 171,
            BookingDueCustWaitDoc = 172,
            AllotmentAlert = 213,
            WaitForFullAuthorizeCard = 292,
            WaitForApproveBoR = 297,
            BookingCompleted = 500,
            CustomerDeparted = 505,
            DelaySettlement = 304,
            CCAutoAuthorization = 306,
            WaitForSettleCard = 307,
            CCAutoCharge = 308,
            CCManualCharge = 311,
            RecurError = 316,
            ManualCVVBNPL = 320,
            SendingPaymentSecureLink = 319,
            TechnicalError = 900,
            DueDMCAmendment1 = 633,
            DueDMCWaitConfirmation1 = 638,
            WaitForPayment = 704,
            ManualAmendment = 709,
            DueDMCAmendment2 = 723,
            DueDMCWaitConfirmation2 = 728,
            SendCxlBookingToCustFailedBooking = 801,
            SendCxlBookingToCustAA = 805,
            SendMsgToHermesWaitForApprove = 836,
            ProvisioningForCancellation = 840,
            AdjustingUPCCard = 857,
            CCAutoRefundCxl = 893,
            AdjustUPCCardAfterFullyReplacement = 778,
            FlexisaleProvisioning = 771,
            FlexisaleRefund = 773
        }

        public enum WorkflowActionResult
        {
            CreditCardDeclined = 20,
            PendingCreditCardValidation = 21,
        }

        public enum AvailabilityType
        {
            Unknown = 0,
            Guarantee = 1,
            RealTime = 2,
            OnRequest = 3,
            OnRequestSupplier = 4,
        }

        public enum BookOnRequestStatus
        {
            None = 0,
            Pending = 1,
            Expired = 2,
            Accepted = 3,
            Declined = 4,
            AwaitingPayment = 5,
            Cancelled = 6,
            HostCancelled = 7,
            RequestProcessing = 8,
            Departed = 9
        }

        public enum HostBookingStatus
        {
            None = 0,
            Request = 1,
            Expired = 2,
            Confirmed = 3,
            Declined = 4,
            Processing = 5,
            Cancelled = 6,
            IssueWithBooking = 8,
            Departed = 9
        }

        public enum ChargeTypeId
        {
            ExtraCancellationFee = 32208
        }

        /// <summary>
        /// CC Received
        /// </summary>
        public enum CcReceived
        {
            /// <summary>
            /// Card has been preAuth
            /// </summary>
            YesWithPreAuth = 1,

            /// <summary>
            /// CC received
            /// </summary>
            Yes = 2,

            /// <summary>
            /// CC is not required
            /// </summary>
            No = 3,

            /// <summary>
            /// Card has been charged 1 USD (BNPL)
            /// </summary>
            PreAuth1USD = 4,

            /// <summary>
            /// CC has been preAuth but is not required anymore
            /// </summary>
            NoWithRecur = 5
        }

        /// <summary>
        /// Booking payment model
        /// </summary>
        public enum BookingPaymentModel
        {
            PayAgoda = 1,
            PayHotel = 2,
            HotelBooking = 3,
            MerchantCommission = 4
        }

        /// <summary>
        /// DMC ID
        /// </summary>
        public enum DmcId
        {
            YCS = 332,
            Bcom = 3038,
            JTB = 29014
        }

        public enum Measurement
        {
            PiiClientMetric = 44,
            
            //Experiment not forced in booking action
            UnsafeExperimentLookup = 1600
        }

        /// <summary>
        /// Agent running mode, Central is running in central DC, Local is running in Local DC
        /// </summary>
        public enum AgentMode
        {
            Central = 0,
            Local = 1
        }

        /// <summary>
        /// WorkflowID, 1 for Hotel and 2 for Flight
        /// </summary>
        public enum WorkflowID : int
        {
            None = 0,
            Hotel = 1,
            Flight = 2,
            ResendEmailConfirmation = 3,
            Vehicle = 4
        }

        public enum RecStatus
        {
            Disabled = -1,
            InActive = 0,
            Active = 1
        }

        public enum MultiProductType
        {
            None = 0,
            Package = 1,
            MixAndSave = 2,
            HackerFare = 3,
            SingleFlight = 4,
            SingleProperty = 5,
            FlightWithProtection = 6,
            SingleVehicle = 7,
            SingleProtection = 8,
            MultiProperties = 9,
            MultiFlightsWithProtection = 10,
            SingleActivity = 11,
            Cart = 12,
            SinglePriceFreeze = 13,
            CegFastTrack = 14,
            CancelForAnyReason = 15,
            ProductWithAncillary = 16
        }

        public enum UPCMeasurementForLocalWrapper
        {
            CancelCC = 1300,
            GetSuRules = 1301,
            RequestCC = 1302
        }

        /// <summary>
        /// Rejection root cause error code from BAPI workflow agents (extracted only ones needed for property)
        /// </summary>
        public enum RejectRootCause
        {
            NoError = 0,

            // 111XX - Property UPC Workflow Codes
            UpcCreateTechnicalError = 11101,

            // 112XX - Property Provisioning Workflow Codes
            ProvisioningCreateAllotmentRejected = 11201,
            ProvisioningCreateMaxRetryReached = 11202,
            ProvisioningCreateManualOperation = 11203,
            ProvisioningCreateAllotmentRejectedUnrecognizedAbsStatus = 11204,
            ProvisioningRejectAllotment = 11211,
            ProvisioningRejectAllotmentMaxRetryReached = 11212,
            ProvisioningRejectAllotmentRejectedUnrecognizedAbsStatus = 11214,
            ProvisioningRejectSbpsPost3DFieldsIsNotValid = 11215,
            ProvisioningRejectSbpsPrerequisiteFieldsIsNotValid = 11216,
            ProvisioningTechnicalErrorNoManualBox = 11291,
            ProvisioningTechnicalError = 11299,
            ProvisioningInstantBookingTimeout = 11298,
            ProvisioningDuplicationError = 11300,
            RejectBookingsOnManualReprocess = 11301
        }

        public enum PaymentType
        {
            CreditCard = 10,
            RewardsPoints = 11,
            Cash = 12,
            BankTransfer = 13,
            UsedAmount = 14,
            UsedCode = 15,
            CompanyAccount = 16,
            Invoice = 20,
            PartnerCorporateCard = 21,
            GiftCard = 22,

            /** DO NOT USE 23 and 24, it has been reserved for free GiftCard and purchased GiftCard  **/
            GiftCardCompensation = 25,
            GiftCardCashReplacement = 27,
            RocketMilesMiles = 28,
            ExternalLoyalty = 30,
        }

        /// <summary>
        /// Agent booking action message topic, use to communicate with BAPI component
        /// </summary>
        public enum BookingActionMessageTopic
        {
            Unknown = 0,
            Redirect = 1,
            PreAuth3dsRequired = 2,
            PreAuthCvcRequired = 3,
            Ticketing = 4,
            PreAuth3ds2Required = 5,
            ManualTicketingTimeout = 6,
            ManualFraud = 7,
            ProvisioningResult = 8,
            AwaitPaymentToken = 9,
            JtbPartnerProvisioningResult = 10,
            CrossSell = 11,
            OtpRequired = 12,
            WrongOtp = 13,
            BOR = 14,
            AwaitJtb3ds = 15
        }

        /// <summary>
        /// Provisioning Result that wrap into Booking action message content to track what is the provisioning status
        /// This Enum is a field in BookingActionMessage.class 
        /// </summary>
        public enum BookingActionMessageProvisioningResult
        {
            ProvisioningUnconfirmed = 0,
            ProvisioningConfirmed = 1,
            ProvisioningFailed = 2,
            ProvisioningTimeout = 3
        }

        public enum PaymentClientIdType
        {
            PropertyAgoda = 0,
            PaymentAgent = 15,
            SPWhitelabel = 131,
            PropertyAgodaV2 = 164
        }

        public enum ItineraryProductType
        {
            Unknown = 0,
            Hotel = 1
        }
        
        static class WhitelabelToken
        {
            public const string CitiUS = "5234834A-9346-47CE-BD5A-F2B754D3383E";
            public const string CitiUSUat1 = "3998FD0C-66D4-4867-BBDD-CBD2F9A4EBF0";
            public const string CitiUSUat2 = "15D8E08C-0498-4966-BC5F-440791FAD57A";
            public const string CitiUSUat3 = "0C9EA82A-9AAA-4AB7-9909-EF0CF839858E";
            public const string ClubTravel = "9EEE3B1E-7207-4E72-9B9E-A0090D78403B";
            public const string ANA = "BB7D5528-D107-40CC-8BB0-C5DD5A4F3053";
        }

        public static ItineraryProductType mapItineraryProductTypeFromMultiProductType(
            MultiProductType multiProductType)
        {
            switch (multiProductType)
            {
                case MultiProductType.MixAndSave:
                case MultiProductType.MultiProperties:
                case MultiProductType.SingleProperty:
                    return ItineraryProductType.Hotel;
                default:
                    return ItineraryProductType.Unknown;
            }
        }

        [Flags]
        public enum WhitelabelId
        {
            Agoda = 1,
            Jtb = 2,
            JtbUat = 201,
            Japanican = 3,
            JapanicanUat = 301,
            Rurubu = 4,
            RurubuUat = 401,
            ClubTravel = 45,
            LotteDFS = 47,
            ANA = 50,
            CitiUS = 51,
            US_BANK = 58,
            TRAVEL_1_MY_REWARDS = 59,
            TRAVEL_2_MY_REWARDS = 60,
            TRAVEL_3_MY_REWARDS = 61,
            TRAVEL_4_MY_REWARDS = 62,
            CitiUSUat1 = 5101,
            CitiUSUat2 = 5102,
            CitiUSUat3 = 5103,
            US_BANK_UAT = 5801,
            TRAVEL_1_MY_REWARDS_UAT = 5901,
            TRAVEL_2_MY_REWARDS_UAT = 6001,
            TRAVEL_3_MY_REWARDS_UAT = 6101,
            TRAVEL_4_MY_REWARDS_UAT = 6201,
            TestStrategicPartners = 999,
            BHFS_Agoda_Wallet = 101,
        }

        public static bool IsSPWLGroup(WhitelabelId whitelabelId)
        {
            switch (whitelabelId)
            {
                case WhitelabelId.ClubTravel:
                case WhitelabelId.LotteDFS:
                case WhitelabelId.TestStrategicPartners:
                case WhitelabelId.ANA:
                    return true;
                default:
                    return false;
            }
        }

        public static bool IsCitiGroup(WhitelabelId whitelabelId)
        {
            var list = new[]
                {WhitelabelId.CitiUS, WhitelabelId.CitiUSUat1, WhitelabelId.CitiUSUat2, WhitelabelId.CitiUSUat3};
            return list.Contains(whitelabelId);
        }
        
        public static bool IsUsBankGroup(WhitelabelId whiteLabelId)
        {
            HashSet<WhitelabelId> usBankWhitelabelIds = new HashSet<WhitelabelId>
            {
                WhitelabelId.US_BANK, WhitelabelId.TRAVEL_1_MY_REWARDS, WhitelabelId.TRAVEL_2_MY_REWARDS, WhitelabelId.TRAVEL_3_MY_REWARDS, WhitelabelId.TRAVEL_4_MY_REWARDS,
                WhitelabelId.US_BANK_UAT, WhitelabelId.TRAVEL_1_MY_REWARDS_UAT, WhitelabelId.TRAVEL_2_MY_REWARDS_UAT, WhitelabelId.TRAVEL_3_MY_REWARDS_UAT, WhitelabelId.TRAVEL_4_MY_REWARDS_UAT
            };
            return usBankWhitelabelIds.Contains(whiteLabelId);
        }

        
        public static string GetWhiteLabelToken(WhitelabelId whitelabelId)
        {
            if (whitelabelId == WhitelabelId.CitiUS) return WhitelabelToken.CitiUS;
            if (whitelabelId == WhitelabelId.CitiUSUat1) return WhitelabelToken.CitiUSUat1;
            if (whitelabelId == WhitelabelId.CitiUSUat2) return WhitelabelToken.CitiUSUat2;
            if (whitelabelId == WhitelabelId.CitiUSUat3) return WhitelabelToken.CitiUSUat3;
            if (whitelabelId == WhitelabelId.ClubTravel) return WhitelabelToken.ClubTravel;
            if (whitelabelId == WhitelabelId.ANA) return WhitelabelToken.ANA;
            return string.Empty;
        }

        public static WhitelabelId GetWhitelabelId(int? whitelabelId)
        {
            try
            {
                return whitelabelId != null && System.Enum.IsDefined(typeof(WhitelabelId), whitelabelId.Value)
                    ? (WhitelabelId) whitelabelId
                    : WhitelabelId.Agoda;
            }
            catch (Exception e)
            {
                LogMessage logMessage = new LogMessage("EBE", LogLevel.WARN,
                    "Can't resolve whitelabelId fallback to agoda whitelabelId", e)
                {
                    StringTags = new Dictionary<string, string>()
                };

                logMessage.StringTags.Add("WhitelabelId", whitelabelId.GetValueOrDefault(0).ToString());
                logMessage.Send(MessagingApiKey);

                return WhitelabelId.Agoda;
            }
        }
        
        public enum ABSResponseStatus
        { 
            //For State 215
            ConfirmAllotment = 1,

            Ok = 10,
            NotSupportMakeBooking = 11,
            NotSupportAmend = 12,
            NotSupportCancel = 13,
            NotSupportQuery = 14,

            // YCS Action Result
            AllotmentConfirmed = 20,
            AllotmentAlert = 21,
            CancellationConfirmed = 22,
            PartialSuccess = 23,
            BookingRequire3DS = 24,
            
            // YCS Action Result
            
            // Resell Action Result
            CancellationConfirmedForResell = 33,
            CancellationConfirmedForACR = 44,
            
            // manual reprocess
            RejectOnManualReprocess = 60,

            TechnicalErrorNoManualBox = 91,
            AllotmentRejected = 95,
            InstantBookingTimeout = 99,
            Skipped = 201,
            Others = 994,
            ManualOperationRequired = 995,
            TechnicalError = 998,
            Timeout = 999
        }

        public enum SnapshotType
        {
            PayoutPropertyCreateBASBefore = 101,
            PayoutPropertyCreatePayoutRequest = 102,
            PayoutPropertyCreatePayoutResponse = 103,
            PayoutPropertyCreateOrchestrationTask = 104,
            PayoutPropertyCreateBASAfter = 105,
 
            PayoutPropertyCxlBASBefore = 201,
            PayoutPropertyCxlPayoutRequest = 202,
            PayoutPropertyCxlPayoutResponse = 203,
            PayoutPropertyCxlOrchestrationTask = 204,
            PayoutPropertyCxlBASAfter = 205,
 
            ProvisioningCreateBASBefore = 301,
            ProvisioningCreateABSRequest = 302,
            ProvisioningCreateABSResponse = 303,
            ProvisioningCreateOrchestrationTask = 304,
            ProvisioningCreateBASAfter = 305,
 
            ProvisioningCxlBASBefore = 401,
            ProvisioningCxlABSRequest = 402,
            ProvisioningCxlABSResponse = 403,
            ProvisioningCxlOrchestrationTask = 404,
            ProvisioningCxlBASAfter = 405
        }
        
        public enum ChargeFilter
        {
            TotalLocalCOGS,
            TotalChargesLocal
        }

        public enum WhitelabelClientMeasurement
        {
            GetFeatureConfigByKey = 1500,
            IsFeatureEnabled = 1501,
            GetWhiteLabelKey = 1502
        }
        
        public enum InventoryType
        {
            OtaJapanican = 2
        }
        
        public enum ExchangeRateOption
        {
            FixedFX = 0,
            FloatingUpliftFX = 1,
            FloatingSiteFX = 2
        }
        
        public static class SourceComponent
        {
            public const string ReplyMessage = "ReplayMessage";
        }
        
        public enum EBEBookingResellStatus
        {
            Open = 1,            // When resell-able booking got cancelled
            Closed = 2,          // Deprecated (design changed)
            Confirmed = 3,       // When new customer create new booking with resell allotment
            Rejected = 4,        // When supplier reject guest's info change request
            Failed = 5,          // When CEG can not contact supplier to request guest's info change
            Cancelled = 6,       // When customer cancel resell booking
            TimeOut = 7,         // When resell-able booking can not resell before non-refundable period
            Purged = 8,          // When hotel opt out from ACR campaign
            OpenV2 = 9,          // When resell-able booking got cancelled (Resell V2)
            ConfirmedV2 = 10,    // When new customer create new booking with resell allotment (Resell V2)
            CancelledV2 = 11     // // When customer cancel resell booking (Resell V2)
        }

        public enum EBEBookingResellType
        {
            ResellV1 = 1,
            ACR = 2,
            SmartFlex = 3,
            CancelAndRebook = 4
        }

        public enum AABType
        {
            CustomerImpersonation = 0,
            CustomerAccountNoPayment = 1,
        }

        public enum WhiteLabelBookingWorkflowByDmcFlag
        {
            IsSupportSupplierMember,
            IsSimulateCancelSuccessAndAcknowledge,
            IsProcessBookingSupplierData,
            IsProcessSupplierPaymentGatewayData,
            IsJtbDmcBooking
        }
    }
}