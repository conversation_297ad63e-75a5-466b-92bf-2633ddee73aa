namespace Agoda.EBE.Agents.Common
{
    public class ResultDto<T>
    {
        public bool Succeeded { get; }
        public bool Failed => !Succeeded;

        public T Data { get; }
        public Error Error { get; }

        public ResultDto(T data) { Succeeded = true; Data = data; }
        public ResultDto(Error error) { Error = error; }
    }

    public static partial class F
    {
        public static ResultDto<T> ToResult<T>(this Either<Error, T> either)
            => either.Match(
                Left: error => new ResultDto<T>(error),
                Right: data => new ResultDto<T>(data)
            );
    }
}