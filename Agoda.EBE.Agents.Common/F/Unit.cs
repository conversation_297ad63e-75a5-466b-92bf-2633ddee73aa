using System;
using Unit = System.ValueTuple;

namespace Agoda.EBE.Agents.Common
{
    using static F;
    
    public static partial class F
    {
        public static Unit Unit() => default(Unit);
    }

    public static class ActionExt
    {
        public static Func<Unit> ToFunc(this Action action)
            => () =>
            {
                action();
                return Unit();
            };

        public static Func<T, Unit> ToFunc<T>(this Action<T> action)
            => (t) =>
            {
                action(t);
                return Unit();
            };
    }
}