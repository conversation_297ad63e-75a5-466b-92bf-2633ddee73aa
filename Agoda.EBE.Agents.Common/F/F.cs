using System;
using System.Data;
using System.Data.SqlClient;
using Unit = System.ValueTuple;

namespace Agoda.EBE.Agents.Common
{
    public static partial class F
    {
        public static R Using<TDisp, R>(TDisp disposable, Func<TDisp, R> f) where TDisp : IDisposable
        {
            using (disposable) return f(disposable);
        }
        
        public static Unit Using<TDisp>(TDisp disposable, Action<TDisp> act) where TDisp : IDisposable 
            => Using(disposable, act.ToFunc());
        
        public static R Connect<R>(string connStr, Func<IDbConnection, R> f)
            => Using(new SqlConnection(connStr)
                , conn => { conn.Open(); return f(conn); });

        public static Unit Connect(string connStr, Action<IDbConnection> f)
            => Connect(connStr, f.ToFunc());
        
        public static R Transact<R>
            (SqlConnection conn, Func<SqlTransaction, R> f)
        {
            R r = default(R);
            using (var tran = conn.BeginTransaction())
            {
                r = f(tran);
                tran.Commit();
            }
            return r;
        }
    }
}