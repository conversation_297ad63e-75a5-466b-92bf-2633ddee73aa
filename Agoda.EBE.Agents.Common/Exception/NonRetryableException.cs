using System;

namespace Agoda.EBE.Agents.Common.EbeCommonException
{
    public class NonRetryableException : Exception
    {
        public NonRetryableException(string storeProcName, Exception innerException) :
            base($"{storeProcName} return NonRetryableException", innerException)
        {
            
        }
    }
    
    public class CustomerApiException : NonRetryableException
    {
        public CustomerApiException(string name, Exception innerException) :
            base($"{name} return CustomerApiException", innerException)
        {
            
        }
    }
    
    public class CreditCardApiException : NonRetryableException
    {
        public CreditCardApiException(string name, Exception innerException) :
            base($"{name} return CreditCardApiException", innerException)
        {
            
        }
    }
}