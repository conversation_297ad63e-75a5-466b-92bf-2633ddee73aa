using System;

namespace Agoda.EBE.Agents.Common
{
    public class WhiteLabelClientInitializationException : Exception
    {
        public WhiteLabelClientInitializationException() : base()
        {
        }

        public WhiteLabelClientInitializationException(string message)
            : base(message)
        {
        }

        public WhiteLabelClientInitializationException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        public WhiteLabelClientInitializationException(Exception innerException)
            : base("Error starting whitelabel client.", innerException)
        {
        }
    }
}