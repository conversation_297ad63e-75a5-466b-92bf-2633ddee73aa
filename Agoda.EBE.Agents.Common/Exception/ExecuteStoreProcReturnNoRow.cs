namespace Agoda.EBE.Agents.Common.EbeCommonException
{
    public class ExecuteStoreProcReturnNoRow: System.Exception
    {
        public ExecuteStoreProcReturnNoRow(string storeProcName, string parameterStr = "") : 
            base(string.IsNullOrEmpty(parameterStr)?
                $"{storeProcName} return no row"
                :$"{storeProcName} return no row for {parameterStr}")
        {
            
        }
    }
}