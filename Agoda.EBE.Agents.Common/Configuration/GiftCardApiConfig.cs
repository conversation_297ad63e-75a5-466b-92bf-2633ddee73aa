using System.Collections.Generic;
using System.Linq;
using Agoda.Config.Consul.ServiceDiscovery.Models;
using Agoda.EBE.Agents.Common.ServiceDiscovery;

namespace Agoda.EBE.Agents.Common.Configuration
{
    public class GiftCardApiConfig
    {
        public string[] Urls { get; private set; }
        public string ApiKey { get; }

        public GiftCardApiConfig(IEnumerable<string> urls, string apiKey)
        {
            ApiKey = apiKey;
            Urls = urls?.ToArray();
        }
        
        public GiftCardApiConfig(GiftCardApiConsulService giftCardApiConsulService, string apiKey)
        {
            ApiKey = apiKey;
            if (giftCardApiConsulService != null)
            {
                Urls = giftCardApiConsulService.GetEndpoints()?.ToArray();
                giftCardApiConsulService.OnServiceListChanged += GiftcardApiService_OnServiceListChanged;
            }
        }
        private void GiftcardApiService_OnServiceListChanged(object sender, UpdateEventArgs e)
        {
            Urls = e.updatedServiceList.Select(GiftCardApiConsulService.GetEndpoint).ToArray();
        }
    }
}