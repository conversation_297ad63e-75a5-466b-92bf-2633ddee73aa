using System;
using System.Collections.Generic;
using System.Linq;
using Agoda.BookingApi.GenClient;
using Agoda.Config.Consul.ServiceDiscovery.Models;
using Agoda.EBE.Agents.Common.ServiceDiscovery;
using Agoda.EBE.Agents.Common.Util;

namespace Agoda.EBE.Agents.Common.Configuration
{
    public class BookingApiLoadBalancingConfig : LoadBalancingConfigBase
    {
        public BookingApiLoadBalancingConfig(IEnumerable<string> baseUrls,
            BookingApiConsulService bookingApiServiceDiscovery)
            : base(baseUrls, 0)
        {
            if (bookingApiServiceDiscovery != null)
            {
                bookingApiServiceDiscovery.OnServiceListChanged += BookingApiServiceDiscovery_OnServiceListChanged;
            }
        }

        private void BookingApiServiceDiscovery_OnServiceListChanged(object sender, UpdateEventArgs e)
        {
            var urls = e
                .updatedServiceList?
                .Select(BookingApiConsulService.GetEndpoint)?
                .Distinct()
                .ToArray();

            UpdateBaseUrls(urls);
        }
    }
}
