﻿// <copyright file="IConsoleConfiguration.cs" company="Agoda Company Co., Ltd.">
// AGODA ® is a registered trademark of AGIP LLC, used under license by Agoda Company Co., Ltd.. Agoda is part of Priceline (NASDAQ:PCLN)
// </copyright>
namespace Agoda.EBE.Agents.Common.Configuration.Interface
{
    using System.Collections.Concurrent;
    using System.Collections.Generic;

    public interface IConsoleConfiguration
    {
        List<ConnectionString> ConnectionStrings { get; set; }
        int ConfigurationGroupID { get; set; }
    }
}