using Agoda.EBE.Agents.Common.Configuration.Interface;

namespace Agoda.EBE.Agents.Common.Configuration
{
    public class ConsulSetting
    {
        public string AgentUrl { get; set; }
        public string ServerUrl { get; set; }
        public string DataCenter { get; set; }
        public int PollingIntervalInSeconds { get; set; }
        public string LocalFileCachePath { get; set; }
        public bool DontUseConsul { get; set; }
    }
}
