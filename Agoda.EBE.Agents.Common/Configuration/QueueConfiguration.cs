﻿//using Agoda.EBE.Agents.Common.Configuration.Interface;
//using System;
//using System.Collections.Generic;
//using System.Text;

//namespace Agoda.EBE.Agents.Common.Configuration
//{
//    public class QueueConfiguration : IQueueConfiguration
//    {
//        public string QueueName { get; set; }
//        public string QueueConnectionString { get; set; }
//        public int QueueMaxSubscriber { get; set; }
//    }
//}
