using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using Agoda.EBE.Agents.Common.Configuration.Interface;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.ServiceDiscovery;
using Agoda.PayoutApi.Client.Generated;
using Consul;
using NUnit.Framework;
using Org.BouncyCastle.Asn1.Cms;
using LogLevel = Agoda.Adp.Messaging.Client.LogLevel;

public class PayoutLoadBalancingConfig : Agoda.PayoutApi.Client.Generated.LoadBalancingConfigBase
{
    private readonly List<Agoda.Config.Consul.Abstractions.Service> _services;
    private IMessaging _messaging;

    public PayoutLoadBalancingConfig(IPayoutConfiguration config, IEnumerable<string> baseUrls,
        PayoutApiDiscovery payoutApiDiscovery, IMessaging messaging) : base(baseUrls, 0)
    {
        _services = payoutApiDiscovery.GetValue();
        _messaging = messaging;
        if (config.UsePayoutStaticUrls)
        {
            foreach (var url in baseUrls)
            {
                var protocolWithAddress = url.Split(new string[] {"://"}, StringSplitOptions.None);
                if (protocolWithAddress.Length != 2)
                {
                    throw new ArgumentException("URL must be in format <protocol>://<ip>:<port>");
                }

                var nodeWithPort = protocolWithAddress[1].Split(':');
                if (nodeWithPort.Length != 2)
                {
                    throw new ArgumentException("URL must be in format <protocol>://<ip>:<port>");
                }

                var node = nodeWithPort[0];
                var port = Convert.ToInt32(nodeWithPort[1]);
                var service = new CatalogService();
                service.ServiceAddress = node;
                service.ServicePort = port;
                _services.Add(new Agoda.Config.Consul.Abstractions.Service(service));
            }
        }
        else
        {
            payoutApiDiscovery.OnNodeAdded += PayoutDiscoveryOnOnNodeAdded;
            payoutApiDiscovery.OnNodeRemoved += PayoutDiscoveryOnOnNodeRemoved;
        }
    }

    private void PayoutDiscoveryOnOnNodeRemoved(Agoda.Config.Consul.Abstractions.Service service)
    {
        _services.Remove(service);
        this.UpdateBaseUrls(GetPayoutServer());
        _messaging.ExceptionMessage.Send(
            $"[PayoutApiDiscoveryOnOnNodeRemoved] result after remove node : {Newtonsoft.Json.JsonConvert.SerializeObject(_services)}",
            null, LogLevel.DEBUG);
    }

    private void PayoutDiscoveryOnOnNodeAdded(Agoda.Config.Consul.Abstractions.Service service)
    {
        _services.Add(service);
        this.UpdateBaseUrls(GetPayoutServer());
        _messaging.ExceptionMessage.Send(
            $"[PayoutApiDiscoveryOnOnNodeAdded] result after add node : {Newtonsoft.Json.JsonConvert.SerializeObject(_services)}",
            null, LogLevel.DEBUG);
    }

    public List<string> GetPayoutServer()
    {
        var payoutServers = new List<string>();
        _services.ForEach(s => payoutServers.Add("https://" + s.ServiceAddress + ":" + s.ServicePort));
        return payoutServers;
    }
}

public static class PayoutLoadBalancingConfigFactory
{
    public static PayoutLoadBalancingConfig GetPayoutLoadBalancingConfig(IPayoutConfiguration config, PayoutApiDiscovery payoutApiDiscovery,
        IMessaging messaging)
    {
        List<string> baseUrls = new List<string>();
        if (config.UsePayoutStaticUrls)
        {
            if (config.GetPayoutUrls.Count == 0)
            {
                throw new ArgumentNullException("PayoutUrls");
            }
            baseUrls.AddRange(config.GetPayoutUrls);
        }
        else
        {
            baseUrls.AddRange(payoutApiDiscovery.GetService(messaging));
        }
        messaging.ExceptionMessage.Send(
            $"[PayoutApiConfigurationFactory] get baseUrls : {Newtonsoft.Json.JsonConvert.SerializeObject(baseUrls)}",
            null, LogLevel.DEBUG);

        return new PayoutLoadBalancingConfig(config, baseUrls, payoutApiDiscovery, messaging);
    }
}
