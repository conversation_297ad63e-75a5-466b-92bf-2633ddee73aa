using System.Collections.Generic;
using System.Linq;

namespace Agoda.EBE.Agents.Common.Configuration.Interface
{
    public class PayoutConfiguration: IPayoutConfiguration
    {
        public bool UsePayoutStaticUrls { get; set; }
        public string PayoutUrls { get; set; }
        public string PayoutToken { get; set; }
        
        public List<string> GetPayoutUrls 
        {
            get
            {
                string urls;
                urls = PayoutUrls;
                if (urls == null || urls=="") return new List<string>();
                return urls.Split(';').ToList();
            }
        }
    }
}