﻿using Agoda.EBE.Agents.Common.Configuration.Interface;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using System;

namespace Agoda.EBE.Agents.Common.Configuration
{
    public class ConsulConfiguration<T> : IConsulConfiguration<T> where T : new()
    {
        private readonly IConfigurationRoot _configurationRoot;
        private readonly string _sectionName;
        private Action<T> _act;

        public ConsulConfiguration(IConfigurationRoot configurationRoot, string sectionName)
        {
            _configurationRoot = configurationRoot;
            _sectionName = sectionName;
            Bind();
        }

        private void Bind()
        {
            ChangeToken.OnChange(() => _configurationRoot.GetReloadToken(),
                InvokeConsulChanged,
                _configurationRoot.GetReloadToken());
        }

        public T Get()
        {
            var section = _configurationRoot.GetSection(_sectionName);
            var data = new T();
            section.Bind(data);
            return data;
        }

        void IConsulConfiguration<T>.RegisterOnChange(Action<T> act) => _act = act;

        private void InvokeConsulChanged(IChangeToken state)
        {
            if (state != null && state.HasChanged)
            {
                _act(Get());
            }
        }
    }
}
