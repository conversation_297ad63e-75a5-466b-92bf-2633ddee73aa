using Agoda.RoundRobin;

namespace Agoda.EBE.Agents.Common.Configuration
{
    public class WhiteLabelClientConfiguration
    {
        public ServerSettings[] ServerSettings { get; set; }
        public int TimeoutMilliseconds { get; set; }
        public string LocalCacheFilePath { get; set; }
        public int InitiateAsyncInterval { get; set; }
        public int? RetryInitializationInterval { get; set; }
        public int? RetryInitializationCount { get; set; }
    }
}