using System.Collections.Generic;
using System.Linq;
using Agoda.Config.Consul.ServiceDiscovery.Models;
using Agoda.EBE.Agents.Common.ServiceDiscovery;
using Agoda.ExternalLoyaltyApi.Client;

namespace Agoda.EBE.Agents.Common.Configuration
{
    public class ExternalLoyaltyApiLoadBalancingConfig : LoadBalancingConfigBase
    {
        public ExternalLoyaltyApiLoadBalancingConfig(IEnumerable<string> baseUrls,
            ExternalLoyaltyApiConsulService externalLoyaltyApiServiceDiscovery)
            : base(baseUrls, 0)
        {
            if (externalLoyaltyApiServiceDiscovery != null)
            {
                externalLoyaltyApiServiceDiscovery.OnServiceListChanged +=
                    ExternalLoyaltyApiServiceDiscovery_OnServiceListChanged;
            }
        }

        private void ExternalLoyaltyApiServiceDiscovery_OnServiceListChanged(object sender, UpdateEventArgs e)
        {
            this.UpdateBaseUrls(e.updatedServiceList.Select(ExternalLoyaltyApiConsulService.GetEndpoint));
        }
    }
}