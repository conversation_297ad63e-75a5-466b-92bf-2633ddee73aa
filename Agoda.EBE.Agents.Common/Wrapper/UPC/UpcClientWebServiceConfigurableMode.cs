using System;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.Payments.Upc.Integrate.Client;
using Agoda.Upc.Structure;

namespace Agoda.EBE.Agents.Common.Wrapper.UPC
{
    public class UpcClientWebServiceConfigurableMode : IUpcClientWebServiceConfigurableMode
    {
        private readonly IUpcWebServiceClient _localClient;
        private readonly IUpcWebServiceClient _centralClient;
        private readonly IMessaging _messaging;

        public UpcApiMode ApiMode { get; set; }

        public UpcClientWebServiceConfigurableMode(
            IUpcWebServiceClient localClient,
            IUpcWebServiceClient centralClient,
            IMessaging messaging)
        {
            _localClient = localClient;
            _centralClient = centralClient;
            _messaging = messaging;
        }


        public CancelCCResponse CancelCC(CancelCCRequest request)
        {
            var upcMeasurement = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            upcMeasurement.BeginTrack();

            switch (ApiMode)
            {
                case UpcApiMode.Local:
                    var localResponse = _localClient.CancelCC(request);
                    upcMeasurement.Tags["mode"] = "local";
                    upcMeasurement.EndTrack(Enum.UPCMeasurementForLocalWrapper.CancelCC, localResponse.IsSuccess);
                    return localResponse;
                case UpcApiMode.Central:
                    var centralResponse = _centralClient.CancelCC(request);
                    upcMeasurement.Tags["mode"] = "central";
                    upcMeasurement.EndTrack(Enum.UPCMeasurementForLocalWrapper.CancelCC, centralResponse.IsSuccess);
                    return centralResponse;
                default:
                    throw new ArgumentOutOfRangeException("UPC Mode not defined" + ApiMode);
            }
        }

        public GetSuRulesResponse GetSuRules(GetSuRulesRequest request)
        {
            var upcMeasurement = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            upcMeasurement.BeginTrack();
            switch (ApiMode)
            {
                case UpcApiMode.Local:
                    var localResponse = _localClient.GetSuRules(request);
                    upcMeasurement.Tags["mode"] = "local";
                    upcMeasurement.EndTrack(Enum.UPCMeasurementForLocalWrapper.GetSuRules);
                    return localResponse;
                case UpcApiMode.Central:
                    var centralResponse = _centralClient.GetSuRules(request);
                    upcMeasurement.Tags["mode"] = "central";
                    upcMeasurement.EndTrack(Enum.UPCMeasurementForLocalWrapper.GetSuRules,
                        centralResponse.Status == ConstantEnum.Status.Okay);
                    return centralResponse;
                default:
                    throw new ArgumentOutOfRangeException("UPC Mode not defined" + ApiMode);
            }
        }

        public RequestCCResponse RequestCC(RequestCCRequest request)
        {
            var upcMeasurement = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            upcMeasurement.BeginTrack();
            switch (ApiMode)
            {
                case UpcApiMode.Local:
                    var localResponse = _localClient.RequestCC(request);
                    upcMeasurement.Tags["mode"] = "local";
                    upcMeasurement.EndTrack(Enum.UPCMeasurementForLocalWrapper.RequestCC);
                    return localResponse;
                case UpcApiMode.Central:
                    var centralResponse = _centralClient.RequestCC(request);
                    upcMeasurement.Tags["mode"] = "central";
                    upcMeasurement.EndTrack(Enum.UPCMeasurementForLocalWrapper.RequestCC,
                        centralResponse.Status == ConstantEnum.Status.Okay);
                    return centralResponse;
                default:
                    throw new ArgumentOutOfRangeException("UPC Mode not defined" + ApiMode);
            }
        }        
        
        public RequestCCResponse RequestCC(RequestCCRequest request, Enums.ProductType productType)
        {
            var upcMeasurement = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            upcMeasurement.BeginTrack();
            switch (ApiMode)
            {
                case UpcApiMode.Local:
                    var localResponse = _localClient.RequestCC(request, productType);
                    upcMeasurement.Tags["mode"] = "local";
                    upcMeasurement.EndTrack(Enum.UPCMeasurementForLocalWrapper.RequestCC);
                    return localResponse;
                case UpcApiMode.Central:
                    var centralResponse = _centralClient.RequestCC(request, productType);
                    upcMeasurement.Tags["mode"] = "central";
                    upcMeasurement.EndTrack(Enum.UPCMeasurementForLocalWrapper.RequestCC,
                        centralResponse.Status == ConstantEnum.Status.Okay);
                    return centralResponse;
                default:
                    throw new ArgumentOutOfRangeException("UPC Mode not defined" + ApiMode);
            }
        }


        public RetrieveCCResponse RetrieveCC(RetrieveCCRequest request)
        {
            throw new NotImplementedException("PCI Data retrieval not allowed.");
        }

        public UpdateCCResponse UpdateCC(UpdateCCRequest request)
        {
            switch (ApiMode)
            {
                case UpcApiMode.Local:
                    throw new NotImplementedException("UpdateCC function not supported on Local UPC Client..");
                case UpcApiMode.Central:
                    throw new NotImplementedException("UpdateCC function not supported on Central UPC Client..");
                default:
                    throw new ArgumentOutOfRangeException("UPC Mode not defined" + ApiMode);
            }
           
        }

        public CheckCCResponse CheckCC(CheckCCRequest request)
        {
            switch (ApiMode)
            {
                case UpcApiMode.Local:
                    throw new NotImplementedException("CheckCC function not supported on Local UPC Client..");
                case UpcApiMode.Central:
                    throw new NotImplementedException("CheckCC function not supported on Central UPC Client..");
                default:
                    throw new ArgumentOutOfRangeException("UPC Mode not defined" + ApiMode);
            }

        }

        public ReplaceCCResponse ReplaceCC(ReplaceCCRequest request)
        {
            switch (ApiMode)
            {
                case UpcApiMode.Local:
                    throw new NotImplementedException("ReplaceCC function not supported on Local UPC Client..");
                case UpcApiMode.Central:
                    throw new NotImplementedException("ReplaceCC function not supported on Central UPC Client..");
                default:
                    throw new ArgumentOutOfRangeException("UPC Mode not defined" + ApiMode);
            }
        }
    }
}