using System.Collections.Generic;
using System.Linq;
using Agoda.EBE.Agents.Common.Util;

namespace Agoda.EBE.Agents.Common.Extension
{
//    public static class ListExtensions
//    {
//        /// <summary>
//        ///  Copies list items using the Copy method of individual item
//        ///  <see cref="ICopyable{T}"/> for more information.
//        /// </summary>
//        /// <param name="listToClone"></param>
//        /// <typeparam name="T"></typeparam>
//        /// <returns></returns>
//        public static List<T> Clone<T>(this IEnumerable<T> listToClone) where T : class, ICopyable<T>
//        {
//            return listToClone.Select(item => item.Clone()).ToList();
//        }
//    }
//    
//    /// <summary>
//    ///  Interface for adding logic for copying
//    /// </summary>
//    /// <typeparam name="T">Generic class type</typeparam>
//    public interface ICopyable<out T> where T : class
//    {
//        /// <summary>
//        ///  Method to copy an object. For specific details whether
//        ///  it's shallow or deep, see implementation.
//        /// </summary>
//        /// <returns></returns>
//        T Clone();
//    }
}