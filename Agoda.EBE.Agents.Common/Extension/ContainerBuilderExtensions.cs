using System;
using System.Linq;
using Agoda.EBE.Agents.Common.Configuration;
using Agoda.EBE.Agents.Common.Service.WhiteLabel;
using Agoda.WhiteLabelApi.Client;
using Autofac;
using IWhiteLabelExperimentManager = Agoda.WhiteLabelApi.Client.Services.IExperimentManager;

namespace Agoda.EBE.Agents.Common.Extension;

public static class ContainerBuilderExtensions
{
    public static void RegisterWhiteLabelServices(
        this ContainerBuilder builder,
        WhiteLabelClientConfiguration whiteLabelClientConfiguration,
        string applicationName,
        string adpApiKey = "ebe",
        int retryCount = 1)
    {
        // Extract the server settings from the configuration
        var serverSettings = whiteLabelClientConfiguration.ServerSettings.ToList();

        // Create the API configuration
        var whiteLabelApiConfig = new ApiBaseConfig
        {
            name = "WhiteLabelApiClient",
            deserializationSettings = null,
            serializationSettings = null,
            retryCount = retryCount,
            timeout = TimeSpan.FromMilliseconds(whiteLabelClientConfiguration.TimeoutMilliseconds),
            manualCalcUris = false,
            overrideHostHeader = true,
            settings = serverSettings
        };

        // Initialize the API and client instances
        var whiteLabelApi = new WhiteLabelAPIImpl(whiteLabelApiConfig);
        var whiteLabelClient = new WhiteLabelClient(whiteLabelApi, adpApiKey, applicationName);

        // Register the instances and types
        builder.RegisterInstance(whiteLabelClient).As<IWhiteLabelClient>();
        builder.RegisterType<WhiteLabelExperimentManager>().As<IWhiteLabelExperimentManager>();
        builder.RegisterType<WhiteLabelConfigService>().As<IWhiteLabelConfigService>();
        builder.RegisterType<WhiteLabelHelperService>().As<IWhiteLabelHelperService>();
    }
}