﻿using System;
using System.Linq;
using System.Text.RegularExpressions;
using System.Xml;
using System.Xml.Linq;

namespace Agoda.EBE.Agents.Common
{
    public static class StringExtension
    {
        public static string 
            RemoveAllAlphabet(this string str)
        {
            return string.IsNullOrEmpty(str) ? string.Empty : Regex.Replace(str, "[\\-*()+\\/,.a-zA-Z ]+", string.Empty);
        }

        public static string GetXmlNodeValue(this string xml, string parent, string node)
        {
            return XDocument.Parse(xml).Descendants(parent).Select(x => x.Element(node).Value).First();
        }
    }
}
