using System;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.DataAccess.Interface;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Model;
using Agoda.EBE.Agents.Common.Request;
using Agoda.EBE.Agents.Common.Util;
using Agoda.EBE.Framework.MQ.MessageType;
using Agoda.EBE.Framework.Object;

namespace Agoda.EBE.Agents.Common.Service
{
    public interface IDeduplicationService
    {
        PreProcessAgentResponse preProcess(IDefaultMessage message, String agent);
        void postProcess(IDefaultMessage message, String agent, int workflowActionResultId, String remarkMessage);
    }
    public class DeduplicationService: IDeduplicationService
    {
        private readonly IChangeStateService _changeStateService;
        private readonly IBookingActionUtils _bookingActionUtils;
        private readonly IBFDBDataAccess _bfdbDataAccess;
        private readonly IMessaging _messaging;
        public DeduplicationService(IChangeStateService changeStateService, IBookingActionUtils bookingActionUtils, IBFDBDataAccess bfdbDataAccess, IMessaging messaging)
        {
            _changeStateService = changeStateService;
            _bookingActionUtils = bookingActionUtils;
            _bfdbDataAccess = bfdbDataAccess;
            _messaging = messaging;
        }
        public PreProcessAgentResponse preProcess(IDefaultMessage message, String agent)
        {
            var bookingId = message.BookingId;
            var productBookingAction = _bookingActionUtils.GetBookingAction(message.BookingId, agent);
            var preProcessResponse = new PreProcessAgentResponse
                {
                    IsExistingAgentResultFound = false
                };
            if (productBookingAction == null)
            {
                var exceptionMessage = "product booking action not found for booking id " + bookingId;
                var exception = new BookingActionNotFound(exceptionMessage);
                _messaging.ExceptionMessage.Send(
                    exceptionMessage,
                    exception,
                    LogLevel.ERROR);
            }
            else
            {
                var agentResultMap = productBookingAction.State.AgentResultMap;
                var nodeIdentifier = message.NodeIdentifier;
                if (agentResultMap.TryGetValue(nodeIdentifier, out var existingResult))
                {
                    var request = new CommonChangeBookingStateRequest
                    {
                        ItineraryID = productBookingAction.ItineraryId,
                        BookingID = (int) productBookingAction.BookingId,
                        UserID = Guid.Empty,
                        WorkflowActionResultID = existingResult.Code,
                        WorkflowReasonID = 0,
                        Remark = existingResult.Message,
                        EmailID = Guid.Empty,
                        NodeIdentifier = nodeIdentifier,
                        OperationId = message.OperationId
                    };
                    var changeStateResponse = _changeStateService.ChangeBookingState(request);
                    preProcessResponse = new PreProcessAgentResponse
                    {
                        IsExistingAgentResultFound = true,
                        ChangeBookingStateResponse = changeStateResponse
                    };
                }
            }
            return preProcessResponse;
        }
        
        public void postProcess(IDefaultMessage message, String agent, int workflowActionResultId, String remarkMessage)
        {
            var bookingId = message.BookingId;
            var productBookingAction = _bookingActionUtils.GetBookingAction(message.BookingId, agent);
            if (productBookingAction == null)
            {
                var exceptionMessage = "product booking action not found for booking id " + bookingId;
                var exception = new BookingActionNotFound(exceptionMessage);
                _messaging.ExceptionMessage.Send(
                    exceptionMessage,
                    exception,
                    LogLevel.ERROR);
            }
            else
            {
                var agentResult = new Result
                {
                    Code = workflowActionResultId,
                    Message = remarkMessage,
                    Status = 0 //can be anything, only being used by Scala Workflow Agent
                };
                var productBookingActionState = productBookingAction.State;
            
                productBookingActionState.AgentResultMap.Add(message.NodeIdentifier, agentResult);

                _bfdbDataAccess.UpdateBookingActionState(productBookingAction.ActionId, productBookingActionState);
            }
        }
    }
}