﻿using Agoda.Adp.Messaging.Client;
using System;
using System.Collections.Generic;

namespace Agoda.EBE.Agents.Common.Service
{
    public static class PortProviderService
    {
        private const int DEFAULT_PORT = 5000;
        public static int GetPort(string[] args)
        {
            int port = DEFAULT_PORT;
            bool portFromCommandLineArgs = false;
            try
            {
                if (args.Length > 1)
                {
                    port = Convert.ToInt32(args[1]);
                    portFromCommandLineArgs = true;
                }
            }
            catch (Exception)
            {
            }

            LogMessage logMessage = new LogMessage(
                "EBE",
                LogLevel.INFO,
                $"{AgentNameProvider.GetAppNameFromAgentNames(args)} running on port: {port}")
            {
                StringTags = new Dictionary<string, string>()
            };

            logMessage.StringTags.Add("PortFromCommandLineArgs", portFromCommandLineArgs.ToString());
            logMessage.StringTags.Add("Agents", AgentNameProvider.GetAgentNamesCmdLineArg(args));

            logMessage.Send(Common.Enum.MessagingApiKey);

            return port;
        }
    }
}
