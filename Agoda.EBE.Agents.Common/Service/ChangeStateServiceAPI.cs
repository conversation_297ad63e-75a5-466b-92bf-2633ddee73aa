using Agoda.EBE.Agents.Common.Base;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Request;
using Agoda.EBE.Framework.ClientBase;
using Agoda.EBE.Workflow.Client;
using Agoda.EBE.Workflow.Structure;
using Agoda.EBE.Workflow.Structure.Request;

namespace Agoda.EBE.Agents.Common.Service
{
    public class ChangeStateServiceAPI : BaseProxy, IChangeStateService
    {
        private readonly IWorkflowServiceClient _client;
        private readonly IMessaging _messaging;

        public ChangeStateServiceAPI(IWorkflowServiceClient apiClient, IMessaging messaging)
        {
            _client = apiClient;
            _messaging = messaging;
        }

        protected override IMeasurementMessageFactory MeasurementMessageFactory => _messaging.MeasurementMessageFactory;
        protected override ExternalServiceName ApiName => ExternalServiceName.WorkflowApi;

        public ChangeBookingStateResponse ChangeBookingState(CommonChangeBookingStateRequest request)
        {
            var req = new ChangeBookingStateRequest
            {
                Remark = request.Remark,
                BookingID = request.BookingID,
                EmailID = request.EmailID,
                isCuscoEmailId = request.isCuscoEmailId,
                ItineraryID = request.ItineraryID,
                UserID = request.UserID,
                WorkflowReasonID = request.WorkflowReasonID,
                WorkflowActionResultID = request.WorkflowActionResultID
            };
            return MeasureCall(
                methodName: ExternalService.WorkflowApi.ChangeBookingState,
                methodFunc: () => _client.ChangeBookingState(req),
                defineSuccessFunc: response => response.Status == ConstantEnum.Status.Okay && response.IsSuccess
            );
        }

        public InsertBookingHistoryResponse InsertBookingHistory(InsertBookingHistoryRequest request)
        {
            return MeasureCall(
                methodName: ExternalService.WorkflowApi.InsertBookingHistory,
                methodFunc: () => _client.InsertBookingHistory(request),
                defineSuccessFunc: response => response.Status == ConstantEnum.Status.Okay && response.IsSuccess
            );
        }

        public void Dispose()
        {
            ;
        }
    }
}