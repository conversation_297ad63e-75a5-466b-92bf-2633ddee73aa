using Agoda.EBE.Agents.Common.DataAccess.Interface;
using Agoda.EBE.Framework.Encryption;
using Agoda.EBE.Framework.Objects;
using Newtonsoft.Json;

namespace Agoda.EBE.Agents.Common.Service
{
    public class LocalCreditCardService : ICreditCardService
    {
        private readonly IEncryptedDataAccess _encryptedDataAccess;

        public LocalCreditCardService(IEncryptedDataAccess encryptedDataAccess)
        {
            _encryptedDataAccess = encryptedDataAccess;
        }
        
        public CreditCardDetail GetEncryptedCreditCard(long itinerary)
        {
            var encryptedCreditCard = _encryptedDataAccess.GetEncryptedCreditCard(itinerary);
            var key = _encryptedDataAccess.GetEncryptionKeyFromId(encryptedCreditCard.KeyId);
            var decryptedString = RsaEncryption.Decrypt(encryptedCreditCard.CcEncrypted, key.EncryptedPrivateKey);
            return JsonConvert.DeserializeObject<CreditCardDetail>(decryptedString);
        }
    }
}