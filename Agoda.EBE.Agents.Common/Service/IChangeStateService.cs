using System;
using Agoda.EBE.Agents.Common.Request;
using Agoda.EBE.Workflow.Structure;
using Agoda.EBE.Workflow.Structure.Request;

namespace Agoda.EBE.Agents.Common.Service
{
    public interface IChangeStateService:IDisposable
    {
        ChangeBookingStateResponse ChangeBookingState(
            CommonChangeBookingStateRequest request);
        
        InsertBookingHistoryResponse InsertBookingHistory(
            InsertBookingHistoryRequest request);
    }
}