using Agoda.EBE.Agents.Common.Configuration.Interface;
using Agoda.EBE.Agents.Common.Request;
using Agoda.EBE.Framework.ClientBase;
using Agoda.EBE.Framework.MQ.Interface;
using Agoda.EBE.Framework.MQ.MessageType;
using Agoda.EBE.Workflow.Structure;
using Agoda.EBE.Workflow.Structure.Request;

namespace Agoda.EBE.Agents.Common.Service
{
    public class ChangeStateServiceQueue : IChangeStateService
    {
        #region Private Members

        private readonly IRabbitMQAdapter _rabbitMqAdapter;
        private readonly string _orchestratorQueue;
        private readonly string _historyQueue;
        private static readonly string sourceComponent = "NetCoreAgent";

        #endregion

        #region Construtctor

        public ChangeStateServiceQueue(IRabbitMQAdapter rabbitMqAdapter, IOrchestratorConfiguration configuration)
        {
            _rabbitMqAdapter = rabbitMqAdapter;
            _orchestratorQueue = configuration.OrchestratorQueue;
            _historyQueue = configuration.OrchestratorHistoryQueue;
        }

        #endregion

        #region Public Methods

        public ChangeBookingStateResponse ChangeBookingState(CommonChangeBookingStateRequest request)
        {
            var orchTask = FromChangeStateRequest(request);
            _rabbitMqAdapter.PublishMessage(orchTask, _orchestratorQueue, 0, 0);
            return new ChangeBookingStateResponse
            {
                Status = ConstantEnum.Status.Okay,
                IsSuccess = true
            };
        }

        public InsertBookingHistoryResponse InsertBookingHistory(InsertBookingHistoryRequest request)
        {
            var historyTask = FromBookingHistoryRequest(request);
            _rabbitMqAdapter.PublishMessage(historyTask, _historyQueue, 0, 0);
            return new InsertBookingHistoryResponse
            {
                Status = ConstantEnum.Status.Okay,
                IsSuccess = true
            };
        }

        #endregion

        #region Private Methods

        public static OrchestratorTask FromChangeStateRequest(CommonChangeBookingStateRequest request)
        {
            return new OrchestratorTask
            {
                ItineraryId = (int) request.ItineraryID,
                BookingId = request.BookingID,
                WorkflowId = request.WorkflowID.GetValueOrDefault(1),
                WorkflowActionResultId = request.WorkflowActionResultID,
                WorkflowReasonId = request.WorkflowReasonID,
                UserId = request.UserID.ToString(),
                Remark = request.Remark,
                NodeIdentifier = request.NodeIdentifier,
                SourceComponent = sourceComponent,
                IsPartialTransfer = request.IsPartialTransfer,
                IsSyncTransfer = request.IsSyncTransfer,
                OperationId = request.OperationId
            };
        }

        private OrchestratorTask FromBookingHistoryRequest(InsertBookingHistoryRequest request)
        {
            return new OrchestratorTask
            {
                ItineraryId = 0,
                BookingId = request.BookingId,
                WorkflowId = (int) Enum.WorkflowID.Hotel,
                WorkflowActionResultId = request.WorkflowActionResultId,
                WorkflowReasonId = request.WorkflowReasonId,
                UserId = request.UserId.ToString(),
                Remark = request.Remark
            };
        }

        #endregion

        public void Dispose()
        {
            _rabbitMqAdapter.Dispose();
        }
    }
}