﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Agoda.WhiteLabelApi.Client.Models;

namespace Agoda.EBE.Agents.Common.Service.WhiteLabel
{
    public interface IWhiteLabelConfigService
    {
        T GetFeatureConfigByKey<T>(string whiteLabelToken, string featureName, bool? isTestBooking, string origin = "", int deviceTypeId = 0);
        MPBE GetMPBEConfigByKey(string whiteLabelToken, int bookingId, bool? isTestBooking);
        string GetWhiteLabelKey(int whiteLabelId);
        bool IsFeatureEnabled(string featureName, Guid whiteLabelKey, bool? isTestBooking, int? whiteLabelId, string origin = "*", int deviceTypeId = 0);
        public bool DisableAgent(BookingWorkflow bookingWorkflowConfig, string agentName);
        bool IsDisabledAgentWithDmc(BookingWorkflow bookingWorkflowConfig, string agentName, int dmcId);
        bool IsBookingWorkflowByDmcEnableFlag(BookingWorkflow bookingWorkflowConfig, int dmcId, Enum.WhiteLabelBookingWorkflowByDmcFlag flag);
        bool IsRurubuWl(int whitelabelId, bool? isTestBooking);
        bool IsJapanicanWl(int whitelabelId, bool? isTestBooking);
        bool IsJtbWl(int whitelabelId, bool? isTestBooking);
        bool IsJtbDmcBooking(int whitelabelId, bool? isTestBooking, int dmcId);
        bool IsRurubuDomestic(int whitelabelId, bool? isTestBooking, int dmcId);
    }
}