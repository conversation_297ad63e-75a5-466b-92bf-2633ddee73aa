﻿using IWhiteLabelExperimentManager = Agoda.WhiteLabelApi.Client.Services.IExperimentManager;

namespace Agoda.EBE.Agents.Common.Service.WhiteLabel
{
    public class WhiteLabelExperimentManager : IWhiteLabelExperimentManager
    {
        public bool IsB(string experimentId)
        {
            return false;
        }

        public char DetermineVariant(string experimentId)
        {
            return 'A';
        }
    }
}