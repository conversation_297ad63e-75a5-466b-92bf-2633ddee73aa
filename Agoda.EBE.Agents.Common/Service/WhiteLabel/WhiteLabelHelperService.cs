using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Framework.Messaging;

namespace Agoda.EBE.Agents.Common.Service.WhiteLabel
{
    public class WhiteLabelHelperService : IWhiteLabelHelperService
    {
        private readonly IMessaging _messaging;

        public WhiteLabelHelperService(IMessaging messaging)
        {
            _messaging = messaging;
        }

        public int ResolveWhiteLabelId(int bookingId, int? whitelabelId = null)
        {
            _messaging.ExceptionMessage.Send(
                $"ResolveWhiteLabelId: bookingId: {bookingId}, whitelabelId: {whitelabelId}",
                null, LogLevel.DEBUG, new TagBuilder().AddBookingTag(bookingId).Build());

            return whitelabelId ?? (int)Enum.WhitelabelId.Agoda;
        }

        public string ResolveWhiteLabelToken(int? whitelabelId)
        {
            int whiteLabelId = whitelabelId ?? 0;
            string whiteLabelToken = Enum.GetWhiteLabelToken((Enum.WhitelabelId)whiteLabelId);
            return whiteLabelToken;
        }
}
}