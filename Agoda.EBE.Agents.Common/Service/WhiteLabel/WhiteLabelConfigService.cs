﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Agoda.Adp.Messaging.Client;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.EBE.Agents.Common.Object;
using Agoda.EBE.Agents.Common.Util;
using Agoda.EBE.Agents.Common.Util.Model;
using Agoda.EBE.Framework.Messaging;
using Agoda.WhiteLabelApi.Client;
using Agoda.WhiteLabelApi.Client.Models;
using IWhiteLabelExperimentManager = Agoda.WhiteLabelApi.Client.Services.IExperimentManager;

namespace Agoda.EBE.Agents.Common.Service.WhiteLabel
{
    public class WhiteLabelConfigService : IWhiteLabelConfigService
    {
        private readonly IWhiteLabelClient _whiteLabelClient;
        private readonly IWhiteLabelExperimentManager _whiteLabelExperimentManager;
        private readonly IMessaging _messaging;

        public WhiteLabelConfigService(
            IWhiteLabelClient whiteLabelClient,
            IWhiteLabelExperimentManager whiteLabelExperimentManager,
            IMessaging messaging
        )
        {
            _whiteLabelClient = whiteLabelClient;
            _whiteLabelExperimentManager = whiteLabelExperimentManager;
            _messaging = messaging;
        }

        public T GetFeatureConfigByKey<T>(string whiteLabelToken, string featureName, bool? isTestBooking,
            string origin = "", int deviceTypeId = 0)
        {
            var stat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            stat.BeginTrack();
            var isSuccess = true;
            try
            {
                return _whiteLabelClient.GetFeatureConfigByKey<T>(
                    featureName,
                    _whiteLabelExperimentManager,
                    whiteLabelToken,
                    origin,
                    deviceTypeId
                );
            }
            catch
            {
                isSuccess = false;
                _messaging.ExceptionMessage.Send(
                    $"WhiteLabelClient.GetFeatureConfigByKey returns Null Config: whiteLabelToken: {whiteLabelToken}, featureName: {featureName}",
                    null, LogLevel.INFO);
                return default;
            }
            finally
            {
                stat.EndTrack(Enum.WhitelabelClientMeasurement.GetFeatureConfigByKey, isSuccess);
            }
        }

        public MPBE GetMPBEConfigByKey(string whiteLabelToken, int bookingId, bool? isTestBooking)
        {
            var mpbeConfig = GetFeatureConfigByKey<MPBE>(whiteLabelToken, "mpbe", isTestBooking);
            if (mpbeConfig == null)
            {
                _messaging.ExceptionMessage.Send(
                    $@"MpbeConfig return null for BookingToken:{whiteLabelToken}",
                    null, LogLevel.DEBUG, new TagBuilder().AddBookingTag(bookingId).Build());
                return new MPBE();
            }

            return mpbeConfig;
        }

        public string GetWhiteLabelKey(int whiteLabelId)
        {
            var stat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            stat.BeginTrack();
            var isSuccess = true;
            try
            {
                return _whiteLabelClient.GetWhiteLabelKey(whiteLabelId);
            }
            catch (Exception ex)
            {
                isSuccess = false;
                _messaging.ExceptionMessage.Send(
                    $"WhiteLabelClient.GetWhiteLabelKey returns Null Key: whiteLabelId: {whiteLabelId}, Exception: {ex.Message}",
                    ex,
                    LogLevel.WARN);
                return default;
            }
            finally
            {
                stat.EndTrack(Enum.WhitelabelClientMeasurement.GetWhiteLabelKey, isSuccess);
            }
        }

        public bool IsFeatureEnabled(string featureName, Guid whiteLabelKey, bool? isTestBooking, int? whiteLabelId,
            string origin = "*", int deviceTypeId = 0)
        {
            var stat = _messaging.MeasurementMessageFactory.CreateNewMeasurement();
            stat.BeginTrack();
            var isSuccess = true;
            try
            {
                return _whiteLabelClient.IsFeatureEnabled(featureName, whiteLabelKey, origin,
                    deviceTypeId, _whiteLabelExperimentManager);
            }
            catch (Exception ex)
            {
                isSuccess = false;
                _messaging.ExceptionMessage.Send(
                    $"IsFeatureEnabled Fail for FeatureName: {featureName}",
                    ex,
                    LogLevel.WARN);
                throw ex;
            }
            finally
            {
                stat.EndTrack(Enum.WhitelabelClientMeasurement.IsFeatureEnabled, isSuccess);
            }
        }

        public bool DisableAgent(BookingWorkflow bookingWorkflowConfig, string agentName)
        {
            return bookingWorkflowConfig?.DisabledAgents?.Contains(agentName) ?? false;
        }

        public bool IsDisabledAgentWithDmc(BookingWorkflow bookingWorkflowConfig, string agentName, int dmcId)
        {
            var defaultDisableAgent = bookingWorkflowConfig?.DisabledAgents?.Contains(agentName) ?? false;
            var defaultDmcControlSetting =
                bookingWorkflowConfig?.DmcControlSettings?.FirstOrDefault(settings => settings.DmcId == null);
            var matchDmcControlSetting =
                bookingWorkflowConfig?.DmcControlSettings?.FirstOrDefault(settings => settings.DmcId == dmcId);
            var isDisabledAgentInMatchedDmc =
                matchDmcControlSetting?.DisabledAgents?.Any(disableAgents => disableAgents.Contains(agentName));
            var isDisabledAgentInDefaultDmc =
                defaultDmcControlSetting?.DisabledAgents?.Any(disableAgents => disableAgents.Contains(agentName));

            if (bookingWorkflowConfig?.DmcControlSettings == null)
            {
                return defaultDisableAgent;
            }
            else if (matchDmcControlSetting != null)
            {
                return isDisabledAgentInMatchedDmc ?? false;
            }
            else if (defaultDmcControlSetting != null)
            {
                return isDisabledAgentInDefaultDmc ?? false;
            }
            else
            {
                return defaultDisableAgent;
            }
        }

        public bool IsBookingWorkflowByDmcEnableFlag(BookingWorkflow bookingWorkflowConfig, int dmcId,
            Enum.WhiteLabelBookingWorkflowByDmcFlag flag)
        {
            var defaultDmcControlSetting =
                bookingWorkflowConfig?.DmcControlSettings?.FirstOrDefault(settings => settings.DmcId == null);
            var matchDmcControlSetting =
                bookingWorkflowConfig?.DmcControlSettings?.FirstOrDefault(settings => settings.DmcId == dmcId);

            if (matchDmcControlSetting != null)
            {
                return GetBookingWorkflowEnableFlag(matchDmcControlSetting, flag);
            }
            else if (defaultDmcControlSetting != null)
            {
                return GetBookingWorkflowEnableFlag(defaultDmcControlSetting, flag);
            }
            else
            {
                return false;
            }
        }

        private bool GetBookingWorkflowEnableFlag(DmcControlSettingModel dmcSetting,
            Enum.WhiteLabelBookingWorkflowByDmcFlag flag)
        {
            switch (flag)
            {
                case Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSupportSupplierMember:
                    return dmcSetting.IsSupportSupplierMember ?? false;
                case Enum.WhiteLabelBookingWorkflowByDmcFlag.IsSimulateCancelSuccessAndAcknowledge:
                    return dmcSetting.IsSimulateCancelSuccessAndAcknowledge ?? false;
                case Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessBookingSupplierData:
                    return dmcSetting.IsProcessBookingSupplierData ?? false;
                case Enum.WhiteLabelBookingWorkflowByDmcFlag.IsProcessSupplierPaymentGatewayData:
                    return dmcSetting.IsProcessSupplierPaymentGatewayData ?? false;
                case Enum.WhiteLabelBookingWorkflowByDmcFlag.IsJtbDmcBooking:
                    return dmcSetting.IsJtbDmcBooking ?? false;
                default:
                    return false;
            } 
        }
        public bool IsRurubuDomestic(int whitelabelId, bool? isTestBooking, int dmcId)
        {
            if (!IsRurubuWl(whitelabelId, isTestBooking)) return false;
            var wlToken = GetWhiteLabelKey(whitelabelId);
            var bookingWorkflowConfig = GetFeatureConfigByKey<BookingWorkflow>(wlToken, "bookingWorkflow", isTestBooking);
            return IsBookingWorkflowByDmcEnableFlag(bookingWorkflowConfig, dmcId, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsJtbDmcBooking);
        }
        public bool IsJtbDmcBooking(int whitelabelId, bool? isTestBooking, int dmcId)
        {
            var whitelabelToken = GetWhiteLabelKey(whitelabelId);
            var bookingWorkflowConfig = GetFeatureConfigByKey<BookingWorkflow>(whitelabelToken, "bookingWorkflow", isTestBooking);
            return IsBookingWorkflowByDmcEnableFlag(bookingWorkflowConfig, dmcId, Enum.WhiteLabelBookingWorkflowByDmcFlag.IsJtbDmcBooking);
        }
        private bool IsGivenWl(int whitelabelId, string featureName, bool? isTestBooking)
        {
            var whitelabelToken = GetWhiteLabelKey(whitelabelId);
            return IsFeatureEnabled(featureName, Guid.Parse(whitelabelToken), isTestBooking, whitelabelId);
        }
        public bool IsJapanicanWl(int whitelabelId, bool? isTestBooking)
        {
            return IsGivenWl(whitelabelId, WhiteLabelFeatureName.IsJapanicanWl, isTestBooking);
        }
        public bool IsJtbWl(int whitelabelId, bool? isTestBooking)
        {
            return IsGivenWl(whitelabelId, WhiteLabelFeatureName.IsJtbWl, isTestBooking);
        }
        
        public bool IsRurubuWl(int whitelabelId, bool? isTestBooking)
        {
            return IsGivenWl(whitelabelId, WhiteLabelFeatureName.IsRurubuWl, isTestBooking);
        }
    }
}