﻿using Agoda.EBE.Agents.Common.Util;
using System;
using System.Linq;

namespace Agoda.EBE.Agents.Common.Service
{
    public static class AgentNameProvider
    {
        public static string GetAgentNamesCmdLineArg(string[] args)
        {
            if (args != null && args.Any())
            {
                return args[0] ?? string.Empty;
            }

            return string.Empty;
        }

        public static string GetAgentNamesCmdLineArg(ICommandLineInterface cli)
        {
            string[] args = cli.GetCommandLineArguments();
            if (args != null && args.Length > 1)
            {
                return args[1] ?? string.Empty;
            }

            return string.Empty;
        }

        public static string[] GetSplitAgentNames(ICommandLineInterface cli)
        {
            string agentNames = GetAgentNamesCmdLineArg(cli);

            if (string.IsNullOrWhiteSpace(agentNames))
            {
                return new string[] { };
            }

            return agentNames.Split(new[] { '_' }, StringSplitOptions.RemoveEmptyEntries)
                             ?.Where(agentName => !string.IsNullOrWhiteSpace(agentName))
                             ?.Select(agentName => agentName.Trim()).ToArray();
        }

        public static string GetAppNameFromAgentNames(ICommandLineInterface cli)
        {
            string agentNames = GetAgentNamesCmdLineArg(cli);
            return GetAppNameFromAgentNames(agentNames);
        }

        public static string GetAppNameFromAgentNames(string[] args)
        {
            string agentNames = GetAgentNamesCmdLineArg(args);
            return GetAppNameFromAgentNames(agentNames);
        }

        private static string GetAppNameFromAgentNames(string agentNames)
        {
            string appName = "ebe";
            if (!string.IsNullOrWhiteSpace(agentNames))
            {
                agentNames = $"_{agentNames}";
                appName = $"{appName}{agentNames}";
            }

            return $"{appName}_agent";
        }
    }
}
