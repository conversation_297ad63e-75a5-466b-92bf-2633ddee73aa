﻿using System;
using System.Collections.Generic;
using System.Text;
using Agoda.EBE.Agents.Common.Interface;
using Agoda.EBE.Agents.Common.Validator.Interface;
using Agoda.EBE.Framework.MQ.MessageType;

namespace Agoda.EBE.Agents.Common.Validator
{
    public class UpcValidator : BaseValidator, IValidator
    {
        private IUpcValidationInfo upcValidationInfo;

        public UpcValidator(IUpcValidationInfo validationInfo) : base(validationInfo)
        {
            upcValidationInfo = validationInfo;
        }

        protected override void ValidateOtherThings(IDefaultMessage message, List<ValidateErrorItem> responseList)
        {
            if (upcValidationInfo.IsFinancialBreakdownProcessing)
            {
                responseList.Add(new ValidateErrorItem
                {
                    Result = ValidateBookingResult.ReProcess,
                    Remark = $"Financial breakdown is converting"
                });
            }
        }
    }
}
