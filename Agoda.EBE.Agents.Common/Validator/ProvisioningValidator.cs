﻿using System;
using System.Collections.Generic;
using System.Text;
using Agoda.EBE.Agents.Common.Interface;
using Agoda.EBE.Agents.Common.Validator.Interface;
using Agoda.EBE.Framework.MQ.MessageType;

namespace Agoda.EBE.Agents.Common.Validator
{
    public class ProvisioningValidator : BaseValidator, IValidator
    {
        private IProvisioningValidationInfo provisioningValidationInfo;

        public ProvisioningValidator(IProvisioningValidationInfo validationInfo) : base(validationInfo)
        {
            provisioningValidationInfo = validationInfo;
        }
    }
}
