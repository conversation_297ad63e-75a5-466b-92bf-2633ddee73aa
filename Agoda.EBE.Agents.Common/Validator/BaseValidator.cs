﻿using Agoda.EBE.Agents.Common.DataAccess.Interface;
using Agoda.EBE.Agents.Common.DataAccess;
using Agoda.EBE.Agents.Common.Interface;
using System;
using System.Collections.Generic;
using Agoda.EBE.Agents.Common.Validator;
using Agoda.EBE.Agents.Common.Validator.Interface;
using Agoda.EBE.Framework.MQ.MessageType;
using System.Linq;

namespace Agoda.EBE.Agents.Common
{
    public class BaseValidator : IValidator
    {
        protected IBaseValidationInfo validationInfo;

        public BaseValidator(IBaseValidationInfo validationInfo)
        {
            this.validationInfo = validationInfo ?? throw new ArgumentNullException(nameof(validationInfo));
        }

        public ValidateResponse ValidateBooking(IDefaultMessage message)
        {
            var response = new ValidateResponse();
            response.ErrorItemList = new List<ValidateErrorItem>();

            if (validationInfo.WorkflowStateId != message.WorkflowStateId)
            {
                response.ErrorItemList.Add(new ValidateErrorItem
                {
                    Result = ValidateBookingResult.Reject,
                    Remark = $"Workflow state ID in message is {message.WorkflowStateId}, in DB is {validationInfo.WorkflowStateId} which are not matched"
                });
            }

            ValidateOtherThings(message, response.ErrorItemList);
            SummarizeResult(response);

            return response;
        }

        public static void SummarizeResult(ValidateResponse response)
        {
            if (response.ErrorItemList.Count > 0)
            {
                response.SummaryResult = response.ErrorItemList.Max(x => x.Result);
            }

            if (response.SummaryResult == 0)
            {
                response.SummaryResult = ValidateBookingResult.Ok;
            }
        }

        /// <summary>
        /// Overwrite to validate other things specifically for each agent
        /// </summary>
        /// <param name="message"></param>
        /// <param name="responseList"></param>
        protected virtual void ValidateOtherThings(IDefaultMessage message, List<ValidateErrorItem> responseList)
        {
            // Do nothing in base.
        }
    }
}
