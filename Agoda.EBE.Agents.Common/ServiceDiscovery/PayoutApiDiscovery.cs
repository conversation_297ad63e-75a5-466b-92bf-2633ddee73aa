using System.Collections.Generic;
using Agoda.Adp.Messaging.Client;
using Agoda.Config.Consul.ServiceDiscovery.Models;
using Agoda.EBE.Agents.Common.Messaging;
using Agoda.Upc.Structure;

namespace Agoda.EBE.Agents.Common.ServiceDiscovery
{
    public class PayoutApiDiscovery : ServiceDiscoveryHealth
    {
        public override string ServiceName() => "payout-api";

        public override List<Config.Consul.Abstractions.Service> DefaultDevelopmentValue()
        {
            return new List<Config.Consul.Abstractions.Service>();
        }

        public List<string> GetService(IMessaging messaging)
        {
            var payoutService = new List<string>();
            var services = GetValue();
            messaging.ExceptionMessage.Send($"[PayoutApiDiscovery] GetService found services : {Newtonsoft.Json.JsonConvert.SerializeObject(services)}",
                null,
                LogLevel.DEBUG);
            if (services != null)
            {
                services.ForEach(s => payoutService.Add("https://" + s.ServiceAddress + ":" + s.ServicePort));
            }
            return payoutService;
        }
    }
}