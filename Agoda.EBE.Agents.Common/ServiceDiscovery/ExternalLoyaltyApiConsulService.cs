using System.Collections.Generic;
using System.Linq;
using Agoda.Config.Consul.Abstractions;
using Agoda.Config.Consul.ServiceDiscovery.Models;
using Models = Agoda.Config.Consul.ServiceDiscovery.Models;

namespace Agoda.EBE.Agents.Common.ServiceDiscovery
{
    public class ExternalLoyaltyApiConsulService : ServiceDiscoveryHealth
    {
        private const string PROTOCOL = "http";
        public override string ServiceName() => "whitelabel_loyalty_api";
        private static readonly IEnumerable<string> _serviceTags = new[] { "whitelabel_loyalty_api" };

        public override ServiceTags ServiceTags() => new ServiceTags(_serviceTags.ToArray());

        public override List<Config.Consul.Abstractions.Service> DefaultDevelopmentValue()
        {
            return new List<Config.Consul.Abstractions.Service>();
        }

        public IEnumerable<string> GetEndpoints()
        {
            foreach (var service in GetValue())
            {
                yield return GetEndpoint(service);
            }
        }

        public static string GetEndpoint(Config.Consul.Abstractions.Service service)
        {
            return $"{PROTOCOL}://{service.Address}:{service.ServicePort}";
        }
    }
}