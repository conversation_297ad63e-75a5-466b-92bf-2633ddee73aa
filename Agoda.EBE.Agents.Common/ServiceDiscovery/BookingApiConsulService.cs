using System.Collections.Generic;
using System.Linq;
using Agoda.Config.Consul.Abstractions;
using Agoda.Config.Consul.ServiceDiscovery.Models;

namespace Agoda.EBE.Agents.Common.ServiceDiscovery
{
    public class BookingApiConsulService: ServiceDiscoveryHealth
    {
        private const string PROTOCOL = "https";
        public override string ServiceName() => "booking_api";
        private static readonly IEnumerable<string> _serviceTags = new[] {"non_pci"};
        
        public override ServiceTags ServiceTags() => new ServiceTags(_serviceTags.ToArray());
        public override List<Config.Consul.Abstractions.Service> DefaultDevelopmentValue()
        {
            return new List<Config.Consul.Abstractions.Service>();
        }
        
        public IEnumerable<string> GetEndpoints()
        {
            var services = GetValue();
            return services.Select(GetEndpoint)?.Distinct();
        }
        public static string GetEndpoint(Config.Consul.Abstractions.Service service)
        {
            return $"{PROTOCOL}://{service.ServiceAddress}:{service.ServicePort}";
        }

    }
}